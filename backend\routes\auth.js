const express = require('express');
const passport = require('passport');
const router = express.Router();

// Start Google OAuth login
router.get('/google', passport.authenticate('google', {
  scope: ['profile', 'email'],
  prompt: 'select_account'
}));

// Google OAuth callback
router.get('/google/callback',
  passport.authenticate('google', {
    failureRedirect: '/',
    session: true,
  }),
  (req, res) => {
    // Log authentication success
    console.log('[Auth Debug] Google auth successful, user:', req.user?.id);
    console.log('[Auth Debug] Session ID after auth:', req.session?.id);

    // Add a timestamp to the session to ensure it's modified
    req.session.loginTime = Date.now();

    // Force touch the session
    req.session.touch();

    // Save the session explicitly
    req.session.save((err) => {
      if (err) {
        console.error('[Auth Debug] Error saving session:', err);
      }

      // Set a cookie directly to ensure it's set
      res.cookie('isLoggedIn', 'true', {
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
        httpOnly: false, // Allow JavaScript access
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production' // Use secure cookies in production
      });

      // Redirect to prototypes page after successful login
      console.log('[Auth Debug] Redirecting to prototypes page after login');
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

      // Ensure we don't have double slashes in the URL
      const redirectUrl = frontendUrl.endsWith('/')
        ? `${frontendUrl}prototypes`
        : `${frontendUrl}/prototypes`;

      console.log('[Auth Debug] Redirect URL:', redirectUrl);
      res.redirect(redirectUrl);
    });
  }
);

// Logout
router.get('/logout', (req, res) => {
  console.log('[Auth Debug] Logging out user:', req.user?.id);

  // Set CORS headers directly to ensure cross-origin requests work
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept');
  res.header('Access-Control-Expose-Headers', 'Set-Cookie, X-Auth-Status');

  req.logout((err) => {
    if (err) {
      console.error('[Auth Debug] Error during logout:', err);
      return res.status(500).json({ error: 'Logout failed' });
    }

    // Destroy the session
    req.session.destroy((err) => {
      if (err) {
        console.error('[Auth Debug] Error destroying session:', err);
      }

      // Clear all cookies with the same settings they were set with
      res.clearCookie('connect.sid', {
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true
      });

      res.clearCookie('isLoggedIn', {
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production',
        httpOnly: false
      });

      // Also set an expired cookie to ensure it's cleared
      res.cookie('isLoggedIn', 'false', {
        expires: new Date(0),
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production'
      });

      console.log('[Auth Debug] User logged out successfully');

      // Add a small delay to ensure cookies are properly cleared before redirect
      setTimeout(() => {
        const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

        // Remove any trailing slash to ensure consistent URLs
        const cleanUrl = frontendUrl.endsWith('/')
          ? frontendUrl.slice(0, -1)
          : frontendUrl;

        console.log('[Auth Debug] Logout redirect URL:', cleanUrl);
        res.redirect(cleanUrl);
      }, 100);
    });
  });
});

// API version of logout that returns JSON instead of redirecting
router.post('/logout', (req, res) => {
  console.log('[Auth Debug] API Logout called for user:', req.user?.id);

  // Set CORS headers directly to ensure cross-origin requests work
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept');
  res.header('Access-Control-Expose-Headers', 'Set-Cookie, X-Auth-Status');

  req.logout((err) => {
    if (err) {
      console.error('[Auth Debug] Error during API logout:', err);
      return res.status(500).json({ success: false, error: 'Logout failed' });
    }

    // Destroy the session
    req.session.destroy((err) => {
      if (err) {
        console.error('[Auth Debug] Error destroying session during API logout:', err);
      }

      // Clear all cookies with the same settings they were set with
      res.clearCookie('connect.sid', {
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true
      });

      res.clearCookie('isLoggedIn', {
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production',
        httpOnly: false
      });

      // Also set an expired cookie to ensure it's cleared
      res.cookie('isLoggedIn', 'false', {
        expires: new Date(0),
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production'
      });

      console.log('[Auth Debug] API Logout successful');
      res.json({ success: true, message: 'Logged out successfully' });
    });
  });
});

// Handle OPTIONS requests for CORS preflight
router.options('/me', (_req, res) => {
  console.log('[Auth Debug] /me OPTIONS request received');
  res.status(204).end();
});

// Handle OPTIONS requests for logout
router.options('/logout', (req, res) => {
  console.log('[Auth Debug] /logout OPTIONS request received');
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept');
  res.status(204).end();
});

// Get current user
router.get('/me', (req, res) => {
  console.log('[Auth Debug] /me endpoint called');
  console.log('[Auth Debug] Session ID:', req.session?.id);
  console.log('[Auth Debug] isAuthenticated:', req.isAuthenticated?.());
  console.log('[Auth Debug] req.user:', req.user);
  console.log('[Auth Debug] Cookies:', req.headers.cookie);
  console.log('[Auth Debug] Origin:', req.headers.origin);
  console.log('[Auth Debug] Referer:', req.headers.referer);

  // Set CORS headers directly
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept');
  res.header('Access-Control-Expose-Headers', 'Set-Cookie, X-Auth-Status');

  // Force save the session to ensure it's stored
  req.session.touch();
  req.session.save((err) => {
    if (err) {
      console.error('[Auth Debug] Error saving session:', err);
    }

    if (req.user) {
      console.log('[Auth Debug] User authenticated:', req.user.id);

      // Set a custom header to indicate authentication status
      res.setHeader('X-Auth-Status', 'authenticated');

      // Set a cookie directly to ensure it's set
      res.cookie('isLoggedIn', 'true', {
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
        httpOnly: false, // Allow JavaScript access
        path: '/',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        secure: process.env.NODE_ENV === 'production' // Use secure cookies in production
      });

      // Return the user data
      res.json({
        user: req.user,
        sessionId: req.session.id,
        authenticated: true
      });
    } else {
      console.log('[Auth Debug] User not authenticated');
      res.status(401).json({ error: 'Not authenticated' });
    }
  });
});

module.exports = router;
