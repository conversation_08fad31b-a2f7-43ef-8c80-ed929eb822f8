# JustPrototype Project Status & Context

## 🎯 Current Status (Latest Session)

### ✅ **COMPLETED TASKS**

#### **LiteLLM Integration (FIXED)**
- **Backend Configuration**: LiteLLM proxy set as default provider in `getBestProvider()`
- **Railway Deployment**: LiteLLM hosted at https://litellm-production-744f.up.railway.app/
- **Cost Optimization**: 85% cost savings using DeepSeek models vs GPT-4o
- **Provider Architecture**: Frontend → Backend (auto-selects LiteLLM) → Railway LiteLLM → DeepSeek Models
- **Environment**: `USE_DIRECT_APIS=false`, `LITELLM_BASE_URL` configured

#### **Code Quality Improvements (FIXED)**
- **Externalized Prompts**: All prompts moved to `backend/config/prompts.js`
- **Removed Hardcoded Content**: Eliminated all fallback content and hardcoded sections
- **Clean Architecture**: Frontend never specifies providers, backend handles all provider logic
- **Dynamic Content**: All plan sections, features, accessibility generated by LLM

#### **Provider Selection Logic (FIXED)**
- **Default Priority**: LiteLLM proxy → DeepSeek direct → OpenRouter → OpenAI → Anthropic
- **Automatic Selection**: `getBestProvider()` always prefers LiteLLM when available
- **Debug Logging**: Added provider selection logging for troubleshooting

### 🚧 **IN PROGRESS**
- Testing the fixed LiteLLM integration with plan generation
- Verifying cost-effective DeepSeek model usage

### 📋 **NEXT PRIORITIES**
1. **Test Complete Flow**: Verify plan generation → code generation works end-to-end
2. **UI Polish**: Continue with editor-v3-refactored improvements
3. **Edit Functionality**: Test click-and-implement flow with new architecture

## 🚨 **CRITICAL MISTAKES TO NEVER REPEAT**

### **1. Provider Selection Errors**
- ❌ **NEVER hardcode providers in frontend** - Backend should always decide
- ❌ **NEVER create duplicate methods** - Had two `generateIntent` methods causing conflicts
- ❌ **NEVER hardcode fallback content** - All content must come from LLM dynamically
- ❌ **NEVER inline prompts in code** - Always externalize to config files

### **2. Architecture Violations**
- ❌ **NEVER mix provider concerns** - Frontend sends requests, backend handles providers
- ❌ **NEVER ignore user preferences** - User explicitly wants LiteLLM proxy, not direct APIs
- ❌ **NEVER break working systems** - If LiteLLM was working, don't switch to direct APIs

### **3. Code Quality Issues**
- ❌ **NEVER use hardcoded sections** - Plan sections must be dynamic from LLM
- ❌ **NEVER add generic fallback content** - Return errors instead of fake content
- ❌ **NEVER ignore separation of concerns** - Keep frontend clean of backend logic

## 🏗️ **Current Architecture**

### **LLM Service Flow**
```
Frontend Request (no provider)
    ↓
Backend getBestProvider()
    ↓
LiteLLM Proxy (Railway)
    ↓
DeepSeek Models (Cost-effective)
```

### **File Structure**
- **Prompts**: `backend/config/prompts.js` (externalized)
- **LLM Service**: `backend/services/llmServiceV3.js` (clean, no hardcoded content)
- **Frontend**: `ui/src/services/planService.ts` (no provider specification)
- **Environment**: `backend/.env` (LiteLLM proxy configuration)

### **Key Configuration**
```bash
# Backend .env
LITELLM_BASE_URL=https://litellm-production-744f.up.railway.app
LITELLM_API_KEY=sk-railway-litellm-2024
USE_DIRECT_APIS=false

# Railway LiteLLM Environment
DEEPSEEK_API_KEY=configured
OPENROUTER_API_KEY=configured
LITELLM_MASTER_KEY=sk-railway-litellm-2024
```

## 🎯 **Core Application Features**

### **Editor-v3-refactored (Primary Focus)**
- **Location**: `/editor-v3-refactored` - Core prototype generation screen
- **Functionality**: Two-step Readdy.ai flow (intent → implementation)
- **Status**: Working with new LiteLLM architecture

### **Plan Generation**
- **Endpoint**: `/api/llm/v3/plan` (structured), `/api/llm/v3/plan/stream` (streaming)
- **Models**: DeepSeek via LiteLLM for cost optimization
- **Content**: Fully dynamic from LLM (no hardcoded sections)

### **Code Generation**
- **Endpoint**: `/api/llm/v3/generate`
- **Models**: DeepSeek coder models via LiteLLM
- **Quality**: Production-grade HTML with embedded CSS/JS

## 💰 **Cost Optimization**
- **DeepSeek**: $0.14/$1.10 per 1M tokens (85% cheaper than GPT-4o)
- **LiteLLM Proxy**: Centralized model management and routing
- **Railway Hosting**: Reliable LiteLLM deployment

## 🔧 **Development Guidelines**
1. **Always use LiteLLM proxy** - Never switch to direct APIs without explicit user request
2. **Keep frontend clean** - No provider logic, no hardcoded content
3. **Externalize all prompts** - Use `backend/config/prompts.js`
4. **Dynamic content only** - No fallback or hardcoded sections
5. **Test end-to-end** - Verify full flow from plan → code generation

## 📞 **Support & Resources**
- **Repository**: https://github.com/sanjeev23oct/justprototype
- **LiteLLM Repo**: https://github.com/sanjeev23oct/litellm
- **Railway LiteLLM**: https://litellm-production-744f.up.railway.app/
- **Frontend**: http://localhost:5173/
- **Backend**: http://localhost:5000/
