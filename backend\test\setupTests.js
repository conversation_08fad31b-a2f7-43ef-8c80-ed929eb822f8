// Setup file for Jest tests
const { TextEncoder, TextDecoder } = require('util');

// Polyfill for TextEncoder and TextDecoder which are not available in the JSDOM environment
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock console methods to keep test output clean
const originalConsole = { ...console };

beforeEach(() => {
  // Mock console methods
  global.console = {
    ...originalConsole,
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  };
});

afterEach(() => {
  // Restore original console methods
  global.console = originalConsole;
});

// Mock any global objects needed for testing
global.Headers = class Headers {
  constructor(init) {
    this.headers = new Map();
    if (init) {
      Object.entries(init).forEach(([key, value]) => {
        this.headers.set(key.toLowerCase(), value);
      });
    }
  }
  
  get(key) {
    return this.headers.get(key.toLowerCase());
  }
  
  set(key, value) {
    this.headers.set(key.toLowerCase(), value);
  }
  
  has(key) {
    return this.headers.has(key.toLowerCase());
  }
  
  delete(key) {
    return this.headers.delete(key.toLowerCase());
  }
  
  forEach(callback) {
    return this.headers.forEach(callback);
  }
};

// Mock the uuid module
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('test-uuid')
}));
