const { jest } = require('@jest/globals');
const request = require('supertest');
const express = require('express');
const bodyParser = require('body-parser');

// Mock the LLM service
jest.mock('../../services/llmServiceV2');
const llmService = require('../../services/llmServiceV2');

// Import the AST store
const astStore = require('../../services/ast/astStore');

// Helper function to create a test app
function createTestApp() {
  const app = express();
  app.use(bodyParser.json());
  
  // Import routes
  const astRoutes = require('../../routes/astRoutes');
  app.use('/api/ast', astRoutes);
  
  return app;
}

// Mock the AST store methods
beforeEach(() => {
  jest.clearAllMocks();
  
  // Reset the AST store to initial state
  astStore.getCurrentSnapshot.mockReturnValue({
    snapshotId: 'v1',
    ast: { id: 'test-ast', type: 'Page', children: [] }
  });
  
  astStore.applyPatch.mockImplementation((snapshotId, operations) => {
    return {
      success: true,
      snapshotId: 'v2',
      ast: { id: 'patched-ast', type: 'Page', children: [{ type: 'div' }] }
    };
  });
  
  astStore.listSnapshots.mockReturnValue([
    { id: 'v1', timestamp: '2023-01-01' },
    { id: 'v2', timestamp: '2023-01-02' }
  ]);
  
  astStore.getSnapshot.mockImplementation((id) => {
    if (id === 'non-existent-id') return null;
    return { 
      id, 
      ast: { id: 'test-ast', type: 'Page' },
      timestamp: '2023-01-01'
    };
  });
  
  // Mock LLM service
  const mockStream = {
    on: jest.fn((event, callback) => {
      if (event === 'data') {
        callback({ type: 'status', data: 'Generating...' });
        callback({ 
          type: 'result', 
          data: { 
            ast: { 
              id: 'test-ast', 
              type: 'Page',
              children: []
            } 
          } 
        });
      } else if (event === 'end') {
        callback();
      }
      return mockStream;
    })
  };
  llmService.generatePlan.mockReturnValue(mockStream);
});

describe('AST API Integration Tests', () => {
  let app;
  let server;
  
  beforeAll(async () => {
    // Create a test app with the routes
    app = await createTestApp();
    server = app.listen(0); // Use a random port
  });
  
  afterAll((done) => {
    // Clean up
    server.close(done);
  });
  
  beforeEach(() => {
    // Reset mocks and clear the AST store before each test
    jest.clearAllMocks();
    // Reset the AST store to initial state
    Object.keys(astStore.history.versions).forEach(key => {
      if (key !== 'v1') {
        delete astStore.history.versions[key];
      }
    });
    astStore.history.currentVersion = 'v1';
    astStore.currentAST = astStore.createEmptyPage();
  });
  
  describe('GET /api/ast/current', () => {
    it('should return the current AST', async () => {
      const response = await request(app)
        .get('/api/ast/current')
        .expect('Content-Type', /json/)
        .expect(200);
      
      expect(response.body).toHaveProperty('snapshotId');
      expect(response.body).toHaveProperty('ast');
      expect(response.body.ast.type).toBe('Page');
    });
  });
  
  describe('POST /api/ast/plan', () => {
    it('should generate a plan and return SSE events', async () => {
      // Mock the LLM service to simulate streaming
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            // Simulate data events
            callback({ type: 'status', data: 'Generating...' });
            callback({ 
              type: 'result', 
              data: { 
                ast: { 
                  id: 'test-ast', 
                  type: 'Page',
                  children: []
                } 
              } 
            });
          } else if (event === 'end') {
            // Simulate end event
            callback();
          }
          return mockStream;
        })
      };
      llmService.generatePlan.mockReturnValue(mockStream);
      
      const response = await request(app)
        .post('/api/ast/plan')
        .set('Accept', 'text/event-stream')
        .send({ prompt: 'Test prompt' })
        .expect('Content-Type', /text\/event-stream/)
        .expect(200);
      
      // Note: In a real test, you would need to parse the SSE stream
      // This is a simplified check
      expect(response.text).toContain('event: start');
      
      // Verify the LLM service was called
      expect(llmService.generatePlan).toHaveBeenCalledWith(
        'Test prompt',
        'openai',
        expect.any(String), // requestId
        expect.any(Function) // onToken callback
      );
    });
    
    it('should return 400 if prompt is missing', async () => {
      await request(app)
        .post('/api/ast/plan')
        .send({})
        .expect(400)
        .then(response => {
          expect(response.body.error).toBe('Prompt is required');
        });
    });
  });
  
  describe('POST /api/ast/patch', () => {
    it('should apply a patch to the AST', async () => {
      // First, get the current snapshot
      const currentResponse = await request(app)
        .get('/api/ast/current')
        .expect(200);
      
      const snapshotId = currentResponse.body.snapshotId;
      
      // Define a patch to add a new node
      const patch = {
        snapshotId,
        operations: [
          {
            op: 'add',
            path: '/children',
            value: [
              { 
                id: 'test-node', 
                type: 'div',
                props: { className: 'test-class' },
                children: []
              }
            ]
          }
        ]
      };
      
      // Apply the patch
      const response = await request(app)
        .post('/api/ast/patch')
        .send(patch)
        .expect(200);
      
      // Verify the response
      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('snapshotId');
      expect(response.body.snapshotId).not.toBe(snapshotId); // Should be a new snapshot
      expect(response.body.ast).toBeDefined();
      
      // Verify the AST was updated
      const updatedResponse = await request(app)
        .get('/api/ast/current')
        .expect(200);
      
      expect(updatedResponse.body.ast.children).toHaveLength(1);
      expect(updatedResponse.body.ast.children[0].id).toBe('test-node');
    });
    
    it('should return 400 for invalid patch', async () => {
      const invalidPatch = {
        snapshotId: 'invalid-snapshot',
        operations: [
          { op: 'invalid-op' } // Invalid operation
        ]
      };
      
      await request(app)
        .post('/api/ast/patch')
        .send(invalidPatch)
        .expect(400)
        .then(response => {
          expect(response.body.error).toBeDefined();
        });
    });
  });
  
  describe('GET /api/ast/snapshots', () => {
    it('should return a list of snapshots', async () => {
      // Create a few snapshots
      await request(app)
        .post('/api/ast/plan')
        .send({ prompt: 'Test 1' });
      
      await request(app)
        .post('/api/ast/plan')
        .send({ prompt: 'Test 2' });
      
      // Get the list of snapshots
      const response = await request(app)
        .get('/api/ast/snapshots')
        .expect(200);
      
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThanOrEqual(2);
      
      // Verify snapshot structure
      const snapshot = response.body[0];
      expect(snapshot).toHaveProperty('id');
      expect(snapshot).toHaveProperty('timestamp');
      expect(snapshot).toHaveProperty('ast');
    });
  });
  
  describe('GET /api/ast/snapshots/:id', () => {
    it('should return a specific snapshot', async () => {
      // Create a snapshot
      const createResponse = await request(app)
        .post('/api/ast/plan')
        .send({ prompt: 'Test snapshot' });
      
      const snapshotId = createResponse.body.snapshotId;
      
      // Get the snapshot by ID
      const response = await request(app)
        .get(`/api/ast/snapshots/${snapshotId}`)
        .expect(200);
      
      expect(response.body).toHaveProperty('id', snapshotId);
      expect(response.body).toHaveProperty('ast');
    });
    
    it('should return 404 for non-existent snapshot', async () => {
      await request(app)
        .get('/api/ast/snapshots/non-existent-id')
        .expect(404)
        .then(response => {
          expect(response.body.error).toBe('Snapshot not found');
        });
    });
  });
});
