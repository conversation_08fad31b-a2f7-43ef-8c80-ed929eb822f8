/**
 * SPA Shell Component
 * Main container for the modular SPA functionality
 */

import React, { useEffect, useRef, useState } from 'react';
import { Router } from '../modules/spa/core/Router';
import { PatchManager } from '../modules/spa/core/PatchManager';
import { ComponentRegistry } from '../modules/spa/registries/ComponentRegistry';
import { ActionRegistry } from '../modules/spa/registries/ActionRegistry';

// --- Helper functions from previous version (restored for TS) ---
function getDashboardContent(): string {
  return `
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <div class="px-4 py-6 sm:px-0">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Dashboard</h1>
        <div class="text-gray-500">No prototype loaded.</div>
      </div>
    </div>
  `;
}
function getAnalyticsContent(): string {
  return `<div class="p-8 text-gray-700">Analytics coming soon.</div>`;
}
function getSettingsContent(): string {
  return `<div class="p-8 text-gray-700">Settings coming soon.</div>`;
}
function setupEventListeners(container: HTMLElement, spaCore: any, setHighlightedEl: (el: HTMLElement | null) => void): void {
  // Navigation click delegation (disabled in edit mode)
  container.addEventListener('click', (e) => {
    if (spaCore.isEditMode) return;
    const target = e.target as HTMLElement;
    const navElement = target.closest('[data-nav]');
    if (navElement) {
      e.preventDefault();
      const viewName = navElement.getAttribute('data-nav');
      if (viewName) {
        spaCore.router.navigateToView(viewName);
      }
    }
  });

  // Action click delegation (disabled in edit mode)
  container.addEventListener('click', (e) => {
    if (spaCore.isEditMode) return;
    const target = e.target as HTMLElement;
    const actionElement = target.closest('[data-action]');
    if (actionElement) {
      e.preventDefault();
      const action = actionElement.getAttribute('data-action');
      const actionTarget = actionElement.getAttribute('data-target');
      const params = extractDataParams(actionElement as HTMLElement);

      if (action) {
        spaCore.actionRegistry.execute(action, actionTarget, params, actionElement);
      }
    }
  });

  // Edit mode toggle
  const editModeBtn = container.querySelector('#editModeBtn');
  if (editModeBtn) {
    editModeBtn.addEventListener('click', () => {
      toggleEditMode(spaCore);
    });
  }

  // Diff modal controls
  const closeDiffModal = container.querySelector('#closeDiffModal');
  const applyDiff = container.querySelector('#applyDiff');
  const rejectDiff = container.querySelector('#rejectDiff');

  if (closeDiffModal) {
    closeDiffModal.addEventListener('click', () => {
      spaCore.patchManager.hideDiffModal();
    });
  }

  if (applyDiff) {
    applyDiff.addEventListener('click', () => {
      spaCore.patchManager.applyPendingDiff();
    });
  }

  if (rejectDiff) {
    rejectDiff.addEventListener('click', () => {
      spaCore.patchManager.rejectPendingDiff();
    });
  }

  // Router callbacks for component reinitialization
  spaCore.router.onAfterNavigate((viewName: string) => {
    const viewContainer = container.querySelector('#viewContainer');
    if (viewContainer) {
      spaCore.componentRegistry.reinitialize(viewContainer as HTMLElement);
    }
  });
}
function extractDataParams(element: HTMLElement): any {
  const params: any = {};
  Array.from(element.attributes).forEach(attr => {
    if (attr.name.startsWith('data-') &&
        !['data-action', 'data-target', 'data-nav', 'data-view', 'data-component'].includes(attr.name)) {
      const key = attr.name.replace('data-', '').replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
      try {
        params[key] = JSON.parse(attr.value);
      } catch {
        params[key] = attr.value;
      }
    }
  });
  return params;
}
function toggleEditMode(spaCore: any): void {
  spaCore.isEditMode = !spaCore.isEditMode;
  const btn = document.getElementById('editModeBtn');
  if (btn) {
    if (spaCore.isEditMode) {
      btn.textContent = 'Exit Edit';
      btn.classList.add('bg-red-600', 'hover:bg-red-700');
      btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
      enableEditMode(spaCore);
    } else {
      btn.textContent = 'Edit Mode';
      btn.classList.remove('bg-red-600', 'hover:bg-red-700');
      btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
      disableEditMode();
    }
  }
  console.log(`${spaCore.isEditMode ? '✏️ Enabled' : '🔒 Disabled'} edit mode`);
}
function enableEditMode(spaCore: any): void {
  document.body.classList.add('edit-mode');
}
function disableEditMode(): void {
  document.body.classList.remove('edit-mode');
}

// --- END helpers ---

interface SPAShellProps {
  className?: string;
  enableEditMode?: boolean;
  dashboardHtml?: string;
  onElementClick?: (element: any) => void; // Connect to main editor's element handling
  viewMode?: 'preview' | 'code';
  onViewModeChange?: (mode: 'preview' | 'code') => void;
}

export const SPAShell: React.FC<SPAShellProps> = ({
  className = '',
  enableEditMode = false,
  dashboardHtml,
  onElementClick,
  viewMode = 'preview',
  onViewModeChange
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const spaCore = useRef<any>(null);
  const [highlightedEl, setHighlightedEl] = useState<HTMLElement | null>(null);

  // Debug: Log the dashboardHtml content
  useEffect(() => {
    console.log('🔍 SPAShell dashboardHtml:', {
      hasContent: !!dashboardHtml,
      length: dashboardHtml?.length || 0,
      preview: dashboardHtml?.substring(0, 100) || 'No content',
      viewMode,
      isEmptyString: dashboardHtml === '',
      isOnlyWhitespace: dashboardHtml?.trim() === ''
    });
  }, [dashboardHtml, viewMode]);

  useEffect(() => {
    if (!containerRef.current) return;

    // Initialize SPA Core System
    const componentRegistry = new ComponentRegistry();
    const actionRegistry = new ActionRegistry();
    const router = new Router();
    const patchManager = new PatchManager();

    // Store in ref for cleanup
    spaCore.current = {
      componentRegistry,
      actionRegistry,
      router,
      patchManager,
      isEditMode: enableEditMode
    };

    // Make globally available for compatibility
    (window as any).spaCore = spaCore.current;

    // Expose test methods for debugging
    (window as any).testPatchManager = () => {
      spaCore.current.patchManager.testPatchApplication();
    };

    // Expose simulation method for testing with exact API data
    (window as any).simulateApiResponse = () => {
      spaCore.current.patchManager.simulateApiResponse();
    };

    // Initialize default views
    initializeDefaultViews(router, dashboardHtml);

    // Setup event listeners
    setupEventListeners(containerRef.current, spaCore.current, setHighlightedEl);

    // Navigate to default view
    router.navigateToView('dashboard');

    console.log('🚀 SPA Shell initialized');

    // Cleanup
    return () => {
      componentRegistry.clear();
      actionRegistry.clear();
      delete (window as any).spaCore;
      document.removeEventListener('click', globalEditHandler, true);
      console.log('🧹 SPA Shell cleaned up');
    };
  }, [enableEditMode, dashboardHtml]);

  // Sync edit mode state when prop changes
  useEffect(() => {
    if (spaCore.current) {
      spaCore.current.isEditMode = enableEditMode;
      console.log(`🔄 SPAShell edit mode synced: ${enableEditMode ? 'enabled' : 'disabled'}`);

      // Update button appearance to match state
      const btn = document.getElementById('editModeBtn');
      if (btn) {
        if (enableEditMode) {
          btn.textContent = 'Exit Edit';
          btn.classList.add('bg-red-600', 'hover:bg-red-700');
          btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
          enableEditMode && document.body.classList.add('edit-mode');
        } else {
          btn.textContent = 'Edit Mode';
          btn.classList.remove('bg-red-600', 'hover:bg-red-700');
          btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
          document.body.classList.remove('edit-mode');
        }
      }
    }
  }, [enableEditMode]);

  // Highlight selected element for editing
  useEffect(() => {
    document.querySelectorAll('[data-spa-edit-highlight]').forEach(el => {
      el.removeAttribute('data-spa-edit-highlight');
      (el as HTMLElement).style.outline = '';
      (el as HTMLElement).style.outlineOffset = '';
      (el as HTMLElement).style.zIndex = '';
      (el as HTMLElement).style.position = '';
    });

    if (!highlightedEl) return;
    highlightedEl.setAttribute('data-spa-edit-highlight', 'true');
    highlightedEl.style.outline = '3px solid #2563eb !important';
    highlightedEl.style.outlineOffset = '2px';
    highlightedEl.style.zIndex = '9999';
    highlightedEl.style.position = 'relative';

    return () => {
      highlightedEl.removeAttribute('data-spa-edit-highlight');
      highlightedEl.style.outline = '';
      highlightedEl.style.outlineOffset = '';
      highlightedEl.style.zIndex = '';
      highlightedEl.style.position = '';
    };
  }, [highlightedEl]);

  // --- GLOBAL EDIT HANDLER ---
  function globalEditHandler(e: MouseEvent) {
    const spaCore = (window as any).spaCore;
    console.log('🔥 globalEditHandler called:', {
      spaCore: !!spaCore,
      isEditMode: spaCore?.isEditMode,
      enableEditModeProp: enableEditMode,
      target: (e.target as HTMLElement)?.tagName
    });

    if (!spaCore?.isEditMode) {
      console.log('🔥 Edit mode not active, ignoring click');
      return;
    }

    // Ignore clicks inside modals, popups, or UI overlays
    const target = e.target as HTMLElement;
    const isInsideModal = target.closest('.fixed.inset-0') || // Modal overlay
                         target.closest('[role="dialog"]') || // Dialog elements
                         target.closest('.modal') || // Generic modal class
                         target.closest('.z-50'); // High z-index overlays

    if (isInsideModal) {
      console.log('🔥 Click inside modal/overlay, ignoring in edit mode');
      return;
    }

    e.preventDefault();
    e.stopPropagation();
    if (typeof e.stopImmediatePropagation === 'function') e.stopImmediatePropagation();
    const editable = target.closest('[data-editable], button, section, div') as HTMLElement | null;
    if (!editable) return;

    setHighlightedEl(editable);

    let selector = '';
    if (editable.id) {
      selector = `#${editable.id}`;
    } else if (editable.getAttribute('data-component')) {
      selector = `[data-component="${editable.getAttribute('data-component')}"]`;
    } else if (editable.className) {
      selector = `${editable.tagName.toLowerCase()}.${editable.className.split(' ').join('.')}`;
    } else {
      const parent = editable.parentElement;
      if (parent) {
        const children = Array.from(parent.children);
        const idx = children.indexOf(editable) + 1;
        selector = `${editable.tagName.toLowerCase()}:nth-child(${idx})`;
      } else {
        selector = editable.tagName.toLowerCase();
      }
    }

    console.log('🔥 SPAShell edit mode click:', {
      element: editable.tagName,
      text: editable.textContent?.trim(),
      selector: selector,
      outerHTML: editable.outerHTML.slice(0, 100)
    });

    // If onElementClick callback is provided, use main editor's system
    if (onElementClick) {
      console.log('🔥 Using main editor element handling system');

      // Prepare element data in the format expected by main editor
      const elementData = {
        tagName: editable.tagName.toLowerCase(),
        textContent: editable.textContent?.trim() || '',
        className: editable.className || '',
        id: editable.id || '',
        outerHTML: editable.outerHTML,
        isInteractive: true, // Mark as interactive to trigger implementation modal
        implementationType: 'edit',
        implementationReason: `Edit ${editable.tagName.toLowerCase()}: "${editable.textContent?.trim() || 'element'}"`,
        selector: selector
      };

      // Call the main editor's element click handler
      onElementClick(elementData);
      return;
    }

    // Fallback to original PatchManager system if no callback provided
    console.log('🔥 Using fallback PatchManager system');
    const fragmentHtml = editable.outerHTML;
    const section = editable.closest('[data-view]');
    const viewName = section ? section.getAttribute('data-view') : undefined;
    const prompt = `Edit the selected element: ${editable.textContent?.trim() || editable.tagName}`;

    if (viewName) {
      spaCore.patchManager.applyLlmPatchToView(viewName, fragmentHtml, prompt, selector);
    }
  }

  useEffect(() => {
    document.addEventListener('click', globalEditHandler, true);
    return () => {
      document.removeEventListener('click', globalEditHandler, true);
    };
  }, [onElementClick]); // Add onElementClick to dependencies

  return (
    <div ref={containerRef} className={`spa-shell ${className}`}>
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <h1 className="text-xl font-semibold text-gray-900">
                  SPA Dashboard
                </h1>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
                <div id="navItems" className="flex space-x-4">
                  {/* Navigation items will be populated by router */}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {/* View Mode Toggle */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => {
                    console.log('🔍 Preview button clicked');
                    onViewModeChange?.('preview');
                  }}
                  className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                    viewMode === 'preview'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  📱 Preview
                </button>
                <button
                  onClick={() => {
                    console.log('🔍 Code button clicked, current viewMode:', viewMode);
                    console.log('🔍 onViewModeChange function:', typeof onViewModeChange);
                    onViewModeChange?.('code');
                  }}
                  className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                    viewMode === 'code'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  💻 Code
                </button>
              </div>

              {enableEditMode && (
                <button
                  id="editModeBtn"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  Edit Mode
                </button>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-1">
        {(() => {
          console.log('🔍 SPAShell render - viewMode:', viewMode, 'dashboardHtml length:', dashboardHtml?.length || 0);
          return null;
        })()}

        {/* CODE VIEW */}
        {viewMode === 'code' && (
          <div className="h-full bg-gray-50 p-4">
            <div className="bg-white rounded-lg shadow-sm border h-full flex flex-col">
              <div className="flex items-center justify-between p-4 border-b bg-gray-50 rounded-t-lg">
                <h3 className="text-lg font-medium text-gray-900">HTML Source Code</h3>
                <button
                  onClick={() => {
                    if (dashboardHtml) {
                      navigator.clipboard.writeText(dashboardHtml);
                      console.log('📋 HTML copied to clipboard');
                    }
                  }}
                  className="px-3 py-1.5 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors"
                >
                  📋 Copy Code
                </button>
              </div>
              <div className="flex-1 overflow-auto">
                {dashboardHtml ? (
                  <div className="p-4">
                    <div className="mb-4 text-sm text-gray-600">
                      <span className="font-medium">{dashboardHtml.length.toLocaleString()}</span> characters
                    </div>
                    <pre className="p-4 text-sm text-gray-800 font-mono leading-relaxed whitespace-pre-wrap bg-gray-50 border rounded-lg overflow-auto">
                      <code>{dashboardHtml}</code>
                    </pre>
                  </div>
                ) : (
                  <div className="p-8 text-center text-gray-500">
                    <div className="mb-4">
                      <svg className="w-16 h-16 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No HTML Content</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Generate some content first to view the HTML source code.
                    </p>
                    <button
                      onClick={() => onViewModeChange?.('preview')}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors"
                    >
                      Switch to Preview Mode
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* NORMAL SPA VIEW WHEN viewMode === 'preview' */}
        {viewMode === 'preview' && (
          <div id="viewContainer" className="h-full">
            {/* View content will be populated by router */}
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-500">Loading...</p>
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Diff Modal */}
      <div id="diffModal" className="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
          <div className="mt-3">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Review Changes
            </h3>
            <div id="diffContent" className="mt-2 max-h-96 overflow-y-auto bg-gray-50 p-4 rounded border text-sm">
              {/* Diff content will be populated */}
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                id="rejectDiff"
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
              >
                Reject
              </button>
              <button
                id="applyDiff"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Apply Changes
              </button>
            </div>
          </div>
          <button
            id="closeDiffModal"
            className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Loading Overlay */}
      <div id="loadingOverlay" className="hidden fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-40">
        <div className="bg-white rounded-lg p-6 shadow-xl">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-gray-700">Processing changes...</span>
          </div>
        </div>
      </div>
      {/* Highlight style */}
      <style>{`
        [data-spa-edit-highlight] {
          outline: 3px solid #2563eb !important;
          outline-offset: 2px !important;
          z-index: 9999 !important;
          position: relative !important;
          transition: outline 0.2s;
        }
      `}</style>
    </div>
  );
};

/**
 * Initialize default views
 */
function initializeDefaultViews(router: Router, dashboardHtml?: string): void {
  // Use injected dashboardHtml if provided, otherwise fallback to default
  router.addView('dashboard', dashboardHtml || getDashboardContent(), 'Dashboard');
  router.addView('analytics', getAnalyticsContent(), 'Analytics');
  router.addView('settings', getSettingsContent(), 'Settings');
  router.updateNavigation();
}

export default SPAShell;
