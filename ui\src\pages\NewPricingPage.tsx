import React from "react";
import { PricingModule, PricingPlan } from "../modules/pricing";
import { useBilling } from "../contexts/BillingContext";

/**
 * Simplified pricing page focusing only on the Free version
 */
export const NewPricingPage: React.FC = () => {
  const { plan: currentPlan, prototypeCount, prototypeLimit } = useBilling();

  // Define pricing plans - only Free version
  const plans: PricingPlan[] = [
    {
      id: "free",
      name: "Free",
      description: "Perfect for creating prototypes quickly",
      price: 0,
      buttonText: "Get Started",
      features: [
        { name: "Upto 3 multi-page prototypes" },
        { name: "Advanced design options" },
        { name: "HTML5/CSS output" }
        
      ],
      quota: {
        prototypeLimit: 3
      }
    },
    {
      id: "pro",
      name: "Pro",
      description: "Coming soon",
      price: 5,
      buttonText: "Coming Soon",
      disabled: true, // Disable the Pro plan
      features: [
        { name: "Upto 50 multi-page prototypes" },
        { name: "Advanced design options" },
        { name: "Clean HTML5/CSS output" }        
      ],
      quota: {
        prototypeLimit: 50
      }
    }
  ];

  // Custom header to explain the pricing model
  const customHeader = (
    <div style={{ textAlign: 'center', maxWidth: '800px', margin: '0 auto', padding: '2rem 1rem' }}>
      <h1 style={{ fontSize: '2.5rem', fontWeight: 'bold', marginBottom: '1rem' }}>Simple Pricing</h1>
      <p style={{ fontSize: '1.25rem', color: '#4b5563', marginBottom: '2rem' }}>
        JustPrototype is currently available as a free service. Create up to 3 prototypes at no cost.
      </p>
    </div>
  );

  return (
    <PricingModule
      plans={plans}
      currentPlanId={currentPlan === "Free" ? "free" : "pro"}
      customHeader={customHeader}
      // Remove faqItems, comparisonData, and yearlyDiscountPercentage
    />
  );
};

export default NewPricingPage;
