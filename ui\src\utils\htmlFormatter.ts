/**
 * HTML Formatter Utility
 * Formats HTML code for better readability in the code editor
 */

export const formatHTML = (html: string): string => {
  if (!html || html.trim() === '') return html;
  
  try {
    // Remove extra whitespace and normalize
    let formatted = html.replace(/>\s+</g, '><').trim();
    
    // Split into tokens (tags and text content)
    const tokens = formatted.match(/<\/?[^>]+>|[^<]+/g) || [];
    let result = '';
    let indent = 0;
    const indentSize = 2;
    
    // Self-closing tags that don't need closing tags
    const selfClosingTags = new Set([
      'area', 'base', 'br', 'col', 'embed', 'hr', 'img', 
      'input', 'link', 'meta', 'param', 'source', 'track', 'wbr'
    ]);
    
    // Tags that should be on the same line as their content
    const inlineTags = new Set([
      'span', 'a', 'strong', 'em', 'b', 'i', 'u', 'small', 
      'code', 'kbd', 'var', 'samp', 'time', 'mark', 'del', 'ins'
    ]);
    
    for (let i = 0; i < tokens.length; i++) {
      const token = tokens[i].trim();
      if (!token) continue;
      
      if (token.startsWith('</')) {
        // Closing tag - decrease indent first
        indent = Math.max(0, indent - indentSize);
        result += ' '.repeat(indent) + token + '\n';
      } else if (token.startsWith('<')) {
        // Opening tag
        const tagName = token.match(/<(\w+)/)?.[1]?.toLowerCase();
        const isInlineTag = tagName && inlineTags.has(tagName);
        const isSelfClosing = token.endsWith('/>') || (tagName && selfClosingTags.has(tagName));
        
        // Check if next token is text content and this is an inline tag
        const nextToken = tokens[i + 1];
        const hasTextContent = nextToken && !nextToken.startsWith('<');
        
        if (isInlineTag && hasTextContent) {
          // Keep inline tags with their content on the same line
          result += ' '.repeat(indent) + token;
          if (nextToken) {
            result += nextToken.trim();
            i++; // Skip the text content token
            
            // Check for closing tag
            const closingToken = tokens[i + 1];
            if (closingToken && closingToken.startsWith('</')) {
              result += closingToken + '\n';
              i++; // Skip the closing tag token
            } else {
              result += '\n';
            }
          } else {
            result += '\n';
          }
        } else {
          // Regular block-level tag
          result += ' '.repeat(indent) + token + '\n';
          
          // Increase indent for next line if it's not self-closing
          if (!isSelfClosing) {
            indent += indentSize;
          }
        }
      } else {
        // Text content
        if (token.trim()) {
          result += ' '.repeat(indent) + token.trim() + '\n';
        }
      }
    }
    
    return result.trim();
  } catch (error) {
    console.error('Error formatting HTML:', error);
    return html;
  }
};

/**
 * Format HTML specifically for display in code editor
 * Adds extra spacing for better readability
 */
export const formatHTMLForEditor = (html: string): string => {
  const formatted = formatHTML(html);
  
  // Add extra line breaks between major sections
  return formatted
    .replace(/(<\/head>)/g, '$1\n')
    .replace(/(<body[^>]*>)/g, '$1\n')
    .replace(/(<\/body>)/g, '\n$1')
    .replace(/(<\/html>)/g, '\n$1');
};

/**
 * Check if HTML content needs formatting
 */
export const needsFormatting = (html: string): boolean => {
  if (!html || html.trim() === '') return false;
  
  // Check if HTML is all on one line or has poor formatting
  const lines = html.split('\n');
  const nonEmptyLines = lines.filter(line => line.trim().length > 0);
  
  // If most content is on one line, it needs formatting
  if (nonEmptyLines.length <= 2 && html.length > 200) {
    return true;
  }
  
  // Check for lack of proper indentation
  const hasProperIndentation = nonEmptyLines.some(line => 
    line.startsWith('  ') || line.startsWith('\t')
  );
  
  return !hasProperIndentation && html.includes('<');
};
