// Conversation service for managing conversation history in local storage
import { v4 as uuidv4 } from 'uuid';

// Local storage key for conversations
const STORAGE_KEY = 'prototype_conversations';

// Maximum number of messages to keep in a conversation
const MAX_CONVERSATION_LENGTH = 10;

// Maximum number of conversations to keep
const MAX_CONVERSATIONS = 20;

/**
 * Get all conversations from local storage
 * @returns {Object} Map of conversations by ID
 */
function getConversations() {
  const storedConversations = localStorage.getItem(STORAGE_KEY);
  return storedConversations ? JSON.parse(storedConversations) : {};
}

/**
 * Save conversations to local storage
 * @param {Object} conversations - Map of conversations by ID
 */
function saveConversations(conversations) {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(conversations));
}

/**
 * Create a new conversation
 * @param {string} initialPrompt - The initial prompt
 * @param {string} initialHtml - The initial generated HTML
 * @returns {Object} The new conversation object with ID
 */
export function createConversation(initialPrompt, initialHtml) {
  const conversations = getConversations();
  
  // Generate a unique ID
  const conversationId = uuidv4();
  
  // Create the conversation object
  const conversation = {
    id: conversationId,
    title: initialPrompt.substring(0, 50) + (initialPrompt.length > 50 ? '...' : ''),
    messages: [
      {
        role: 'user',
        content: initialPrompt,
        timestamp: Date.now()
      },
      {
        role: 'assistant',
        content: initialHtml,
        timestamp: Date.now()
      }
    ],
    currentHtml: initialHtml,
    createdAt: Date.now(),
    updatedAt: Date.now()
  };
  
  // Add the conversation to the map
  conversations[conversationId] = conversation;
  
  // Limit the number of conversations
  const conversationIds = Object.keys(conversations);
  if (conversationIds.length > MAX_CONVERSATIONS) {
    // Sort by updated time (oldest first)
    conversationIds.sort((a, b) => conversations[a].updatedAt - conversations[b].updatedAt);
    
    // Remove the oldest conversations
    const toRemove = conversationIds.slice(0, conversationIds.length - MAX_CONVERSATIONS);
    toRemove.forEach(id => delete conversations[id]);
  }
  
  // Save to local storage
  saveConversations(conversations);
  
  return conversation;
}

/**
 * Get a conversation by ID
 * @param {string} conversationId - The conversation ID
 * @returns {Object|null} The conversation object or null if not found
 */
export function getConversation(conversationId) {
  if (!conversationId) return null;
  
  const conversations = getConversations();
  return conversations[conversationId] || null;
}

/**
 * Add a message to a conversation
 * @param {string} conversationId - The conversation ID
 * @param {string} role - The role of the message sender (user or assistant)
 * @param {string} content - The message content
 * @returns {Object|null} The updated conversation or null if not found
 */
export function addMessage(conversationId, role, content) {
  const conversations = getConversations();
  const conversation = conversations[conversationId];
  
  if (!conversation) return null;
  
  // Add the message to the conversation
  conversation.messages.push({
    role,
    content,
    timestamp: Date.now()
  });
  
  // Limit the number of messages in the conversation
  if (conversation.messages.length > MAX_CONVERSATION_LENGTH) {
    conversation.messages = conversation.messages.slice(-MAX_CONVERSATION_LENGTH);
  }
  
  // Update the conversation timestamp
  conversation.updatedAt = Date.now();
  
  // Save to local storage
  saveConversations(conversations);
  
  return conversation;
}

/**
 * Update the current HTML in a conversation
 * @param {string} conversationId - The conversation ID
 * @param {string} html - The HTML content
 * @returns {Object|null} The updated conversation or null if not found
 */
export function updateHtml(conversationId, html) {
  const conversations = getConversations();
  const conversation = conversations[conversationId];
  
  if (!conversation) return null;
  
  conversation.currentHtml = html;
  conversation.updatedAt = Date.now();
  
  // Save to local storage
  saveConversations(conversations);
  
  return conversation;
}

/**
 * Get all conversations as an array
 * @returns {Array} Array of conversation objects
 */
export function getAllConversations() {
  const conversations = getConversations();
  
  // Convert to array and sort by updated time (newest first)
  return Object.values(conversations).sort((a, b) => b.updatedAt - a.updatedAt);
}

/**
 * Delete a conversation
 * @param {string} conversationId - The conversation ID
 * @returns {boolean} True if the conversation was deleted, false otherwise
 */
export function deleteConversation(conversationId) {
  const conversations = getConversations();
  
  if (!conversations[conversationId]) return false;
  
  delete conversations[conversationId];
  
  // Save to local storage
  saveConversations(conversations);
  
  return true;
}

/**
 * Format the conversation history for use in a prompt
 * @param {string} conversationId - The conversation ID
 * @returns {string} Formatted conversation history
 */
export function formatConversationForPrompt(conversationId) {
  const conversation = getConversation(conversationId);
  if (!conversation) return '';
  
  // Format the conversation history for the prompt
  let formattedHistory = '## CONVERSATION HISTORY\n\n';
  
  conversation.messages.forEach((message, index) => {
    // Skip the first assistant message as it's the initial HTML
    if (index === 1 && message.role === 'assistant') return;
    
    // For user messages, include the full content
    if (message.role === 'user') {
      formattedHistory += `USER: ${message.content}\n\n`;
    } 
    // For assistant messages (except the first), include a summary
    else if (message.role === 'assistant' && index > 1) {
      formattedHistory += `ASSISTANT: [Generated HTML code]\n\n`;
    }
  });
  
  formattedHistory += '## CURRENT HTML\n\n';
  formattedHistory += conversation.currentHtml;
  
  formattedHistory += '\n\n## INSTRUCTIONS\n\n';
  formattedHistory += 'Please modify the current HTML based on the user\'s latest request. Return the complete updated HTML.';
  
  return formattedHistory;
}
