import {
  Paper,
  Typography,
  List,
  <PERSON>I<PERSON>,
  Button,
  Box,
  Divider,
  <PERSON>,
  Stack,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  History as HistoryIcon
} from '@mui/icons-material';

function SavedPrototypes({ prototypes, onSelect, onDelete }) {
  // We'll handle empty state inside the component

  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: 1,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        flexGrow: 1,
        backgroundColor: 'transparent',
      }}
    >
      <Box sx={{
        p: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderBottom: '1px solid rgba(75, 85, 99, 0.2)',
        mb: 1
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography variant="subtitle2" fontWeight={600} sx={{ color: 'rgba(255, 255, 255, 0.9)' }}>
            Saved Prototypes
          </Typography>
        </Box>
        <Tooltip title={`${prototypes.length} saved ${prototypes.length === 1 ? 'prototype' : 'prototypes'}`}>
          <Chip
            label={`${prototypes.length}`}
            size="small"
            color="primary"
            sx={{
              fontWeight: 600,
              backgroundColor: 'rgba(139, 92, 246, 0.2)',
              color: '#A855F7',
              border: 'none',
              height: 20,
              fontSize: '0.7rem'
            }}
          />
        </Tooltip>
      </Box>

      {prototypes.length === 0 ? (
        <Box sx={{
          p: 2,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          textAlign: 'center',
          color: 'rgba(255, 255, 255, 0.5)',
          backgroundColor: 'rgba(30, 30, 60, 0.4)',
          borderRadius: 1
        }}>
          <HistoryIcon sx={{ fontSize: 32, mb: 1, opacity: 0.5 }} />
          <Typography variant="body2" sx={{ mb: 0.5, fontSize: '0.8rem' }}>
            No saved prototypes yet
          </Typography>
          <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>
            Generate and save prototypes to see them here
          </Typography>
        </Box>
      ) : (
        <List sx={{
          p: 0,
          overflow: 'auto',
          flexGrow: 1,
          maxHeight: '200px',
          backgroundColor: 'rgba(30, 30, 60, 0.4)',
          borderRadius: 1
        }}>
          {prototypes.map((prototype, index) => (
            <Box key={prototype.id}>
              {index > 0 && <Divider sx={{ borderColor: 'rgba(75, 85, 99, 0.2)' }} />}
              <ListItem
                sx={{
                  py: 1,
                  px: 1.5,
                  flexDirection: 'column',
                  alignItems: 'flex-start',
                  '&:hover': {
                    backgroundColor: 'rgba(139, 92, 246, 0.1)'
                  }
                }}
              >
                <Typography
                  variant="body2"
                  fontWeight={600}
                  noWrap
                  sx={{
                    width: '100%',
                    mb: 0.5,
                    color: '#A855F7',
                    fontSize: '0.8rem'
                  }}
                >
                  {prototype.name}
                </Typography>

                <Typography variant="caption" sx={{ mb: 1, color: 'rgba(255, 255, 255, 0.5)', fontSize: '0.7rem' }}>
                  {new Date(prototype.createdAt).toLocaleDateString(undefined, {
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </Typography>

                <Stack direction="row" spacing={1} sx={{ width: '100%' }}>
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<EditIcon fontSize="small" />}
                    onClick={() => onSelect(prototype)}
                    sx={{
                      fontWeight: 600,
                      fontSize: '0.7rem',
                      py: 0.25,
                      flexGrow: 1,
                      borderColor: 'rgba(139, 92, 246, 0.3)',
                      color: 'rgba(255, 255, 255, 0.9)',
                      '&:hover': {
                        borderColor: 'rgba(139, 92, 246, 0.8)',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)'
                      }
                    }}
                  >
                    Edit
                  </Button>
                  <IconButton
                    size="small"
                    onClick={() => onDelete(prototype.id)}
                    sx={{
                      border: '1px solid',
                      borderColor: 'rgba(248, 113, 113, 0.3)',
                      p: 0.25,
                      color: 'rgba(248, 113, 113, 0.8)',
                      '&:hover': {
                        backgroundColor: 'rgba(248, 113, 113, 0.1)',
                        borderColor: 'rgba(248, 113, 113, 0.5)'
                      }
                    }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Stack>
              </ListItem>
            </Box>
          ))}
        </List>
      )}
    </Paper>
  );
}

export default SavedPrototypes;
