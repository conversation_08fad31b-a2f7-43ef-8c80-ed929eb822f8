-- Sample data for testing the page list functionality
-- This file creates sample sessions (pages) for existing prototypes

-- First, let's ensure we have the prototype_sessions table
-- (This should already exist from the migration, but just in case)
CREATE TABLE IF NOT EXISTS prototype_sessions (
    id VARCHAR(36) PRIMARY KEY,
    prototype_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    page_url VARCHAR(500) NOT NULL,
    page_html TEXT NOT NULL,
    session_state VARCHAR(20) DEFAULT 'active' CHECK (session_state IN ('active', 'editing', 'completed', 'expired')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '24 hours'),

    -- Foreign key constraints
    CONSTRAINT fk_prototype_sessions_prototype FOREIGN KEY (prototype_id) REFERENCES prototypes(id) ON DELETE CASCADE,
    CONSTRAINT fk_prototype_sessions_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_user_prototype ON prototype_sessions (user_id, prototype_id);
CREATE INDEX IF NOT EXISTS idx_session_state ON prototype_sessions (session_state);
CREATE INDEX IF NOT EXISTS idx_last_accessed ON prototype_sessions (last_accessed);

-- Function to generate sample sessions for existing prototypes
-- This will create sample pages for each prototype
CREATE OR REPLACE FUNCTION create_sample_sessions()
RETURNS void AS $$
DECLARE
    prototype_record RECORD;
    session_id VARCHAR(36);
    sample_pages TEXT[] := ARRAY[
        '/home',
        '/about',
        '/contact',
        '/services',
        '/portfolio',
        '/blog',
        '/pricing',
        '/dashboard',
        '/profile',
        '/settings'
    ];
    page_url TEXT;
    sample_html TEXT;
    i INTEGER;
BEGIN
    -- Loop through all prototypes
    FOR prototype_record IN 
        SELECT p.id, p.user_id, p.title 
        FROM prototypes p 
        LIMIT 10 -- Limit to first 10 prototypes for testing
    LOOP
        -- Create 3-5 sample sessions for each prototype
        FOR i IN 1..(3 + (prototype_record.id % 3)) LOOP
            -- Generate unique session ID
            session_id := gen_random_uuid()::text;
            
            -- Pick a page URL from our sample list
            page_url := sample_pages[((prototype_record.id + i) % array_length(sample_pages, 1)) + 1];
            
            -- Create sample HTML content
            sample_html := '<html><head><title>' || prototype_record.title || ' - ' || page_url || '</title></head>' ||
                          '<body><h1>' || prototype_record.title || '</h1>' ||
                          '<nav><a href="/home">Home</a><a href="/about">About</a><a href="/contact">Contact</a></nav>' ||
                          '<main><h2>Page: ' || page_url || '</h2>' ||
                          '<p>This is a sample page for ' || prototype_record.title || '</p>' ||
                          '<p>Content for ' || page_url || ' goes here.</p></main></body></html>';
            
            -- Insert the session
            INSERT INTO prototype_sessions (
                id, prototype_id, user_id, page_url, page_html, session_state,
                created_at, updated_at, last_accessed, expires_at
            ) VALUES (
                session_id,
                prototype_record.id,
                prototype_record.user_id,
                page_url,
                sample_html,
                CASE 
                    WHEN i = 1 THEN 'active'
                    WHEN i = 2 THEN 'editing'
                    WHEN i = 3 THEN 'completed'
                    ELSE 'active'
                END,
                CURRENT_TIMESTAMP - (i || ' hours')::INTERVAL,
                CURRENT_TIMESTAMP - (i || ' hours')::INTERVAL,
                CURRENT_TIMESTAMP - ((i * 30) || ' minutes')::INTERVAL,
                CURRENT_TIMESTAMP + INTERVAL '24 hours'
            );
            
        END LOOP;
        
        RAISE NOTICE 'Created sample sessions for prototype: %', prototype_record.title;
    END LOOP;
    
    RAISE NOTICE 'Sample session creation completed';
END;
$$ LANGUAGE plpgsql;

-- Uncomment the line below to run the function and create sample data
-- SELECT create_sample_sessions();

-- Query to check the created sessions
-- SELECT 
--     ps.id,
--     p.title as project_title,
--     ps.page_url,
--     ps.session_state,
--     ps.created_at
-- FROM prototype_sessions ps
-- JOIN prototypes p ON ps.prototype_id = p.id
-- ORDER BY p.title, ps.created_at DESC;
