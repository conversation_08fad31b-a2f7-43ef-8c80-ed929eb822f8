/**
 * Test component for diff functionality
 * Used to verify diff-match-patch integration works correctly
 */

import React, { useState } from 'react';
import { diffUtils, applyHtmlDiff } from '../services/diffUtils';

const DiffTestComponent: React.FC = () => {
  const [originalHtml, setOriginalHtml] = useState(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Page</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div id="app" class="p-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">Welcome to My App</h1>
        <p class="text-gray-700 mb-4">This is a sample paragraph.</p>
        <button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            Click Me
        </button>
    </div>
</body>
</html>`);

  const [modifiedHtml, setModifiedHtml] = useState(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Page - Updated</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <div id="app" class="p-8">
        <h1 class="text-4xl font-bold text-blue-900 mb-6">Welcome to My Updated App</h1>
        <p class="text-gray-700 mb-4">This is a sample paragraph with more content.</p>
        <p class="text-green-600 mb-4">This is a new paragraph added to the page.</p>
        <button class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors">
            Click Me - Updated
        </button>
        <button class="bg-red-500 text-white px-4 py-2 rounded ml-4 hover:bg-red-600">
            New Button
        </button>
    </div>
</body>
</html>`);

  const [testResults, setTestResults] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const testBackendDiff = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/diff-test/workflow', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ originalHtml, modifiedHtml })
      });

      const result = await response.json();
      setTestResults(result);
    } catch (error) {
      console.error('Backend diff test failed:', error);
      setTestResults({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const testFrontendDiff = async () => {
    setIsLoading(true);
    try {
      // First get diff from backend
      const response = await fetch('/api/diff-test/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ originalHtml, modifiedHtml })
      });

      const backendResult = await response.json();
      
      if (backendResult.success && backendResult.diffResult.patches) {
        // Apply diff on frontend
        const frontendResult = applyHtmlDiff(originalHtml, backendResult.diffResult.patches);
        
        setTestResults({
          backend: backendResult,
          frontend: frontendResult,
          isIdentical: frontendResult.html === modifiedHtml,
          type: 'frontend'
        });
      } else {
        setTestResults({ error: 'Backend diff generation failed', backend: backendResult });
      }
    } catch (error) {
      console.error('Frontend diff test failed:', error);
      setTestResults({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const testSample = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/diff-test/sample');
      const result = await response.json();
      setTestResults(result);
    } catch (error) {
      console.error('Sample test failed:', error);
      setTestResults({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const getDiffStats = () => {
    const stats = diffUtils.getStats();
    setTestResults({ stats, type: 'stats' });
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Diff Functionality Test</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div>
          <h2 className="text-xl font-semibold mb-3">Original HTML</h2>
          <textarea
            value={originalHtml}
            onChange={(e) => setOriginalHtml(e.target.value)}
            className="w-full h-64 p-3 border border-gray-300 rounded font-mono text-sm"
            placeholder="Enter original HTML..."
          />
        </div>
        
        <div>
          <h2 className="text-xl font-semibold mb-3">Modified HTML</h2>
          <textarea
            value={modifiedHtml}
            onChange={(e) => setModifiedHtml(e.target.value)}
            className="w-full h-64 p-3 border border-gray-300 rounded font-mono text-sm"
            placeholder="Enter modified HTML..."
          />
        </div>
      </div>

      <div className="flex flex-wrap gap-4 mb-6">
        <button
          onClick={testBackendDiff}
          disabled={isLoading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {isLoading ? 'Testing...' : 'Test Backend Diff Workflow'}
        </button>
        
        <button
          onClick={testFrontendDiff}
          disabled={isLoading}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          {isLoading ? 'Testing...' : 'Test Frontend Diff Application'}
        </button>
        
        <button
          onClick={testSample}
          disabled={isLoading}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
        >
          {isLoading ? 'Testing...' : 'Test Sample Data'}
        </button>
        
        <button
          onClick={getDiffStats}
          className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
        >
          Get Diff Stats
        </button>
      </div>

      {testResults && (
        <div className="bg-gray-50 p-4 rounded">
          <h2 className="text-xl font-semibold mb-3">Test Results</h2>
          <pre className="bg-white p-4 rounded border overflow-auto max-h-96 text-sm">
            {JSON.stringify(testResults, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default DiffTestComponent;
