-- Fix for the increment_prototype_count function to properly enforce quotas
CREATE OR REPLACE FUNCTION public.increment_prototype_count(p_user_id integer)
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
DECLARE
    user_plan VARCHAR;
    current_count INTEGER;
    quota INTEGER;
BEGIN
    -- Get the user's current plan, prototype count, and quota
    SELECT plan, prototype_count, quota_prototypes INTO user_plan, current_count, quota
    FROM users WHERE id = p_user_id FOR UPDATE;

    -- Set default quota if null
    IF quota IS NULL THEN
        quota := CASE WHEN user_plan = 'free' THEN 3 ELSE 50 END;
    END IF;

    -- Ensure current_count is not null
    current_count := COALESCE(current_count, 0);

    -- Check if the user has already reached or exceeded their quota
    -- Note: Using >= instead of > to ensure users can't exceed their quota
    IF current_count >= quota THEN
        RETURN FALSE; -- Quota reached or exceeded
    END IF;

    -- Increment the prototype count
    UPDATE users
    SET prototype_count = current_count + 1
    WHERE id = p_user_id;

    RETURN TRUE;
END;
$function$;

-- Add a function to get a user's remaining quota
CREATE OR REPLACE FUNCTION public.get_user_quota(p_user_id integer)
 RETURNS TABLE (
    plan VARCHAR,
    prototype_count INTEGER,
    quota_prototypes INTEGER,
    remaining_count INTEGER
 )
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY
    SELECT
        u.plan,
        COALESCE(u.prototype_count, 0) as prototype_count,
        COALESCE(u.quota_prototypes, CASE WHEN u.plan = 'free' THEN 3 ELSE 50 END) as quota_prototypes,
        GREATEST(0, COALESCE(u.quota_prototypes, CASE WHEN u.plan = 'free' THEN 3 ELSE 50 END) - COALESCE(u.prototype_count, 0)) as remaining_count
    FROM users u
    WHERE u.id = p_user_id;
END;
$function$;
