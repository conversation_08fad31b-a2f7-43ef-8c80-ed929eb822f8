// API service for LLM providers - Frontend Client
import axios from 'axios';

// Backend API URL
const API_URL = 'http://localhost:5000/api';

// Create an axios instance with credentials configuration
const api = axios.create({
  baseURL: API_URL,
  withCredentials: true, // This is important for sending cookies with requests
  headers: {
    'Content-Type': 'application/json',
  }
});

// Default configuration (used for local operations only)
const DEFAULT_CONFIG = {
  temperature: 0.7,
  max_tokens: 4000,
};

// Default design settings
export const DEFAULT_DESIGN_SETTINGS = {
  designStyle: 'minimal',
  layoutOption: 'single-column',
  colorScheme: 'light',
  typographyStyle: 'modern-sans',
  responsiveDesign: true,
  animationsLevel: 1,
};

// API providers configuration
export const PROVIDERS = {
  OPENAI: 'openai',
  DEEPSEEK: 'deepseek',
  ANTHROPIC: 'anthropic',
  MISTRAL: 'mistral',
  GOOGLE: 'google',
};

// Available models for each provider
export const MODELS = {
  [PROVIDERS.OPENAI]: [
    { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'Most capable model, best for complex tasks' },
    { id: 'gpt-4o', name: 'GPT-4o', description: 'Latest model with improved capabilities' },
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Faster and more cost-effective' }
  ],
  [PROVIDERS.DEEPSEEK]: [
    { id: 'deepseek-coder', name: 'Deepseek Coder', description: 'Specialized for code generation' },
    { id: 'deepseek-chat', name: 'Deepseek Chat', description: 'General purpose chat model' }
  ],
  [PROVIDERS.ANTHROPIC]: [
    { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: 'Most powerful Claude model' },
    { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', description: 'Balanced performance and speed' },
    { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', description: 'Fastest Claude model' }
  ],
  [PROVIDERS.MISTRAL]: [
    { id: 'mistral-large-latest', name: 'Mistral Large', description: 'Most capable Mistral model' },
    { id: 'mistral-medium-latest', name: 'Mistral Medium', description: 'Balanced performance' },
    { id: 'mistral-small-latest', name: 'Mistral Small', description: 'Fast and efficient' }
  ],
  [PROVIDERS.GOOGLE]: [
    { id: 'gemini-pro', name: 'Gemini Pro', description: 'Balanced performance' },
    { id: 'gemini-ultra', name: 'Gemini Ultra', description: 'Most capable Google model' }
  ]
};

// Create a class to handle API calls
class LLMService {
  constructor() {
    // Get default provider from environment variable if available, otherwise use localStorage or fallback to OpenAI
    const envDefaultProvider = import.meta.env.VITE_DEFAULT_LLM_PROVIDER;
    const validEnvProvider = envDefaultProvider && Object.values(PROVIDERS).includes(envDefaultProvider.toLowerCase())
      ? envDefaultProvider.toLowerCase()
      : null;

    this.provider = localStorage.getItem('llm_provider') || validEnvProvider || PROVIDERS.OPENAI;
    this.model = localStorage.getItem(`${this.provider}_model`) || this.getDefaultModel(this.provider);
    this.config = DEFAULT_CONFIG;

    // Load design settings from localStorage or use defaults
    const savedDesignSettings = localStorage.getItem('design_settings');
    this.designSettings = savedDesignSettings ? JSON.parse(savedDesignSettings) : DEFAULT_DESIGN_SETTINGS;
  }

  // Set the provider
  setProvider(provider) {
    if (Object.values(PROVIDERS).includes(provider)) {
      this.provider = provider;
      localStorage.setItem('llm_provider', provider);

      // Set default model for this provider if not already set
      const savedModel = localStorage.getItem(`${provider}_model`);
      if (!savedModel) {
        this.model = this.getDefaultModel(provider);
        localStorage.setItem(`${provider}_model`, this.model);
      } else {
        this.model = savedModel;
      }

      return true;
    }
    return false;
  }

  // Set the model
  setModel(model) {
    this.model = model;
    localStorage.setItem(`${this.provider}_model`, model);
  }

  // Get default model for a provider
  getDefaultModel(provider) {
    const models = MODELS[provider] || [];
    return models.length > 0 ? models[0].id : '';
  }

  // Get available providers
  getProviders() {
    return Object.entries(PROVIDERS).map(([key, value]) => ({
      id: value,
      name: key.charAt(0) + key.slice(1).toLowerCase(),
    }));
  }

  // Get available models for current provider
  getModels() {
    return MODELS[this.provider] || [];
  }

  // Get available models for a specific provider
  getModelsForProvider(provider) {
    return MODELS[provider] || [];
  }

  // Get current provider
  getCurrentProvider() {
    return this.provider;
  }

  // Get current model
  getCurrentModel() {
    return this.model;
  }

  // Check if API key is set - always returns true now since keys are on the backend
  hasApiKey() {
    return true;
  }

  // Set design settings
  setDesignSettings(settings) {
    this.designSettings = { ...this.designSettings, ...settings };
    localStorage.setItem('design_settings', JSON.stringify(this.designSettings));
  }

  // Get current design settings
  getDesignSettings() {
    return this.designSettings;
  }

  // Generate HTML from prompt using backend API with streaming progress
  async generateHTML(prompt, customDesignSettings = null, onProgress = null) {
    // Use custom design settings if provided, otherwise use the stored settings
    const designSettings = customDesignSettings || this.designSettings;

    return new Promise((resolve, reject) => {
      let retryCount = 0;
      const maxRetries = 3;
      let retryDelay = 1000; // Start with 1 second
      let html = '';
      let eventSource;

      const connect = () => {
        eventSource = new EventSource(
          `${API_URL}/llm/generate-stream?prompt=${encodeURIComponent(prompt)}` +
          `&provider=${this.provider}` +
          `&model=${this.model}` +
          `&designSettings=${encodeURIComponent(JSON.stringify(designSettings))}`
        );

        eventSource.onopen = () => {
          retryCount = 0; // Reset retry counter on successful connection
          retryDelay = 1000;
        };

        eventSource.onmessage = (event) => {
          try {
            // Parse the raw SSE event data
            const eventStr = event.data;
            if (eventStr === '[DONE]') return;

            const data = JSON.parse(eventStr);

            if (data.status === 'planning') {
              if (onProgress) onProgress({
                status: 'planning',
                message: data.message
              });
            }
            else if (data.status === 'plan_complete') {
              if (onProgress) onProgress({
                status: 'plan_complete',
                message: data.message,
                plan: data.plan
              });
            }
            else if (data.status === 'generating') {
              if (onProgress) onProgress({
                status: 'generating',
                message: data.message
              });
            }
            else if (data.status === 'chunk') {
              html += data.chunk;
              if (onProgress) onProgress({
                status: 'chunk',
                chunk: data.chunk,
                html
              });
            }
            else if (data.status === 'complete') {
              if (onProgress) onProgress({
                status: 'complete',
                message: data.message,
                html: data.data?.html || html
              });
              eventSource.close();
              resolve(data.data?.html || html);
            }
            else if (data.status === 'error') {
              eventSource.close();
              reject(new Error(data.message || 'Failed to generate prototype'));
            }
          } catch (error) {
            console.error('Error processing SSE event:', error);
            eventSource.close();
            reject(error);
          }
        };

        eventSource.onerror = (error) => {
          eventSource.close();

          if (retryCount < maxRetries) {
            retryCount++;
            retryDelay *= 2; // Exponential backoff

            if (onProgress) onProgress({
              status: 'reconnecting',
              message: `Connection lost. Retrying in ${retryDelay/1000} seconds... (${retryCount}/${maxRetries})`
            });

            setTimeout(connect, retryDelay);
          } else {
            reject(new Error('Max reconnection attempts reached. Please try again.'));
          }
        };
      };

      connect(); // Initial connection
    });
  }

  // Alias for backward compatibility
  async streamGenerateHTML(prompt, customDesignSettings = null, onProgress = null) {
    return this.generateHTML(prompt, customDesignSettings, onProgress);
  }

  // Get available providers from backend
  async fetchProviders() {
    try {
      const response = await api.get('/llm/providers');
      return response.data.providers;
    } catch (error) {
      console.error('Error fetching providers:', error);
      return this.getProviders(); // Fallback to local providers
    }
  }
}

// Export the service instance
export const llmService = new LLMService();
