import { BrowserRouter as Router, Routes, Route, Link, useNavigate, useLocation } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import { CounterProvider } from './contexts/CounterContext';
import { ErrorBoundary, DefaultErrorFallback } from './components/ErrorBoundary';
import { Home } from './pages/Home';
import { LandingPage } from './pages/LandingPage';
import ASTDebugPage from './pages/ASTDebugPage';
import InitialScreen from './pages/InitialScreen';
import { PromptInputPage } from './pages/PromptInputPage';
import { EditorPage } from './pages/EditorPage';
import { PromptInputPageV3 } from './pages/PromptInputPageV3';
import { PlanReviewPageV3 } from './pages/PlanReviewPageV3';
import { EditorPageV3 } from './pages/EditorPageV3';
import EditorPageV3Refactored from './pages/EditorPageV3Refactored';
import EditorComparisonPage from './pages/EditorComparisonPage';
import { PlanReviewPage } from './pages/PlanReviewPage';
import SharedPrototypePage from './pages/SharedPrototypePage';
import { MyPrototypesPage } from './pages/MyPrototypesPage';
import { PrototypeViewerPage } from './pages/PrototypeViewerPage';
import { PrivacyPolicyPage } from './pages/PrivacyPolicyPage';
import PatchManagerTestPage from './pages/PatchManagerTestPage';
import { useTheme } from './contexts/ThemeContext';
import RequireAuth from './components/RequireAuth';
import React, { useEffect, useState } from 'react';
import styles from './App.module.css';

import { BillingProvider } from './contexts/BillingContext';
import { useBilling } from './contexts/BillingContext';
import { AuthProvider } from './contexts/AuthContext';
import { useAuth } from './contexts/AuthContext';
import { trackPageView } from './utils/analytics';
import CookieConsent from './components/CookieConsent';
import { Footer } from './components/Footer';

import { FiMenu } from 'react-icons/fi';
import { FiUser } from 'react-icons/fi';
import { FiChevronDown, FiLogOut } from 'react-icons/fi';

// Import PricingPage (now default export)
import NewPricingPage from './pages/NewPricingPage';

function AuthNav() {
  const { plan, prototypeCount, prototypeLimit } = useBilling();
  const { logout, authState } = useAuth();
  const [dropdownOpen, setDropdownOpen] = React.useState(false);

  if (!authState.isAuthenticated) {
    const base = import.meta.env.VITE_API_BASE_URL || "/api";
    return (
      <div style={{ display: 'flex', gap: 12 }}>
        <button
          className={styles.primaryCta}
          onClick={() => { window.location.href = `${base}/auth/google?redirect=/prototypes`; }}
          style={{ fontWeight: 700 }}
        >
          Sign In
        </button>
        <button
          className={styles.primaryCta}
          onClick={() => { window.location.href = `${base}/auth/google?redirect=/prototypes`; }}
          style={{ fontWeight: 700 }}
        >
          Start Free Trial
        </button>
      </div>
    );
  }

  return (
    <div className={styles.authNav} style={{ position: 'relative' }}>
      <span style={{ display: 'flex', alignItems: 'center', gap: 8, cursor: 'pointer' }} onClick={() => setDropdownOpen((v) => !v)}>
        <span style={{ width: 32, height: 32, borderRadius: '50%', background: '#2563eb', color: '#fff', display: 'flex', alignItems: 'center', justifyContent: 'center', fontWeight: 700, fontSize: 18 }}>
          <FiUser />
        </span>
        <FiChevronDown style={{ color: '#2563eb', fontSize: 18 }} />
        {plan === 'Free' && (
          <span className={styles.trialBadge} style={{ background: '#2563eb', color: '#fff', border: 'none' }}>Trial</span>
        )}
      </span>
      {dropdownOpen && (
        <div style={{ position: 'absolute', top: 44, right: 0, background: '#fff', border: '1px solid #e5e7eb', borderRadius: 8, boxShadow: '0 4px 16px rgba(0,0,0,0.08)', minWidth: 180, zIndex: 100, padding: 12 }}>
          <div style={{ fontWeight: 600, marginBottom: 6 }}>
            {plan === 'Free' ? 'Trial Plan' : plan ? plan.charAt(0).toUpperCase() + plan.slice(1) + ' Plan' : 'Plan'}
          </div>
          <div style={{ fontSize: 14, color: '#2563eb', marginBottom: 8 }}>
            {typeof prototypeCount === 'number' && typeof prototypeLimit === 'number' ? `${prototypeCount} / ${prototypeLimit} prototypes used` : ''}
          </div>
          <button
            onClick={() => { setDropdownOpen(false); logout(); }}
            style={{ width: '100%', background: 'none', border: 'none', padding: '0.6em 0', textAlign: 'left', color: '#ef4444', fontWeight: 600, display: 'flex', alignItems: 'center', gap: 8, cursor: 'pointer', fontSize: 15 }}
          >
            <FiLogOut /> Logout
          </button>
        </div>
      )}
    </div>
  );
}

function AppContent() {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  const [checked, setChecked] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const redirectAttemptedRef = React.useRef(false);

  const [menuOpen, setMenuOpen] = React.useState(false);

  useEffect(() => {
    const path = location.pathname;
    if (path.includes('//')) {
      console.log('Fixing double slash in URL:', path);
      const fixedPath = path.replace(/\/{2,}/g, '/');
      console.log('Fixed path:', fixedPath);
      navigate(fixedPath, { replace: true });
    }
  }, [location.pathname, navigate]);

  useEffect(() => {
    let pageTitle = 'JustPrototype';

    if (location.pathname === '/') {
      pageTitle = 'JustPrototype - Create UI Prototypes Instantly';
    } else if (location.pathname === '/prototypes') {
      pageTitle = 'My Prototypes - JustPrototype';
    } else if (location.pathname === '/pricing') {
      pageTitle = 'Pricing - JustPrototype';
    } else if (location.pathname.startsWith('/editor')) {
      pageTitle = 'Prototype Editor - JustPrototype';
    } else if (location.pathname.startsWith('/prototype/')) {
      pageTitle = 'View Prototype - JustPrototype';
    } else if (location.pathname.startsWith('/shared/')) {
      pageTitle = 'Shared Prototype - JustPrototype';
    } else if (location.pathname === '/prompt') {
      pageTitle = 'Create Prototype - JustPrototype';
    }

    trackPageView(location.pathname, pageTitle);
  }, [location.pathname]);

  useEffect(() => {
    if (location.pathname === "/" && !redirectAttemptedRef.current) {
      redirectAttemptedRef.current = true;

      const isLoggedIn = document.cookie.includes('isLoggedIn=true');

      if (isLoggedIn) {
        console.log("AppContent: User is logged in according to cookie, redirecting to prototypes");
        navigate("/prototypes", { replace: true });
      } else {
        const cachedAuthString = localStorage.getItem('authState');
        if (cachedAuthString) {
          try {
            const cachedAuth = JSON.parse(cachedAuthString);
            const isRecent = (Date.now() - cachedAuth.lastChecked) < 30 * 60 * 1000;

            if (isRecent && cachedAuth.isAuthenticated) {
              console.log("AppContent: User is authenticated according to cached state, redirecting to prototypes");
              navigate("/prototypes", { replace: true });
            }
          } catch (e) {
            console.error("AppContent: Error parsing cached auth state:", e);
          }
        }
      }

      setChecked(true);
    } else {
      setChecked(true);
    }
  }, [location.pathname, navigate]);

  if (!checked) return null;

  const isActive = (path: string) => location.pathname === path;
  const isSharedPage = location.pathname.startsWith('/shared/');

  return (
    <div className={`${styles.container} ${isDark ? styles.darkContainer : ''}`}>
      {!isSharedPage && (
        <>
          <nav className={`${styles.nav} ${isDark ? styles.darkNav : ''}`} style={{ background: 'linear-gradient(90deg, #2563eb 0%, #1e40af 100%)', position: 'sticky', top: 0, zIndex: 100 }}>
            <div className={styles.navContent} style={{ minHeight: 64, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <Link to="/" className={`${styles.navLink} ${styles.navLogo}`} style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ borderRadius: 8, background: '#fff', boxShadow: '0 1px 4px rgba(0,0,0,0.04)' }}>
                    <rect x="4" y="4" width="24" height="24" rx="8" fill="#2563eb" />
                    <text x="16" y="22" textAnchor="middle" fontSize="14" fontWeight="bold" fill="#fff">JP</text>
                  </svg>
                  <span>JustPrototype</span>
                </Link>
              </div>
              <div className="nav-center-links" style={{ display: 'flex', alignItems: 'center', gap: 24 }}>
                <Link
                  to="/prototypes"
                  className={`${styles.navLink} ${isActive("/prototypes") ? styles.activeNavLink : ""}`}
                  style={isActive("/prototypes") ? { textDecoration: "underline", color: "#fde047", background: "#2563eb", borderRadius: "999px", padding: "0.25em 1em" } : {}}
                >
                  My Prototypes
                </Link>
                <Link
                  to="/pricing"
                  className={`${styles.navLink} ${isActive("/pricing") ? styles.activeNavLink : ""}`}
                  style={isActive("/pricing") ? { textDecoration: "underline", color: "#fde047", background: "#2563eb", borderRadius: "999px", padding: "0.25em 1em" } : {}}
                >
                  Pricing
                </Link>
                <Link
                  to="/prompt-v3"
                  className={`${styles.navLink} ${isActive("/prompt-v3") || isActive("/plan-v3") || isActive("/editor-v3-refactored") ? styles.activeNavLink : ""}`}
                  style={isActive("/prompt-v3") || isActive("/plan-v3") || isActive("/editor-v3-refactored") ? { textDecoration: "underline", color: "#fde047", background: "#2563eb", borderRadius: "999px", padding: "0.25em 1em" } : {}}
                >
                  V3 Editor
                </Link>
                <Link
                  to="/debug/patch-manager"
                  className={`${styles.navLink} ${isActive("/debug/patch-manager") ? styles.activeNavLink : ""}`}
                  style={isActive("/debug/patch-manager") ? { textDecoration: "underline", color: "#fde047", background: "#2563eb", borderRadius: "999px", padding: "0.25em 1em" } : { fontSize: '0.9em', opacity: 0.8 }}
                >
                  🧪 Test
                </Link>
              </div>
              <div style={{ marginLeft: 'auto', display: 'flex', alignItems: 'center', gap: '1rem' }}>
                <AuthNav />
                <button className={styles.hamburger} aria-label="Menu" onClick={() => setMenuOpen((v) => !v)} style={{ display: 'none', background: 'none', border: 'none', color: '#fff', fontSize: 28, marginLeft: 8 }}>
                  <FiMenu />
                </button>
              </div>
            </div>
          </nav>
          <UsageBannerWrapper />
        </>
      )}
      <Routes>
        <Route path="/" element={<LandingPage />} />
        <Route path="/home" element={<Home />} />
        <Route path="/new" element={<InitialScreen />} />
        <Route path="/prompt" element={
          <RequireAuth>
            <PromptInputPage />
          </RequireAuth>
        } />
        <Route path="/plan" element={
          <RequireAuth>
            <PlanReviewPage />
          </RequireAuth>
        } />
        <Route path="/editor" element={
          <RequireAuth>
            <EditorPage />
          </RequireAuth>
        } />
        <Route path="/editor/:id" element={
          <RequireAuth>
            <EditorPage />
          </RequireAuth>
        } />
        <Route path="/prompt-v3" element={
          <RequireAuth>
            <PromptInputPageV3 />
          </RequireAuth>
        } />
        <Route path="/plan-v3" element={
          <RequireAuth>
            <PlanReviewPageV3 />
          </RequireAuth>
        } />
        <Route path="/editor-v3" element={
          <RequireAuth>
            <EditorPageV3 />
          </RequireAuth>
        } />
        <Route path="/editor-v3-refactored" element={
          <RequireAuth>
            <EditorPageV3Refactored />
          </RequireAuth>
        } />
        <Route path="/editor-v3-new" element={
          <RequireAuth>
            <EditorPageV3Refactored />
          </RequireAuth>
        } />
        <Route path="/editor-comparison" element={
          <RequireAuth>
            <EditorComparisonPage />
          </RequireAuth>
        } />
        <Route path="/prototypes" element={
          <RequireAuth>
            <MyPrototypesPage />
          </RequireAuth>
        } />
        <Route path="/prototype/:id" element={
          <RequireAuth>
            <PrototypeViewerPage />
          </RequireAuth>
        } />
        <Route path="/pricing" element={<NewPricingPage />} />
        <Route path="/debug/ast" element={<ASTDebugPage />} />
        <Route path="/debug/patch-manager" element={<PatchManagerTestPage />} />
        <Route path="/shared/:accessToken" element={<SharedPrototypePage />} />
        <Route path="/privacy" element={<PrivacyPolicyPage />} />
      </Routes>
      {!isSharedPage && <Footer />}
    </div>
  );
}

import { UsageBanner } from './components/UsageBanner';

function UsageBannerWrapper() {
  const { plan, prototypeCount, prototypeLimit } = useBilling();
  return (
    <UsageBanner
      plan={plan}
      prototypeCount={prototypeCount}
      prototypeLimit={prototypeLimit}
    />
  );
}

function App() {
  return (
    <ErrorBoundary fallback={DefaultErrorFallback}>
      <ThemeProvider>
        <CounterProvider>
          <BillingProvider>
            <AuthProvider>
              <Router>
                <AppContent />
                <CookieConsent />
              </Router>
            </AuthProvider>
          </BillingProvider>
        </CounterProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;
