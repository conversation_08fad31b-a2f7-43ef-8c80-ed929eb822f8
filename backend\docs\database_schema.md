# 🗄️ **Database Schema Documentation: Session Management**

## **Overview**
This document describes the database schema for the Readdy-style intent generation system. The schema is designed to support efficient session management, element interaction tracking, and performance optimization.

## **📋 Table Relationships**

```mermaid
erDiagram
    users ||--o{ prototype_sessions : creates
    prototypes ||--o{ prototype_sessions : contains
    prototype_sessions ||--o{ element_interactions : tracks
    element_interactions ||--o{ intent_generations : generates
    prototype_sessions ||--o{ session_analytics : measures
```

## **🗂️ Table Definitions**

### **1. prototype_sessions**
**Purpose**: Stores page session context without repeatedly sending full HTML to LLM

| Column | Type | Description | Constraints |
|--------|------|-------------|-------------|
| id | VARCHAR(36) | Unique session identifier | PRIMARY KEY |
| prototype_id | VARCHAR(36) | Reference to prototype | FK, NOT NULL |
| user_id | VARCHAR(36) | Session owner | FK, NOT NULL |
| page_url | VARCHAR(500) | Current page URL for navigation | NOT NULL |
| page_html | LONGTEXT | Full HTML stored once per session | NOT NULL |
| session_state | ENUM | active/editing/completed/expired | DEFAULT 'active' |
| created_at | TIMESTAMP | Session creation time | AUTO |
| updated_at | TIMESTAMP | Last modification time | AUTO UPDATE |
| last_accessed | TIMESTAMP | Last interaction time | AUTO UPDATE |
| expires_at | TIMESTAMP | Automatic expiration | DEFAULT +24h |

**Key Features:**
- ✅ Stores HTML once per session (not per interaction)
- ✅ Automatic expiration after 24 hours
- ✅ Tracks session lifecycle states
- ✅ Optimized indexes for common queries

### **2. element_interactions**
**Purpose**: Tracks specific element clicks and interactions for context building

| Column | Type | Description | Constraints |
|--------|------|-------------|-------------|
| id | VARCHAR(36) | Unique interaction ID | PRIMARY KEY |
| session_id | VARCHAR(36) | Parent session | FK, NOT NULL |
| element_selector | VARCHAR(500) | CSS selector for element | NOT NULL |
| element_code | TEXT | HTML of clicked element | NOT NULL |
| element_type | VARCHAR(50) | button/link/form/etc | DEFAULT 'unknown' |
| interaction_type | ENUM | click/hover/focus/select | DEFAULT 'click' |
| user_intent | TEXT | Generated user intent | NULLABLE |
| ai_suggestion | TEXT | AI implementation suggestion | NULLABLE |
| confidence_score | DECIMAL(3,2) | Intent confidence 0.00-1.00 | DEFAULT 0.00 |
| created_at | TIMESTAMP | Interaction timestamp | AUTO |

**Key Features:**
- ✅ Stores only clicked element HTML (not full page)
- ✅ Tracks element type for specialized processing
- ✅ Records intent generation results
- ✅ Confidence scoring for quality metrics

### **3. intent_generations**
**Purpose**: Caches intent generation results and tracks LLM performance

| Column | Type | Description | Constraints |
|--------|------|-------------|-------------|
| id | VARCHAR(36) | Unique generation ID | PRIMARY KEY |
| interaction_id | VARCHAR(36) | Source interaction | FK, NOT NULL |
| element_hash | VARCHAR(64) | MD5 hash for caching | NOT NULL |
| user_intent | TEXT | Generated intent description | NOT NULL |
| ai_suggestion | TEXT | Implementation suggestion | NOT NULL |
| confidence_level | ENUM | high/medium/low | DEFAULT 'medium' |
| token_usage | INT | Tokens consumed | DEFAULT 0 |
| response_time_ms | INT | Generation time | DEFAULT 0 |
| llm_provider | VARCHAR(50) | litellm/openai/etc | DEFAULT 'litellm' |
| created_at | TIMESTAMP | Generation timestamp | AUTO |

**Key Features:**
- ✅ Enables caching of similar elements
- ✅ Tracks performance metrics
- ✅ Monitors token usage for cost optimization
- ✅ Provider-agnostic tracking

### **4. session_analytics**
**Purpose**: Aggregates session-level metrics for monitoring and optimization

| Column | Type | Description | Constraints |
|--------|------|-------------|-------------|
| id | VARCHAR(36) | Unique analytics ID | PRIMARY KEY |
| session_id | VARCHAR(36) | Target session | FK, NOT NULL |
| total_interactions | INT | Count of interactions | DEFAULT 0 |
| successful_intents | INT | Successful generations | DEFAULT 0 |
| failed_intents | INT | Failed generations | DEFAULT 0 |
| total_tokens_used | INT | Cumulative token usage | DEFAULT 0 |
| avg_response_time_ms | DECIMAL(8,2) | Average response time | DEFAULT 0.00 |
| session_duration_seconds | INT | Total session time | DEFAULT 0 |
| created_at | TIMESTAMP | Analytics creation | AUTO |
| updated_at | TIMESTAMP | Last update | AUTO UPDATE |

**Key Features:**
- ✅ Real-time performance monitoring
- ✅ Cost tracking per session
- ✅ Success rate analytics
- ✅ Automatic updates via triggers

## **🔧 Performance Optimizations**

### **Index Strategy**
```sql
-- Session lookup optimization
INDEX idx_user_prototype (user_id, prototype_id)
INDEX idx_session_state (session_state)
INDEX idx_last_accessed (last_accessed)

-- Interaction queries
INDEX idx_session_interactions (session_id, created_at)
INDEX idx_element_type (element_type)

-- Caching optimization
INDEX idx_element_hash (element_hash)
INDEX idx_confidence_level (confidence_level)
```

### **Query Patterns**
1. **Get Active Session**: `user_id + prototype_id + session_state = 'active'`
2. **Recent Interactions**: `session_id + ORDER BY created_at DESC LIMIT 5`
3. **Cache Lookup**: `element_hash + confidence_level >= 'medium'`
4. **Cleanup Query**: `expires_at < NOW() OR last_accessed < DATE_SUB(NOW(), INTERVAL 48 HOUR)`

## **🔄 Automatic Maintenance**

### **Triggers**
- **update_session_analytics_on_interaction**: Updates analytics on new interactions
- **update_session_last_accessed**: Keeps session access time current

### **Scheduled Events**
- **cleanup_expired_sessions**: Runs every 6 hours to remove old sessions

### **Cleanup Policy**
- Sessions expire after 24 hours of inactivity
- Hard cleanup after 48 hours regardless of activity
- Cascading deletes maintain referential integrity

## **📊 Expected Performance**

### **Storage Estimates**
- **Session**: ~50KB average (mostly page_html)
- **Interaction**: ~2KB average (element_code only)
- **Intent Generation**: ~1KB average (text responses)
- **Analytics**: ~200 bytes (numeric data)

### **Query Performance Targets**
- Session lookup: <10ms
- Recent interactions: <20ms
- Intent cache hit: <5ms
- Analytics aggregation: <50ms

### **Scalability Projections**
- **1000 active users**: ~50MB session storage
- **10K interactions/day**: ~20MB daily growth
- **Cleanup efficiency**: 95% automated maintenance

## **🛡️ Data Retention & Security**

### **Retention Policy**
- Active sessions: 24 hours
- Completed sessions: 48 hours
- Analytics data: 30 days
- Performance metrics: 90 days

### **Security Considerations**
- Foreign key constraints prevent orphaned data
- Automatic cleanup prevents data accumulation
- No sensitive data stored in plain text
- Session IDs are UUIDs (non-guessable)

## **🔍 Monitoring Queries**

### **Performance Monitoring**
```sql
-- Average response times by provider
SELECT llm_provider, AVG(response_time_ms) as avg_response_time
FROM intent_generations 
WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY llm_provider;

-- Token usage trends
SELECT DATE(created_at) as date, SUM(token_usage) as daily_tokens
FROM intent_generations 
WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(created_at);

-- Success rates
SELECT 
    successful_intents / (successful_intents + failed_intents) * 100 as success_rate
FROM session_analytics 
WHERE updated_at > DATE_SUB(NOW(), INTERVAL 24 HOUR);
```

This schema provides the foundation for efficient, scalable session management while maintaining the performance benefits of Readdy's element-focused approach.
