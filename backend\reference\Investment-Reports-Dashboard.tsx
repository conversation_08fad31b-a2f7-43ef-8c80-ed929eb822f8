// The exported code uses Tailwind CSS. Install Tailwind CSS in your dev environment to ensure all styles work.
import React, { useState, useEffect } from 'react';
import * as echarts from 'echarts';
const App: React.FC = () => {
const [isDropdownOpen, setIsDropdownOpen] = useState(false);
const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
const [activeReportType, setActiveReportType] = useState('all');
const [startDate, setStartDate] = useState('');
const [endDate, setEndDate] = useState('');
const [selectedClient, setSelectedClient] = useState('');
const [viewMode, setViewMode] = useState('table');
const [currentPage, setCurrentPage] = useState(1);
const [isGenerateModalOpen, setIsGenerateModalOpen] = useState(false);
const [selectedFormat, setSelectedFormat] = useState('pdf');
const [isGenerating, setIsGenerating] = useState(false);
const [formErrors, setFormErrors] = useState({
  template: '',
  name: '',
  startDate: '',
  endDate: '',
  client: ''
});
const [showToast, setShowToast] = useState(false);
const [toastMessage, setToastMessage] = useState({ type: '', message: '', link: '' });

const validateForm = () => {
  const errors = {
    template: '',
    name: '',
    startDate: '',
    endDate: '',
    client: ''
  };
  let isValid = true;

  const template = (document.getElementById('report-template') as HTMLSelectElement)?.value;
  const name = (document.getElementById('report-name') as HTMLInputElement)?.value;
  const startDate = (document.getElementById('modal-start-date') as HTMLInputElement)?.value;
  const endDate = (document.getElementById('modal-end-date') as HTMLInputElement)?.value;
  const client = (document.getElementById('modal-client') as HTMLSelectElement)?.value;

  if (!template) {
    errors.template = 'Please select a report template';
    isValid = false;
  }
  if (!name) {
    errors.name = 'Please enter a report name';
    isValid = false;
  }
  if (!startDate) {
    errors.startDate = 'Please select a start date';
    isValid = false;
  }
  if (!endDate) {
    errors.endDate = 'Please select an end date';
    isValid = false;
  }
  if (!client) {
    errors.client = 'Please select a client';
    isValid = false;
  }

  setFormErrors(errors);
  return isValid;
};

const handleGenerateReport = async () => {
  if (validateForm()) {
    setIsGenerating(true);
    try {
      // Simulate report generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const reportId = 'REP' + Math.random().toString(36).substr(2, 9);
      setToastMessage({
        type: 'success',
        message: 'Report generated successfully!',
        link: `/reports/${reportId}`
      });
      setShowToast(true);
      setIsGenerateModalOpen(false);
      
      // Hide toast after 5 seconds
      setTimeout(() => setShowToast(false), 5000);
    } catch (error) {
      setToastMessage({
        type: 'error',
        message: 'Failed to generate report. Please try again.',
        link: ''
      });
      
    } finally {
      setIsGenerating(false);
    }
  }
};

useEffect(() => {
// Performance trend chart
const performanceChart = echarts.init(document.getElementById('performance-trend-chart'));
const performanceOption = {
animation: false,
tooltip: {
trigger: 'axis'
},
legend: {
data: ['Monthly Reports', 'Quarterly Reports', 'Annual Reports']
},
grid: {
left: '3%',
right: '4%',
bottom: '3%',
containLabel: true
},
xAxis: {
type: 'category',
boundaryGap: false,
data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
},
yAxis: {
type: 'value'
},
series: [
{
name: 'Monthly Reports',
type: 'line',
data: [12, 15, 18, 14, 22, 25, 20, 18, 24, 28, 30, 32],
itemStyle: {
color: '#3B82F6'
}
},
{
name: 'Quarterly Reports',
type: 'line',
data: [5, 4, 6, 5, 7, 8, 9, 8, 10, 12, 14, 15],
itemStyle: {
color: '#10B981'
}
},
{
name: 'Annual Reports',
type: 'line',
data: [2, 1, 3, 2, 4, 3, 5, 4, 6, 5, 7, 8],
itemStyle: {
color: '#F59E0B'
}
}
]
};
performanceChart.setOption(performanceOption);
// Report distribution chart
const distributionChart = echarts.init(document.getElementById('report-distribution-chart'));
const distributionOption = {
animation: false,
tooltip: {
trigger: 'item',
formatter: '{a} <br/>{b}: {c} ({d}%)'
},
legend: {
orient: 'vertical',
right: 10,
top: 'center',
data: ['Performance', 'Client', 'Investment', 'Compliance']
},
series: [
{
name: 'Report Types',
type: 'pie',
radius: ['50%', '70%'],
avoidLabelOverlap: false,
itemStyle: {
borderRadius: 10,
borderColor: '#fff',
borderWidth: 2
},
label: {
show: false,
position: 'center'
},
emphasis: {
label: {
show: true,
fontSize: 14,
fontWeight: 'bold'
}
},
labelLine: {
show: false
},
data: [
{ value: 35, name: 'Performance' },
{ value: 30, name: 'Client' },
{ value: 20, name: 'Investment' },
{ value: 15, name: 'Compliance' }
]
}
]
};
distributionChart.setOption(distributionOption);
// Handle resize
const handleResize = () => {
performanceChart.resize();
distributionChart.resize();
};
window.addEventListener('resize', handleResize);
return () => {
window.removeEventListener('resize', handleResize);
performanceChart.dispose();
distributionChart.dispose();
};
}, []);
return (
<div className="min-h-screen bg-gray-50">
{/* Header */}
<header className="bg-white shadow-sm">
<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
<div className="flex justify-between h-16">
<div className="flex">
<div className="flex-shrink-0 flex items-center">
<img className="h-8 w-auto" src="https://readdy.ai/api/search-image?query=Modern%2520minimalist%2520investment%2520banking%2520logo%2520with%2520blue%2520and%2520gray%2520colors%252C%2520professional%2520and%2520clean%2520design%252C%2520suitable%2520for%2520a%2520high-end%2520financial%2520institution%252C%2520simple%2520geometric%2520shapes%2520with%2520subtle%2520gradient&width=160&height=40&seq=1&orientation=landscape" alt="Dashing CRM" />
</div>
<nav className="ml-6 flex space-x-8">
<div className="relative">
<button
onClick={() => setIsDropdownOpen(!isDropdownOpen)}
className="border-transparent text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 text-sm font-medium cursor-pointer whitespace-nowrap !rounded-button"
>
Dashboard
<i className="fas fa-chevron-down ml-1 text-xs"></i>
</button>
{isDropdownOpen && (
<div className="absolute z-10 mt-1 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
<div className="py-1" role="menu" aria-orientation="vertical">
<a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Overview</a>
<a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Performance</a>
<a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">Analytics</a>
</div>
</div>
)}

{/* Toast Notification */}
{showToast && (
  <div className={`fixed bottom-4 right-4 flex items-center ${toastMessage.type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white px-4 py-3 rounded-lg shadow-lg`}>
    <div className="flex items-center">
      <i className={`mr-2 fas ${toastMessage.type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}`}></i>
      <span>{toastMessage.message}</span>
      {toastMessage.link && (
        <a href={toastMessage.link} className="ml-2 underline">
          View Report
        </a>
      )}
    </div>
    <button 
      onClick={() => setShowToast(false)}
      className="ml-4 text-white hover:text-gray-100 focus:outline-none"
    >
      <i className="fas fa-times"></i>
    </button>
  </div>
)}
</div>
<a href="#" className="border-transparent text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 text-sm font-medium whitespace-nowrap">Clients</a>
<a href="#" className="border-transparent text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 text-sm font-medium whitespace-nowrap">Investments</a>
<a href="https://readdy.ai/home/<USER>/366da360-7d43-403a-9e4a-f7a792963011" data-readdy="true" className="border-indigo-500 text-indigo-600 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium whitespace-nowrap">Reports</a>
</nav>
</div>
<div className="flex items-center">
<div className="flex-shrink-0">
<div className="relative rounded-md shadow-sm">
<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
<i className="fas fa-search text-gray-400"></i>
</div>
<input
type="text"
className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 pr-3 py-2 border-gray-300 rounded-md text-sm"
placeholder="Search..."
/>
</div>
</div>
<button className="ml-4 relative p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<span className="sr-only">View notifications</span>
<i className="fas fa-bell"></i>
<span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500"></span>
</button>
<div className="ml-4 relative flex-shrink-0">
<div>
<button
onClick={() => setIsProfileDropdownOpen(!isProfileDropdownOpen)}
className="bg-white rounded-full flex text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button"
>
<span className="sr-only">Open user menu</span>
<img
className="h-8 w-8 rounded-full"
src="https://readdy.ai/api/search-image?query=Professional%2520headshot%2520of%2520a%2520male%2520investment%2520banker%2520in%2520a%2520suit%2520with%2520a%2520neutral%2520background%252C%2520business%2520attire%252C%2520confident%2520expression%252C%2520high%2520quality%2520portrait%2520photo&width=32&height=32&seq=2&orientation=squarish"
alt=""
/>
</button>
</div>
{isProfileDropdownOpen && (
<div className="absolute right-0 z-10 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5">
<a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
<a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
<a href="#" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
</div>
)}

{/* Toast Notification */}
{showToast && (
  <div className={`fixed bottom-4 right-4 flex items-center ${toastMessage.type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white px-4 py-3 rounded-lg shadow-lg`}>
    <div className="flex items-center">
      <i className={`mr-2 fas ${toastMessage.type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}`}></i>
      <span>{toastMessage.message}</span>
      {toastMessage.link && (
        <a href={toastMessage.link} className="ml-2 underline">
          View Report
        </a>
      )}
    </div>
    <button 
      onClick={() => setShowToast(false)}
      className="ml-4 text-white hover:text-gray-100 focus:outline-none"
    >
      <i className="fas fa-times"></i>
    </button>
  </div>
)}
</div>
</div>
</div>
</div>
</header>
<main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
{/* Page Header */}
<div className="md:flex md:items-center md:justify-between mb-8">
<div className="flex-1 min-w-0">
<div className="flex items-center">
<a href="https://readdy.ai/home/<USER>/366da360-7d43-403a-9e4a-f7a792963011" data-readdy="true" className="mr-2 text-gray-500 hover:text-gray-700 cursor-pointer">
<i className="fas fa-arrow-left"></i>
</a>
<h1 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
Reports Dashboard
</h1>
</div>
<p className="mt-1 text-sm text-gray-500">
Thursday, May 1, 2025
</p>
</div>
<div className="mt-4 flex md:mt-0 md:ml-4">
<button
type="button"
className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button"
onClick={() => {}}
>
<i className="fas fa-download mr-2"></i>
Export Reports
</button>
<button
type="button"
className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button"
onClick={() => setIsGenerateModalOpen(true)}
>
<i className="fas fa-plus mr-2"></i>
Generate New Report
</button>
</div>
</div>
{/* Reports Control Panel */}
<div className="bg-white shadow rounded-lg mb-8">
<div className="p-6">
<h2 className="text-lg font-medium text-gray-900 mb-4">Filter Reports</h2>
<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
<div>
<label htmlFor="start-date" className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
<input
type="date"
id="start-date"
className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
value={startDate}
onChange={(e) => setStartDate(e.target.value)}
/>
</div>
<div>
<label htmlFor="end-date" className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
<input
type="date"
id="end-date"
className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
value={endDate}
onChange={(e) => setEndDate(e.target.value)}
/>
</div>
<div>
<label htmlFor="client" className="block text-sm font-medium text-gray-700 mb-1">Client</label>
<div className="relative">
<select
id="client"
className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md appearance-none pr-10"
value={selectedClient}
onChange={(e) => setSelectedClient(e.target.value)}
>
<option value="">All Clients</option>
<option value="morgan-stanley">Morgan Stanley</option>
<option value="goldman-sachs">Goldman Sachs</option>
<option value="jp-morgan">JP Morgan</option>
<option value="blackrock">Blackrock</option>
<option value="bank-of-america">Bank of America</option>
</select>
<div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
<i className="fas fa-chevron-down text-xs"></i>
</div>
</div>
</div>
<div>
<label htmlFor="report-type" className="block text-sm font-medium text-gray-700 mb-1">Report Type</label>
<div className="relative">
<select
id="report-type"
className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md appearance-none pr-10"
value={activeReportType}
onChange={(e) => setActiveReportType(e.target.value)}
>
<option value="all">All Reports</option>
<option value="performance">Performance Reports</option>
<option value="client">Client Reports</option>
<option value="investment">Investment Reports</option>
<option value="compliance">Compliance Reports</option>
<option value="regulatory">Regulatory Reports</option>
</select>
<div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
<i className="fas fa-chevron-down text-xs"></i>
</div>
</div>
</div>
</div>
<div className="mt-4 flex justify-end">
<button
type="button"
className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button"
>
<i className="fas fa-sync-alt mr-2"></i>
Reset Filters
</button>
<button
type="button"
className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button"
>
<i className="fas fa-search mr-2"></i>
Apply Filters
</button>
</div>
</div>
</div>
{/* Report Categories */}
<div className="mb-8">
<h2 className="text-lg font-medium text-gray-900 mb-4">Report Categories</h2>
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
{/* Performance Reports */}
<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
<div className="p-6">
<div className="flex items-center">
<div className="flex-shrink-0 bg-blue-100 rounded-md p-3">
<i className="fas fa-chart-line text-blue-600 text-xl"></i>
</div>
<div className="ml-4">
<h3 className="text-lg font-medium text-gray-900">Performance Reports</h3>
<div className="mt-1 flex items-baseline">
<p className="text-2xl font-semibold text-gray-900">35</p>
<p className="ml-2 text-sm text-gray-500">reports</p>
</div>
</div>
</div>
<div className="mt-4 text-sm text-gray-500">
Last generated: Today at 10:30 AM
</div>
<div className="mt-4 flex space-x-2">
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye mr-1"></i> View
</button>
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-plus mr-1"></i> New
</button>
</div>
</div>
</div>
{/* Client Reports */}
<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
<div className="p-6">
<div className="flex items-center">
<div className="flex-shrink-0 bg-green-100 rounded-md p-3">
<i className="fas fa-user-tie text-green-600 text-xl"></i>
</div>
<div className="ml-4">
<h3 className="text-lg font-medium text-gray-900">Client Reports</h3>
<div className="mt-1 flex items-baseline">
<p className="text-2xl font-semibold text-gray-900">30</p>
<p className="ml-2 text-sm text-gray-500">reports</p>
</div>
</div>
</div>
<div className="mt-4 text-sm text-gray-500">
Last generated: Yesterday at 4:15 PM
</div>
<div className="mt-4 flex space-x-2">
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye mr-1"></i> View
</button>
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-plus mr-1"></i> New
</button>
</div>
</div>
</div>
{/* Investment Reports */}
<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
<div className="p-6">
<div className="flex items-center">
<div className="flex-shrink-0 bg-purple-100 rounded-md p-3">
<i className="fas fa-briefcase text-purple-600 text-xl"></i>
</div>
<div className="ml-4">
<h3 className="text-lg font-medium text-gray-900">Investment Reports</h3>
<div className="mt-1 flex items-baseline">
<p className="text-2xl font-semibold text-gray-900">20</p>
<p className="ml-2 text-sm text-gray-500">reports</p>
</div>
</div>
</div>
<div className="mt-4 text-sm text-gray-500">
Last generated: Apr 29, 2025 at 2:00 PM
</div>
<div className="mt-4 flex space-x-2">
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-purple-700 bg-purple-100 hover:bg-purple-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye mr-1"></i> View
</button>
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-plus mr-1"></i> New
</button>
</div>
</div>
</div>
{/* Compliance Reports */}
<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
<div className="p-6">
<div className="flex items-center">
<div className="flex-shrink-0 bg-yellow-100 rounded-md p-3">
<i className="fas fa-shield-alt text-yellow-600 text-xl"></i>
</div>
<div className="ml-4">
<h3 className="text-lg font-medium text-gray-900">Compliance Reports</h3>
<div className="mt-1 flex items-baseline">
<p className="text-2xl font-semibold text-gray-900">15</p>
<p className="ml-2 text-sm text-gray-500">reports</p>
</div>
</div>
</div>
<div className="mt-4 text-sm text-gray-500">
Last generated: Apr 28, 2025 at 11:30 AM
</div>
<div className="mt-4 flex space-x-2">
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye mr-1"></i> View
</button>
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-plus mr-1"></i> New
</button>
</div>
</div>
</div>
<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
<div className="p-6">
<div className="flex items-center">
<div className="flex-shrink-0 bg-red-100 rounded-md p-3">
<i className="fas fa-balance-scale text-red-600 text-xl"></i>
</div>
<div className="ml-4">
<h3 className="text-lg font-medium text-gray-900">Regulatory Reports</h3>
<div className="mt-1 flex items-baseline">
<p className="text-2xl font-semibold text-gray-900">12</p>
<p className="ml-2 text-sm text-gray-500">reports</p>
</div>
</div>
</div>
<div className="mt-4 text-sm text-gray-500">
Last generated: Apr 27, 2025 at 9:15 AM
</div>
<div className="mt-4 flex space-x-2">
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye mr-1"></i> View
</button>
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-gray-700 bg-gray-100 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-plus mr-1"></i> New
</button>
</div>
</div>
</div>
</div>
</div>
{/* Analytics Overview */}
<div className="mb-8">
<h2 className="text-lg font-medium text-gray-900 mb-4">Reports Analytics</h2>
<div className="bg-white shadow rounded-lg">
<div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-6">
<div className="h-64">
<h3 className="text-sm font-medium text-gray-500 mb-2">Report Generation Trends</h3>
<div id="performance-trend-chart" className="w-full h-56"></div>
</div>
<div className="h-64">
<h3 className="text-sm font-medium text-gray-500 mb-2">Report Type Distribution</h3>
<div id="report-distribution-chart" className="w-full h-56"></div>
</div>
</div>
</div>
</div>
{/* Recent Reports */}
<div className="mb-8">
<div className="flex justify-between items-center mb-4">
<h2 className="text-lg font-medium text-gray-900">Recent Reports</h2>
<div className="flex items-center space-x-2">
<button
onClick={() => setViewMode('table')}
className={`p-2 rounded-md ${viewMode === 'table' ? 'bg-gray-200 text-gray-800' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'} cursor-pointer whitespace-nowrap !rounded-button`}
>
<i className="fas fa-list"></i>
</button>
<button
onClick={() => setViewMode('grid')}
className={`p-2 rounded-md ${viewMode === 'grid' ? 'bg-gray-200 text-gray-800' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'} cursor-pointer whitespace-nowrap !rounded-button`}
>
<i className="fas fa-th-large"></i>
</button>
</div>
</div>
{viewMode === 'table' ? (
<div className="bg-white shadow overflow-hidden sm:rounded-lg">
<div className="overflow-x-auto">
<table className="min-w-full divide-y divide-gray-200">
<thead className="bg-gray-50">
<tr>
<th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
Report Name
</th>
<th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
Type
</th>
<th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
Generated Date
</th>
<th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
Status
</th>
<th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
Size
</th>
<th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
Actions
</th>
</tr>
</thead>
<tbody className="bg-white divide-y divide-gray-200">
<tr>
<td className="px-6 py-4 whitespace-nowrap">
<div className="flex items-center">
<div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-md flex items-center justify-center">
<i className="fas fa-file-pdf text-blue-600"></i>
</div>
<div className="ml-4">
<div className="text-sm font-medium text-gray-900">Q1 Performance Summary</div>
<div className="text-sm text-gray-500">Morgan Stanley</div>
</div>
</div>
</td>
<td className="px-6 py-4 whitespace-nowrap">
<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
Performance
</span>
</td>
<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
May 1, 2025
</td>
<td className="px-6 py-4 whitespace-nowrap">
<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
Completed
</span>
</td>
<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
4.2 MB
</td>
<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
<div className="flex justify-end space-x-2">
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye"></i>
</button>
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-download"></i>
</button>
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-share-alt"></i>
</button>
<button className="text-gray-500 hover:text-gray-700 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-ellipsis-v"></i>
</button>
</div>
</td>
</tr>
<tr>
<td className="px-6 py-4 whitespace-nowrap">
<div className="flex items-center">
<div className="flex-shrink-0 h-10 w-10 bg-green-100 rounded-md flex items-center justify-center">
<i className="fas fa-file-excel text-green-600"></i>
</div>
<div className="ml-4">
<div className="text-sm font-medium text-gray-900">Client Portfolio Analysis</div>
<div className="text-sm text-gray-500">Goldman Sachs</div>
</div>
</div>
</td>
<td className="px-6 py-4 whitespace-nowrap">
<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
Client
</span>
</td>
<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
Apr 30, 2025
</td>
<td className="px-6 py-4 whitespace-nowrap">
<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
Completed
</span>
</td>
<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
3.8 MB
</td>
<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
<div className="flex justify-end space-x-2">
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye"></i>
</button>
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-download"></i>
</button>
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-share-alt"></i>
</button>
<button className="text-gray-500 hover:text-gray-700 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-ellipsis-v"></i>
</button>
</div>
</td>
</tr>
<tr>
<td className="px-6 py-4 whitespace-nowrap">
<div className="flex items-center">
<div className="flex-shrink-0 h-10 w-10 bg-purple-100 rounded-md flex items-center justify-center">
<i className="fas fa-file-alt text-purple-600"></i>
</div>
<div className="ml-4">
<div className="text-sm font-medium text-gray-900">Investment Strategy Report</div>
<div className="text-sm text-gray-500">JP Morgan</div>
</div>
</div>
</td>
<td className="px-6 py-4 whitespace-nowrap">
<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
Investment
</span>
</td>
<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
Apr 29, 2025
</td>
<td className="px-6 py-4 whitespace-nowrap">
<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
Completed
</span>
</td>
<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
5.1 MB
</td>
<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
<div className="flex justify-end space-x-2">
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye"></i>
</button>
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-download"></i>
</button>
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-share-alt"></i>
</button>
<button className="text-gray-500 hover:text-gray-700 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-ellipsis-v"></i>
</button>
</div>
</td>
</tr>
<tr>
<td className="px-6 py-4 whitespace-nowrap">
<div className="flex items-center">
<div className="flex-shrink-0 h-10 w-10 bg-yellow-100 rounded-md flex items-center justify-center">
<i className="fas fa-file-pdf text-yellow-600"></i>
</div>
<div className="ml-4">
<div className="text-sm font-medium text-gray-900">Regulatory Compliance Review</div>
<div className="text-sm text-gray-500">Blackrock</div>
</div>
</div>
</td>
<td className="px-6 py-4 whitespace-nowrap">
<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
Compliance
</span>
</td>
<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
Apr 28, 2025
</td>
<td className="px-6 py-4 whitespace-nowrap">
<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
Completed
</span>
</td>
<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
6.3 MB
</td>
<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
<div className="flex justify-end space-x-2">
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye"></i>
</button>
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-download"></i>
</button>
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-share-alt"></i>
</button>
<button className="text-gray-500 hover:text-gray-700 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-ellipsis-v"></i>
</button>
</div>
</td>
</tr>
<tr>
<td className="px-6 py-4 whitespace-nowrap">
<div className="flex items-center">
<div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-md flex items-center justify-center">
<i className="fas fa-file-csv text-blue-600"></i>
</div>
<div className="ml-4">
<div className="text-sm font-medium text-gray-900">Monthly Performance Metrics</div>
<div className="text-sm text-gray-500">Bank of America</div>
</div>
</div>
</td>
<td className="px-6 py-4 whitespace-nowrap">
<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
Performance
</span>
</td>
<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
Apr 27, 2025
</td>
<td className="px-6 py-4 whitespace-nowrap">
<span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
Completed
</span>
</td>
<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
2.7 MB
</td>
<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
<div className="flex justify-end space-x-2">
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye"></i>
</button>
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-download"></i>
</button>
<button className="text-indigo-600 hover:text-indigo-900 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-share-alt"></i>
</button>
<button className="text-gray-500 hover:text-gray-700 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-ellipsis-v"></i>
</button>
</div>
</td>
</tr>
</tbody>
</table>
</div>
<div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
<div className="flex-1 flex justify-between sm:hidden">
<button className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 cursor-pointer whitespace-nowrap !rounded-button">
Previous
</button>
<button className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 cursor-pointer whitespace-nowrap !rounded-button">
Next
</button>
</div>
<div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
<div>
<p className="text-sm text-gray-700">
Showing <span className="font-medium">1</span> to <span className="font-medium">5</span> of <span className="font-medium">25</span> results
</p>
</div>
<div>
<nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
<button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 cursor-pointer whitespace-nowrap !rounded-button">
<span className="sr-only">Previous</span>
<i className="fas fa-chevron-left text-xs"></i>
</button>
<button
className="relative inline-flex items-center px-4 py-2 border border-indigo-500 bg-indigo-50 text-sm font-medium text-indigo-600 cursor-pointer whitespace-nowrap !rounded-button"
onClick={() => setCurrentPage(1)}
>
1
</button>
<button
className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer whitespace-nowrap !rounded-button"
onClick={() => setCurrentPage(2)}
>
2
</button>
<button
className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer whitespace-nowrap !rounded-button"
onClick={() => setCurrentPage(3)}
>
3
</button>
<button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 cursor-pointer whitespace-nowrap !rounded-button">
<span className="sr-only">Next</span>
<i className="fas fa-chevron-right text-xs"></i>
</button>
</nav>
</div>
</div>
</div>
</div>
) : (
<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
{/* Grid view items */}
<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
<div className="p-4 border-b border-gray-200">
<div className="flex justify-between items-start">
<div className="flex items-center">
<div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-md flex items-center justify-center">
<i className="fas fa-file-pdf text-blue-600"></i>
</div>
<div className="ml-3">
<h3 className="text-sm font-medium text-gray-900">Q1 Performance Summary</h3>
<p className="text-xs text-gray-500">Morgan Stanley</p>
</div>
</div>
<span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
Performance
</span>
</div>
</div>
<div className="p-4">
<div className="flex justify-between text-sm text-gray-500 mb-4">
<div>Generated: May 1, 2025</div>
<div>4.2 MB</div>
</div>
<div className="h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
<i className="fas fa-file-pdf text-gray-400 text-4xl"></i>
</div>
<div className="flex justify-between">
<button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye mr-1"></i> Preview
</button>
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-download mr-1"></i> Download
</button>
</div>
</div>
</div>
<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
<div className="p-4 border-b border-gray-200">
<div className="flex justify-between items-start">
<div className="flex items-center">
<div className="flex-shrink-0 h-10 w-10 bg-green-100 rounded-md flex items-center justify-center">
<i className="fas fa-file-excel text-green-600"></i>
</div>
<div className="ml-3">
<h3 className="text-sm font-medium text-gray-900">Client Portfolio Analysis</h3>
<p className="text-xs text-gray-500">Goldman Sachs</p>
</div>
</div>
<span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
Client
</span>
</div>
</div>
<div className="p-4">
<div className="flex justify-between text-sm text-gray-500 mb-4">
<div>Generated: Apr 30, 2025</div>
<div>3.8 MB</div>
</div>
<div className="h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
<i className="fas fa-file-excel text-gray-400 text-4xl"></i>
</div>
<div className="flex justify-between">
<button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye mr-1"></i> Preview
</button>
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-download mr-1"></i> Download
</button>
</div>
</div>
</div>
<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
<div className="p-4 border-b border-gray-200">
<div className="flex justify-between items-start">
<div className="flex items-center">
<div className="flex-shrink-0 h-10 w-10 bg-purple-100 rounded-md flex items-center justify-center">
<i className="fas fa-file-alt text-purple-600"></i>
</div>
<div className="ml-3">
<h3 className="text-sm font-medium text-gray-900">Investment Strategy Report</h3>
<p className="text-xs text-gray-500">JP Morgan</p>
</div>
</div>
<span className="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
Investment
</span>
</div>
</div>
<div className="p-4">
<div className="flex justify-between text-sm text-gray-500 mb-4">
<div>Generated: Apr 29, 2025</div>
<div>5.1 MB</div>
</div>
<div className="h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
<i className="fas fa-file-alt text-gray-400 text-4xl"></i>
</div>
<div className="flex justify-between">
<button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye mr-1"></i> Preview
</button>
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-download mr-1"></i> Download
</button>
</div>
</div>
</div>
<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
<div className="p-4 border-b border-gray-200">
<div className="flex justify-between items-start">
<div className="flex items-center">
<div className="flex-shrink-0 h-10 w-10 bg-yellow-100 rounded-md flex items-center justify-center">
<i className="fas fa-file-pdf text-yellow-600"></i>
</div>
<div className="ml-3">
<h3 className="text-sm font-medium text-gray-900">Regulatory Compliance Review</h3>
<p className="text-xs text-gray-500">Blackrock</p>
</div>
</div>
<span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
Compliance
</span>
</div>
</div>
<div className="p-4">
<div className="flex justify-between text-sm text-gray-500 mb-4">
<div>Generated: Apr 28, 2025</div>
<div>6.3 MB</div>
</div>
<div className="h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
<i className="fas fa-file-pdf text-gray-400 text-4xl"></i>
</div>
<div className="flex justify-between">
<button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye mr-1"></i> Preview
</button>
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-download mr-1"></i> Download
</button>
</div>
</div>
</div>
<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
<div className="p-4 border-b border-gray-200">
<div className="flex justify-between items-start">
<div className="flex items-center">
<div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-md flex items-center justify-center">
<i className="fas fa-file-csv text-blue-600"></i>
</div>
<div className="ml-3">
<h3 className="text-sm font-medium text-gray-900">Monthly Performance Metrics</h3>
<p className="text-xs text-gray-500">Bank of America</p>
</div>
</div>
<span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
Performance
</span>
</div>
</div>
<div className="p-4">
<div className="flex justify-between text-sm text-gray-500 mb-4">
<div>Generated: Apr 27, 2025</div>
<div>2.7 MB</div>
</div>
<div className="h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
<i className="fas fa-file-csv text-gray-400 text-4xl"></i>
</div>
<div className="flex justify-between">
<button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye mr-1"></i> Preview
</button>
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-download mr-1"></i> Download
</button>
</div>
</div>
</div>
<div className="bg-white shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-300">
<div className="p-4 border-b border-gray-200">
<div className="flex justify-between items-start">
<div className="flex items-center">
<div className="flex-shrink-0 h-10 w-10 bg-green-100 rounded-md flex items-center justify-center">
<i className="fas fa-file-excel text-green-600"></i>
</div>
<div className="ml-3">
<h3 className="text-sm font-medium text-gray-900">Client Acquisition Report</h3>
<p className="text-xs text-gray-500">Citigroup</p>
</div>
</div>
<span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
Client
</span>
</div>
</div>
<div className="p-4">
<div className="flex justify-between text-sm text-gray-500 mb-4">
<div>Generated: Apr 26, 2025</div>
<div>3.1 MB</div>
</div>
<div className="h-32 bg-gray-100 rounded-md flex items-center justify-center mb-4">
<i className="fas fa-file-excel text-gray-400 text-4xl"></i>
</div>
<div className="flex justify-between">
<button className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-eye mr-1"></i> Preview
</button>
<button className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 cursor-pointer whitespace-nowrap !rounded-button">
<i className="fas fa-download mr-1"></i> Download
</button>
</div>
</div>
</div>
</div>
)}

{/* Toast Notification */}
{showToast && (
  <div className={`fixed bottom-4 right-4 flex items-center ${toastMessage.type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white px-4 py-3 rounded-lg shadow-lg`}>
    <div className="flex items-center">
      <i className={`mr-2 fas ${toastMessage.type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}`}></i>
      <span>{toastMessage.message}</span>
      {toastMessage.link && (
        <a href={toastMessage.link} className="ml-2 underline">
          View Report
        </a>
      )}
    </div>
    <button 
      onClick={() => setShowToast(false)}
      className="ml-4 text-white hover:text-gray-100 focus:outline-none"
    >
      <i className="fas fa-times"></i>
    </button>
  </div>
)}
</div>
</main>
{/* Footer */}
<footer className="bg-white border-t border-gray-200">
<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
<div className="md:flex md:items-center md:justify-between">
<div className="flex space-x-6 md:order-2">
<a href="#" className="text-gray-400 hover:text-gray-500">
<span className="sr-only">Help Center</span>
<i className="fas fa-question-circle"></i>
</a>
<a href="#" className="text-gray-400 hover:text-gray-500">
<span className="sr-only">Settings</span>
<i className="fas fa-cog"></i>
</a>
<a href="#" className="text-gray-400 hover:text-gray-500">
<span className="sr-only">Support</span>
<i className="fas fa-headset"></i>
</a>
</div>
<div className="mt-8 md:mt-0 md:order-1 flex flex-col sm:flex-row sm:items-center">
<div className="flex items-center text-sm text-gray-500">
<i className="fas fa-circle text-green-500 mr-1 text-xs"></i>
System Status: Online
</div>
<div className="mt-2 sm:mt-0 sm:ml-4 text-sm text-gray-500">
Last synced: Today at 12:45 PM
</div>
</div>
</div>
<div className="mt-4 border-t border-gray-200 pt-4">
<p className="text-sm text-gray-500">&copy; 2025 Dashing CRM. All rights reserved.</p>
</div>
</div>
</footer>
{/* Generate New Report Modal */}
{isGenerateModalOpen && (
<div className="fixed z-10 inset-0 overflow-y-auto">
<div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
<div className="fixed inset-0 transition-opacity" aria-hidden="true">
<div className="absolute inset-0 bg-gray-500 opacity-75"></div>
</div>
<span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
<div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
<div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
<div className="sm:flex sm:items-start">
<div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 sm:mx-0 sm:h-10 sm:w-10">
<i className="fas fa-file-plus text-indigo-600"></i>
</div>
<div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
<h3 className="text-lg leading-6 font-medium text-gray-900">
Generate New Report
</h3>
<div className="mt-4">
<div className="mb-4">
<label htmlFor="report-template" className="block text-sm font-medium text-gray-700 mb-1">Report Template</label>
<div className="relative">
<select
id="report-template"
className={`shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md appearance-none pr-10 ${formErrors.template ? 'border-red-500' : ''}`}
>
<option value="">Select a template</option>
<option value="performance-summary">Performance Summary</option>
<option value="client-portfolio">Client Portfolio Analysis</option>
<option value="investment-strategy">Investment Strategy</option>
<option value="compliance-review">Compliance Review</option>
<option value="regulatory-filing">Regulatory Filing</option>
<option value="regulatory-disclosure">Regulatory Disclosure</option>
<option value="custom">Custom Report</option>
</select>
<div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
<i className="fas fa-chevron-down text-xs"></i>
</div>
</div>
</div>
<div className="mb-4">
<label htmlFor="report-name" className="block text-sm font-medium text-gray-700 mb-1">Report Name</label>
<input
type="text"
id="report-name"
className={`shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md ${formErrors.name ? 'border-red-500' : ''}`}
placeholder="Enter report name"
/>
{formErrors.name && <p className="mt-1 text-sm text-red-500">{formErrors.name}</p>}
</div>
<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
<div>
<label htmlFor="modal-start-date" className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
<input
type="date"
id="modal-start-date"
className={`shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md ${formErrors.startDate ? 'border-red-500' : ''}`}
/>
{formErrors.startDate && <p className="mt-1 text-sm text-red-500">{formErrors.startDate}</p>}
</div>
<div>
<label htmlFor="modal-end-date" className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
<input
type="date"
id="modal-end-date"
className={`shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md ${formErrors.endDate ? 'border-red-500' : ''}`}
/>
{formErrors.endDate && <p className="mt-1 text-sm text-red-500">{formErrors.endDate}</p>}
</div>
</div>
<div className="mb-4">
<label htmlFor="modal-client" className="block text-sm font-medium text-gray-700 mb-1">Client</label>
<div className="relative">
<select
id="modal-client"
className={`shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md appearance-none pr-10 ${formErrors.client ? 'border-red-500' : ''}`}
>
<option value="">Select a client</option>
<option value="morgan-stanley">Morgan Stanley</option>
<option value="goldman-sachs">Goldman Sachs</option>
<option value="jp-morgan">JP Morgan</option>
<option value="blackrock">Blackrock</option>
<option value="bank-of-america">Bank of America</option>
</select>
<div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
<i className="fas fa-chevron-down text-xs"></i>
</div>
</div>
</div>
<div className="mb-4">
<label className="block text-sm font-medium text-gray-700 mb-1">Format</label>
<div className="flex space-x-4">
<div className="flex items-center">
<input
id="format-pdf"
name="format"
type="radio"
className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
checked={selectedFormat === 'pdf'}
onChange={() => setSelectedFormat('pdf')}
/>
<label htmlFor="format-pdf" className="ml-2 block text-sm text-gray-700">
PDF
</label>
</div>
<div className="flex items-center">
<input
id="format-excel"
name="format"
type="radio"
className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
checked={selectedFormat === 'excel'}
onChange={() => setSelectedFormat('excel')}
/>
<label htmlFor="format-excel" className="ml-2 block text-sm text-gray-700">
Excel
</label>
</div>
<div className="flex items-center">
<input
id="format-csv"
name="format"
type="radio"
className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
checked={selectedFormat === 'csv'}
onChange={() => setSelectedFormat('csv')}
/>
<label htmlFor="format-csv" className="ml-2 block text-sm text-gray-700">
CSV
</label>
</div>
<div className="flex items-center">
<input
id="format-dashboard"
name="format"
type="radio"
className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
checked={selectedFormat === 'dashboard'}
onChange={() => setSelectedFormat('dashboard')}
/>
<label htmlFor="format-dashboard" className="ml-2 block text-sm text-gray-700">
Dashboard
</label>
</div>
</div>
</div>
</div>
</div>
</div>
</div>
<div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
<button
type="button"
className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm cursor-pointer whitespace-nowrap !rounded-button"
onClick={handleGenerateReport}
disabled={isGenerating}
>
{isGenerating ? (
  <span className="inline-flex items-center">
    <i className="fas fa-spinner fa-spin mr-2"></i>
    Generating...
  </span>
) : (
  'Generate Report'
)}
</button>
<button
type="button"
className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm cursor-pointer whitespace-nowrap !rounded-button"
onClick={() => setIsGenerateModalOpen(false)}
>
Cancel
</button>
</div>
</div>
</div>
</div>
)}

{/* Toast Notification */}
{showToast && (
  <div className={`fixed bottom-4 right-4 flex items-center ${toastMessage.type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white px-4 py-3 rounded-lg shadow-lg`}>
    <div className="flex items-center">
      <i className={`mr-2 fas ${toastMessage.type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}`}></i>
      <span>{toastMessage.message}</span>
      {toastMessage.link && (
        <a href={toastMessage.link} className="ml-2 underline">
          View Report
        </a>
      )}
    </div>
    <button 
      onClick={() => setShowToast(false)}
      className="ml-4 text-white hover:text-gray-100 focus:outline-none"
    >
      <i className="fas fa-times"></i>
    </button>
  </div>
)}
</div>
);
};
export default App