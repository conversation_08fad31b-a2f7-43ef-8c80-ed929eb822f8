/**
 * Theme configuration for JustPrototype
 * Based on a modern CSS variables approach with HSL colors
 * for better theming capabilities and design consistency
 */

const theme = {
  container: {
    center: true,
    padding: '2rem',
    screens: {
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1400px'
    }
  },
  colors: {
    // Base colors
    border: 'hsl(var(--border))',
    input: 'hsl(var(--input))',
    ring: 'hsl(var(--ring))',
    background: 'hsl(var(--background))',
    foreground: 'hsl(var(--foreground))',
    
    // Primary colors
    primary: {
      DEFAULT: 'hsl(var(--primary))',
      foreground: 'hsl(var(--primary-foreground))',
      light: 'hsl(var(--primary-light))',
      dark: 'hsl(var(--primary-dark))'
    },
    
    // Secondary colors
    secondary: {
      DEFAULT: 'hsl(var(--secondary))',
      foreground: 'hsl(var(--secondary-foreground))',
      light: 'hsl(var(--secondary-light))',
      dark: 'hsl(var(--secondary-dark))'
    },
    
    // Accent colors
    accent: {
      DEFAULT: 'hsl(var(--accent))',
      foreground: 'hsl(var(--accent-foreground))',
      light: 'hsl(var(--accent-light))',
      dark: 'hsl(var(--accent-dark))'
    },
    
    // Status colors
    success: {
      DEFAULT: 'hsl(var(--success))',
      foreground: 'hsl(var(--success-foreground))'
    },
    warning: {
      DEFAULT: 'hsl(var(--warning))',
      foreground: 'hsl(var(--warning-foreground))'
    },
    destructive: {
      DEFAULT: 'hsl(var(--destructive))',
      foreground: 'hsl(var(--destructive-foreground))'
    },
    
    // UI component colors
    muted: {
      DEFAULT: 'hsl(var(--muted))',
      foreground: 'hsl(var(--muted-foreground))'
    },
    card: {
      DEFAULT: 'hsl(var(--card))',
      foreground: 'hsl(var(--card-foreground))',
      hover: 'hsl(var(--card-hover))'
    },
    popover: {
      DEFAULT: 'hsl(var(--popover))',
      foreground: 'hsl(var(--popover-foreground))'
    },
    tooltip: {
      DEFAULT: 'hsl(var(--tooltip))',
      foreground: 'hsl(var(--tooltip-foreground))'
    }
  },
  
  // Border radius
  borderRadius: {
    lg: 'var(--radius-lg)',
    md: 'var(--radius-md)',
    sm: 'var(--radius-sm)',
    full: 'var(--radius-full)'
  },
  
  // Shadows
  shadows: {
    sm: 'var(--shadow-sm)',
    md: 'var(--shadow-md)',
    lg: 'var(--shadow-lg)',
    xl: 'var(--shadow-xl)'
  },
  
  // Animations
  keyframes: {
    'fade-in': {
      from: { opacity: 0 },
      to: { opacity: 1 }
    },
    'fade-out': {
      from: { opacity: 1 },
      to: { opacity: 0 }
    },
    'slide-in': {
      from: { transform: 'translateY(10px)', opacity: 0 },
      to: { transform: 'translateY(0)', opacity: 1 }
    },
    'slide-out': {
      from: { transform: 'translateY(0)', opacity: 1 },
      to: { transform: 'translateY(10px)', opacity: 0 }
    },
    'accordion-down': {
      from: { height: 0 },
      to: { height: 'var(--radix-accordion-content-height)' }
    },
    'accordion-up': {
      from: { height: 'var(--radix-accordion-content-height)' },
      to: { height: 0 }
    }
  },
  
  // Animation configurations
  animation: {
    'fade-in': 'fade-in 0.2s ease-in-out',
    'fade-out': 'fade-out 0.2s ease-in-out',
    'slide-in': 'slide-in 0.2s ease-out',
    'slide-out': 'slide-out 0.2s ease-in',
    'accordion-down': 'accordion-down 0.2s ease-out',
    'accordion-up': 'accordion-up 0.2s ease-out'
  }
};

export default theme;
