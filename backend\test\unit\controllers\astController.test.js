const { jest } = require('@jest/globals');
const { mockRequest, mockResponse, mockNext, createMockASTNode } = require('../../test-utils');

// Mock the dependencies
jest.mock('../../services/ast/astStore');
const astStore = require('../../services/ast/astStore');

jest.mock('../../services/llmServiceV2');
const llmService = require('../../services/llmServiceV2');

// Import the controller
const astController = require('../../controllers/astController');

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
  
  // Setup default mock implementations
  astStore.getCurrentSnapshot.mockReturnValue({ 
    snapshotId: 'v1', 
    ast: { id: 'test-ast', type: 'Page', children: [] } 
  });
  
  astStore.applyPatch.mockReturnValue({
    success: true,
    snapshotId: 'v2',
    ast: { id: 'patched-ast' }
  });
  
  astStore.listSnapshots.mockReturnValue([
    { id: 'v1', timestamp: '2023-01-01' },
    { id: 'v2', timestamp: '2023-01-02' }
  ]);
  
  astStore.getSnapshot.mockImplementation((id) => {
    if (id === 'nonexistent') return null;
    return { id, ast: { id: 'test-ast' }, timestamp: '2023-01-01' };
  });
});

describe('AST Controller', () => {
  let req, res, next;
  
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    
    // Setup default request, response, and next mocks
    req = mockRequest();
    res = mockResponse();
    next = mockNext();
  });
  
  describe('getCurrentAST', () => {
    it('should return the current AST snapshot', async () => {
      // Mock the AST store to return a test snapshot
      const mockAST = { id: 'test-ast', type: 'Page', children: [] };
      astStore.getCurrentSnapshot.mockReturnValue({ 
        snapshotId: 'v1', 
        ast: mockAST 
      });
      
      // Call the controller method
      await astController.getCurrentAST(req, res);
      
      // Verify the response
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        snapshotId: 'v1',
        ast: mockAST
      });
    });
    
    it('should handle errors', async () => {
      // Mock the AST store to throw an error
      const error = new Error('Test error');
      astStore.getCurrentSnapshot.mockImplementation(() => {
        throw error;
      });
      
      // Call the controller method
      await astController.getCurrentAST(req, res);
      
      // Verify error handling
      expect(res.status).toHaveBeenCalledWith(500);
      expect(res.json).toHaveBeenCalledWith({ 
        error: 'Test error' 
      });
    });
  });
  
  describe('generatePlan', () => {
    it('should return 400 if prompt is missing', async () => {
      // Setup request without prompt
      req.body = {};
      
      // Call the controller method
      await astController.generatePlan(req, res);
      
      // Verify validation
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ 
        error: 'Prompt is required' 
      });
    });
    
    it('should generate a plan with SSE', async () => {
      // Setup SSE request
      req.body = { prompt: 'Test prompt' };
      req.headers = { accept: 'text/event-stream' };
      req.get = jest.fn().mockReturnValue('test-request-id');
      
      // Mock SSE response methods
      res.setHeader = jest.fn();
      res.flushHeaders = jest.fn();
      res.write = jest.fn().mockReturnValue(true);
      
      // Mock LLM service to simulate streaming
      const mockStream = {
        on: jest.fn((event, callback) => {
          if (event === 'data') {
            // Simulate data events
            callback({ type: 'status', data: 'Generating...' });
            callback({ type: 'result', data: { ast: { id: 'test-ast' } } });
          } else if (event === 'end') {
            // Simulate end event
            callback();
          } else if (event === 'error') {
            // No error in this test case
          }
          return mockStream;
        })
      };
      
      llmService.generatePlan.mockReturnValue(mockStream);
      
      // Call the controller method
      await astController.generatePlan(req, res);
      
      // Verify SSE setup
      expect(res.setHeader).toHaveBeenCalledWith('Content-Type', 'text/event-stream');
      expect(res.setHeader).toHaveBeenCalledWith('Cache-Control', 'no-cache');
      expect(res.setHeader).toHaveBeenCalledWith('Connection', 'keep-alive');
      expect(res.setHeader).toHaveBeenCalledWith('X-Accel-Buffering', 'no');
      
      // Verify SSE events were written
      expect(res.write).toHaveBeenCalledWith('event: start\ndata: ' + 
        JSON.stringify({ requestId: 'test-request-id', status: 'started' }) + '\n\n');
      
      // Verify LLM service was called with correct parameters
      expect(llmService.generatePlan).toHaveBeenCalledWith(
        'Test prompt',
        'openai', // Default provider
        'test-request-id',
        expect.any(Function) // onToken callback
      );
    });
  });
  
  describe('patchAST', () => {
    it('should apply a patch to the AST', async () => {
      // Setup request with patch data
      const patch = {
        snapshotId: 'v1',
        operations: [
          { op: 'add', path: '/children/0', value: { type: 'div' } }
        ]
      };
      req.body = patch;
      
      // Mock the AST store to return success
      astStore.applyPatch.mockReturnValue({
        success: true,
        snapshotId: 'v2',
        ast: { id: 'patched-ast' }
      });
      
      // Call the controller method
      await astController.patchAST(req, res);
      
      // Verify the response
      expect(astStore.applyPatch).toHaveBeenCalledWith(patch.snapshotId, patch.operations);
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        snapshotId: 'v2',
        ast: { id: 'patched-ast' }
      });
    });
    
    it('should handle patch errors', async () => {
      // Setup request with invalid patch
      req.body = { snapshotId: 'v1', operations: [] };
      
      // Mock the AST store to throw an error
      const error = new Error('Invalid patch');
      astStore.applyPatch.mockImplementation(() => {
        throw error;
      });
      
      // Call the controller method
      await astController.patchAST(req, res);
      
      // Verify error handling
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({ 
        error: 'Invalid patch' 
      });
    });
  });
  
  describe('listSnapshots', () => {
    it('should return a list of snapshots', async () => {
      // Mock the AST store to return a list of snapshots
      const snapshots = [
        { id: 'v1', timestamp: '2023-01-01' },
        { id: 'v2', timestamp: '2023-01-02' }
      ];
      astStore.listSnapshots.mockReturnValue(snapshots);
      
      // Call the controller method
      await astController.listSnapshots(req, res);
      
      // Verify the response
      expect(astStore.listSnapshots).toHaveBeenCalled();
      expect(res.json).toHaveBeenCalledWith(snapshots);
    });
  });
  
  describe('getSnapshot', () => {
    it('should return a specific snapshot', async () => {
      // Setup request with snapshot ID
      req.params = { id: 'v1' };
      
      // Mock the AST store to return a snapshot
      const snapshot = { 
        id: 'v1', 
        ast: { id: 'test-ast' },
        timestamp: '2023-01-01'
      };
      astStore.getSnapshot.mockReturnValue(snapshot);
      
      // Call the controller method
      await astController.getSnapshot(req, res);
      
      // Verify the response
      expect(astStore.getSnapshot).toHaveBeenCalledWith('v1');
      expect(res.json).toHaveBeenCalledWith(snapshot);
    });
    
    it('should return 404 for non-existent snapshot', async () => {
      // Setup request with non-existent snapshot ID
      req.params = { id: 'nonexistent' };
      
      // Mock the AST store to return null
      astStore.getSnapshot.mockReturnValue(null);
      
      // Call the controller method
      await astController.getSnapshot(req, res);
      
      // Verify the response
      expect(res.status).toHaveBeenCalledWith(404);
      expect(res.json).toHaveBeenCalledWith({ 
        error: 'Snapshot not found' 
      });
    });
  });
});
