-- PostgreSQL schema for prototype versions
-- This table stores all versions of prototypes to track changes over time

CREATE TABLE IF NOT EXISTS prototype_versions (
    id SERIAL PRIMARY KEY,
    prototype_id INTEGER NOT NULL REFERENCES prototypes(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    html TEXT NOT NULL,                    -- HTML content for this version
    css TEXT,                             -- CSS content for this version (if any)
    change_description TEXT,              -- Description of what changed in this version
    operation_type VARCHAR(50) NOT NULL,  -- 'generate', 'edit', 'implement', 'session_edit'
    user_prompt TEXT,                     -- The user's request that led to this version
    llm_response TEXT,                    -- The full LLM response
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure unique version numbers per prototype
    UNIQUE(prototype_id, version_number)
);

-- Index for efficient querying of versions by prototype
CREATE INDEX IF NOT EXISTS idx_prototype_versions_prototype_id ON prototype_versions(prototype_id, version_number DESC);

-- Index for querying by operation type
CREATE INDEX IF NOT EXISTS idx_prototype_versions_operation_type ON prototype_versions(operation_type);

-- Index for querying recent versions
CREATE INDEX IF NOT EXISTS idx_prototype_versions_created_at ON prototype_versions(created_at DESC);

-- Function to automatically increment version number
CREATE OR REPLACE FUNCTION get_next_version_number(p_prototype_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    next_version INTEGER;
BEGIN
    SELECT COALESCE(MAX(version_number), 0) + 1 
    INTO next_version 
    FROM prototype_versions 
    WHERE prototype_id = p_prototype_id;
    
    RETURN next_version;
END;
$$ LANGUAGE plpgsql;

-- Function to create a new version
CREATE OR REPLACE FUNCTION create_prototype_version(
    p_prototype_id INTEGER,
    p_html TEXT,
    p_css TEXT DEFAULT NULL,
    p_change_description TEXT DEFAULT NULL,
    p_operation_type VARCHAR(50) DEFAULT 'edit',
    p_user_prompt TEXT DEFAULT NULL,
    p_llm_response TEXT DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    new_version_number INTEGER;
    new_version_id INTEGER;
BEGIN
    -- Get the next version number
    new_version_number := get_next_version_number(p_prototype_id);
    
    -- Insert the new version
    INSERT INTO prototype_versions (
        prototype_id,
        version_number,
        html,
        css,
        change_description,
        operation_type,
        user_prompt,
        llm_response
    ) VALUES (
        p_prototype_id,
        new_version_number,
        p_html,
        p_css,
        p_change_description,
        p_operation_type,
        p_user_prompt,
        p_llm_response
    ) RETURNING id INTO new_version_id;
    
    RETURN new_version_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get all versions for a prototype
CREATE OR REPLACE FUNCTION get_prototype_versions(p_prototype_id INTEGER)
RETURNS TABLE (
    id INTEGER,
    version_number INTEGER,
    change_description TEXT,
    operation_type VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pv.id,
        pv.version_number,
        pv.change_description,
        pv.operation_type,
        pv.created_at
    FROM prototype_versions pv
    WHERE pv.prototype_id = p_prototype_id
    ORDER BY pv.version_number DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get a specific version
CREATE OR REPLACE FUNCTION get_prototype_version(p_prototype_id INTEGER, p_version_number INTEGER)
RETURNS TABLE (
    id INTEGER,
    version_number INTEGER,
    html TEXT,
    css TEXT,
    change_description TEXT,
    operation_type VARCHAR(50),
    user_prompt TEXT,
    llm_response TEXT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pv.id,
        pv.version_number,
        pv.html,
        pv.css,
        pv.change_description,
        pv.operation_type,
        pv.user_prompt,
        pv.llm_response,
        pv.created_at
    FROM prototype_versions pv
    WHERE pv.prototype_id = p_prototype_id 
    AND pv.version_number = p_version_number;
END;
$$ LANGUAGE plpgsql;

-- End of prototype versions schema.
