const llmService = require('../services/llmServiceV2');
const withSSE = require('../services/sse/withSSE');

/**
 * Generate plan from prompt using LLM
 */
async function generatePlan(req, res, next) {
  try {
    // Basic session check
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt, provider } = req.query;
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    await llmService.generatePlan(prompt, res, provider);
  } catch (error) {
    next(error);
  }
}

/**
 * Generate code from plan using LLM V2
 */
async function generateCode(req, res, next) {
  try {
    // Basic session check
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { plan, provider } = req.body;
    if (!plan) {
      return res.status(400).json({ error: 'Plan is required' });
    }

    await llmService.generateCode(plan, res, provider);
  } catch (error) {
    next(error);
  }
}

/**
 * Modify specific element in HTML content using V2 approach
 */
async function modifyElement(req, res, next) {
  try {
    // Basic session check
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { htmlContent, elementSelector, prompt, provider } = req.body;

    if (!htmlContent || !elementSelector || !prompt) {
      return res.status(400).json({
        error: 'HTML content, element selector, and prompt are required'
      });
    }

    await llmService.modifyElement(htmlContent, elementSelector, prompt, res, provider);
  } catch (error) {
    next(error);
  }
}

/**
 * Modify entire HTML content using V2 approach
 */
async function modifyContent(req, res, next) {
  try {
    // Basic session check
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { htmlContent, prompt, provider } = req.body;

    if (!htmlContent || !prompt) {
      return res.status(400).json({
        error: 'HTML content and prompt are required'
      });
    }

    await llmService.modifyContent(htmlContent, prompt, res, provider);
  } catch (error) {
    next(error);
  }
}

/**
 * Generate code from plan using LLM
 */
async function generateCode(req, res, next) {
  try {
    // Basic session check
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { plan, provider } = req.body;
    if (!plan) {
      return res.status(400).json({ error: 'Plan is required' });
    }

    await llmService.generateCode(plan, res, provider);
  } catch (error) {
    next(error);
  }
}

/**
 * Modify specific element in HTML content
 */
async function modifyElement(req, res, next) {
  try {
    // Basic session check
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { htmlContent, elementSelector, prompt, provider } = req.body;

    if (!htmlContent || !elementSelector || !prompt) {
      return res.status(400).json({
        error: 'HTML content, element selector, and prompt are required'
      });
    }

    await llmService.modifyElement(htmlContent, elementSelector, prompt, res, provider);
  } catch (error) {
    next(error);
  }
}

/**
 * Modify entire HTML content
 */
async function modifyContent(req, res, next) {
  try {
    // Basic session check
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { htmlContent, prompt, provider } = req.body;

    if (!htmlContent || !prompt) {
      return res.status(400).json({
        error: 'HTML content and prompt are required'
      });
    }

    await llmService.modifyContent(htmlContent, prompt, res, provider);
  } catch (error) {
    next(error);
  }
}

/**
 * Generate code with AST from plan using LLM
 */
async function generateCodeWithAST(req, res, next) {
  try {
    // Basic session check
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { plan, provider } = req.query;
    const {
      language = 'html',
      transformations = [],
      format = true,
      streamTokens = false,
      parseAST = true
    } = req.body || {};

    // Validate required parameters
    if (!plan) {
      return res.status(400).json({ error: 'Plan is required' });
    }

    // Parse transformations if provided as string
    let parsedTransformations = transformations;
    if (typeof transformations === 'string') {
      try {
        parsedTransformations = JSON.parse(transformations);
      } catch (error) {
        console.warn('Failed to parse transformations:', error);
        parsedTransformations = [];
      }
    }

    // Set up options
    const options = {
      language,
      transformations: parsedTransformations,
      format,
      parseAST,
      streamTokens
    };

    console.log(`Generating code with options:`, {
      language,
      transformationsCount: parsedTransformations.length,
      format,
      parseAST,
      streamTokens
    });

    await llmService.generateCodeWithAST(plan, res, provider, options);
  } catch (error) {
    next(error);
  }
}

// Export controller methods wrapped with SSE support
module.exports = {
  generatePlan: withSSE(generatePlan),
  generateCodeWithAST: withSSE(generateCodeWithAST)
};
