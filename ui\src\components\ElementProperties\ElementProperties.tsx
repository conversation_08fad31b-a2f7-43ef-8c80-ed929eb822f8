import React, { useState } from 'react';
import { FiSave, FiX, FiChevronDown, FiChevronRight, FiInfo } from 'react-icons/fi';
import styles from './ElementProperties.module.css';

export interface ElementProperty {
  name: string;
  value: string;
  type?: 'text' | 'color' | 'number' | 'select';
  options?: string[]; // For select type
  unit?: string; // For number type (px, %, etc.)
  icon?: 'horizontal' | 'vertical'; // For margin/padding direction
}

export interface ElementPropertiesProps {
  elementType: string;
  elementId?: string;
  elementClass?: string;
  properties?: ElementProperty[];
  onSave: (properties: ElementProperty[]) => void;
  onCancel: () => void;
  inChatWindow?: boolean;
}

export const ElementProperties: React.FC<ElementPropertiesProps> = ({
  elementType,
  elementId,
  elementClass,
  properties = [],
  onSave,
  onCancel,
  inChatWindow = false
}) => {
  // Initialize with default properties if none provided
  const defaultProperties: ElementProperty[] = [
    { name: 'Margin', value: '0', type: 'number', unit: 'px' },
    { name: 'Padding', value: '0', type: 'number', unit: 'px' },
    { name: 'Background', value: 'gray-800', type: 'color' },
    { name: 'Border radius', value: 'None', type: 'select', options: ['None', '4px', '8px', '16px', '24px', 'Full'] }
  ];

  const [editedProperties, setEditedProperties] = useState<ElementProperty[]>(
    properties.length > 0 ? properties : defaultProperties
  );

  const [showAdvanced, setShowAdvanced] = useState(false);
  const [advancedCSS, setAdvancedCSS] = useState(
    'min-h: screen flex items: center justify: center bg: gradient-to: br from: slate-50 to: slate-100 bg: gray-800'
  );

  const handlePropertyChange = (index: number, value: string) => {
    const updatedProperties = [...editedProperties];
    updatedProperties[index] = { ...updatedProperties[index], value };
    setEditedProperties(updatedProperties);
  };

  const handleSave = () => {
    // Include advanced CSS as a property if it's shown
    const allProperties = [...editedProperties];
    if (showAdvanced) {
      allProperties.push({ name: 'advancedCSS', value: advancedCSS });
    }
    onSave(allProperties);
  };

  return (
    <div className={styles.propertiesPanel}>
      <div className={styles.header}>
        <div className={styles.elementInfo}>
          <span className={styles.elementType}>{elementType}</span>
          {elementId && <span className={styles.elementId}>#{elementId}</span>}
          {elementClass && <span className={styles.elementClass}>.{elementClass}</span>}
        </div>
        <button className={styles.closeButton} onClick={onCancel} aria-label="Close">
          <FiX />
        </button>
      </div>

      <div className={styles.propertiesContainer}>
        {/* Basic Properties */}
        {editedProperties.map((property, index) => (
          <div key={index} className={styles.propertyRow}>
            <label className={styles.propertyLabel}>{property.name}</label>
            {property.type === 'color' ? (
              <div className={styles.colorInputContainer}>
                <input
                  type="color"
                  value={property.value.startsWith('#') ? property.value : '#808080'}
                  onChange={(e) => handlePropertyChange(index, e.target.value)}
                  className={styles.colorInput}
                />
                <input
                  type="text"
                  value={property.value}
                  onChange={(e) => handlePropertyChange(index, e.target.value)}
                  className={styles.colorText}
                />
              </div>
            ) : property.type === 'select' && property.options ? (
              <select
                value={property.value}
                onChange={(e) => handlePropertyChange(index, e.target.value)}
                className={styles.selectInput}
              >
                {property.options.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            ) : property.type === 'number' ? (
              <div className={styles.colorInputContainer}>
                <input
                  type="number"
                  value={property.value}
                  onChange={(e) => handlePropertyChange(index, e.target.value)}
                  className={styles.numberInput}
                />
                {property.unit && (
                  <span className={styles.unitLabel}>{property.unit}</span>
                )}
              </div>
            ) : (
              <input
                type="text"
                value={property.value}
                onChange={(e) => handlePropertyChange(index, e.target.value)}
                className={styles.textInput}
              />
            )}
          </div>
        ))}

        {/* Advanced Section */}
        <div className={styles.advancedSection}>
          <div
            className={styles.advancedHeader}
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            {showAdvanced ? <FiChevronDown /> : <FiChevronRight />}
            <span className={styles.advancedTitle}>Advanced</span>
            <FiInfo size={14} title="Enter CSS properties in shorthand format" />
          </div>

          {showAdvanced && (
            <div className={styles.advancedContent}>
              <textarea
                className={styles.cssTextarea}
                value={advancedCSS}
                onChange={(e) => setAdvancedCSS(e.target.value)}
                placeholder="Enter CSS properties in shorthand format"
              />
            </div>
          )}
        </div>
      </div>

      <div className={styles.actions}>
        <button className={styles.saveButton} onClick={handleSave}>
          <FiSave />
          <span>Save</span>
        </button>
        <button className={styles.cancelButton} onClick={onCancel}>
          <FiX />
          <span>Discard</span>
        </button>
      </div>
    </div>
  );
};
