/**
 * Test script to verify prompt robustness implementation
 * Tests the enhanced modal functionality and validation
 */

const llmServiceV3 = require('../services/llmServiceV3');

// Mock HTML content for testing
const testHtmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard</title>
</head>
<body>
    <div class="container">
        <header>
            <h1>Test Dashboard</h1>
            <button class="btn btn-primary" id="generateReportBtn">
                Generate New Report
            </button>
        </header>
    </div>
</body>
</html>
`;

// Test the validation functions
function testValidation() {
    console.log('🧪 Testing Modal Function Validation...');
    
    const service = new llmServiceV3();
    
    // Test 1: HTML without modal functions
    const result1 = service.validateModalFunctions(testHtmlContent);
    console.log('Test 1 - No modal functions:', result1);
    
    // Test 2: HTML with modal functions
    const htmlWithFunctions = testHtmlContent + `
    <script>
    function openModal(modalId) {
        document.getElementById(modalId).style.display = 'block';
    }
    function closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }
    </script>
    `;
    
    const result2 = service.validateModalFunctions(htmlWithFunctions);
    console.log('Test 2 - With modal functions:', result2);
    
    // Test 3: Modal request detection
    const isModal1 = service.isModalRequest('Add modal functionality');
    const isModal2 = service.isModalRequest('Change the color');
    console.log('Test 3 - Modal request detection:', { isModal1, isModal2 });
    
    // Test 4: Function injection
    const injectedHtml = service.injectMissingModalFunctions(testHtmlContent, ['openModal', 'closeModal']);
    console.log('Test 4 - Function injection successful:', injectedHtml.includes('function openModal'));
    
    console.log('✅ All validation tests completed');
}

// Test prompt enhancement
function testPromptEnhancement() {
    console.log('🧪 Testing Prompt Enhancement...');
    
    const originalPrompt = 'Add modal functionality to the Generate New Report button';
    const prototypingContext = `
PROTOTYPING CONTEXT: This is for live client demonstration
DEMO REQUIREMENTS: All functionality must work immediately
QUALITY STANDARD: Professional, polished, stakeholder-ready
ERROR PREVENTION: Include complete JavaScript functions with error handling
`;
    
    const enhancedPrompt = prototypingContext + originalPrompt;
    
    console.log('Original prompt length:', originalPrompt.length);
    console.log('Enhanced prompt length:', enhancedPrompt.length);
    console.log('Enhancement includes demo context:', enhancedPrompt.includes('DEMO REQUIREMENTS'));
    
    console.log('✅ Prompt enhancement test completed');
}

// Run tests
if (require.main === module) {
    console.log('🚀 Starting Prompt Robustness Tests...\n');
    
    try {
        testValidation();
        console.log('');
        testPromptEnhancement();
        console.log('\n🎉 All tests completed successfully!');
    } catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    }
}

module.exports = {
    testValidation,
    testPromptEnhancement
};
