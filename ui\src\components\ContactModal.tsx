import { useState, useRef } from 'react';
import styles from './ContactModal.module.css';
import { FiX, FiCopy, <PERSON>Check, FiMessageSquare } from 'react-icons/fi';

interface ContactModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ContactModal({ isOpen, onClose }: ContactModalProps) {
  const [copied, setCopied] = useState(false);
  const emailRef = useRef<HTMLSpanElement>(null);
  const supportEmail = '<EMAIL>';
  const discordLink = 'https://discord.com/channels/1371127601736974356/1371127729000419391';

  const handleCopyEmail = () => {
    navigator.clipboard.writeText(supportEmail).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  if (!isOpen) return null;

  return (
    <div className={styles.modalOverlay} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.closeButton} onClick={onClose}>
          <FiX />
        </button>
        <h2 className={styles.modalTitle}>Contact Us</h2>
        <p className={styles.modalText}>
          For support, feedback, or inquiries, please email us at:
        </p>
        <div className={styles.emailContainer}>
          <span ref={emailRef} className={styles.email}>{supportEmail}</span>
          <button
            className={styles.copyButton}
            onClick={handleCopyEmail}
            title={copied ? "Copied!" : "Copy to clipboard"}
          >
            {copied ? <FiCheck /> : <FiCopy />}
          </button>
        </div>

        <p className={styles.modalText}>
          Or join our Discord community:
        </p>
        <div className={styles.discordContainer}>
          <a
            href={discordLink}
            target="_blank"
            rel="noopener noreferrer"
            className={styles.discordLink}
          >
            <FiMessageSquare className={styles.discordIcon} />
            Join our Discord channel
          </a>
        </div>

        <p className={styles.modalText}>
          We'll get back to you as soon as possible.
        </p>
      </div>
    </div>
  );
}

export function FeedbackButton() {
  const [copied, setCopied] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const supportEmail = '<EMAIL>';
  const discordLink = 'https://discord.com/channels/1371127601736974356/1371127729000419391';

  const handleCopyEmail = () => {
    navigator.clipboard.writeText(supportEmail).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  return (
    <div className={styles.feedbackContainer} onMouseLeave={() => setShowDropdown(false)}>
      <button
        className={styles.feedbackButton}
        onClick={() => setShowDropdown(!showDropdown)}
        onMouseEnter={() => setShowDropdown(true)}
      >
        Feedback & Support
      </button>

      {showDropdown && (
        <div className={styles.feedbackDropdown}>
          <div className={styles.dropdownItem}>
            <span className={styles.dropdownLabel}>Email us:</span>
            <div className={styles.dropdownContent}>
              <span className={styles.email}>{supportEmail}</span>
              <button
                className={styles.copyButton}
                onClick={handleCopyEmail}
                title={copied ? "Copied!" : "Copy to clipboard"}
              >
                {copied ? <FiCheck /> : <FiCopy />}
              </button>
            </div>
          </div>

          <div className={styles.dropdownItem}>
            <span className={styles.dropdownLabel}>Join Discord:</span>
            <div className={styles.dropdownContent}>
              <a
                href={discordLink}
                target="_blank"
                rel="noopener noreferrer"
                className={styles.discordLink}
              >
                <FiMessageSquare className={styles.discordIcon} />
                Discord Channel
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
