.kpi-card { @apply bg-white rounded-lg shadow p-4 text-center; }
.stat-number { @apply text-2xl font-bold text-gray-800; }
.stat-label { @apply text-sm text-gray-500; }
.timeline-item { @apply flex justify-between items-center py-2 px-3 border-b text-sm; }
.task-item { @apply flex justify-between items-center p-2 text-sm rounded hover:bg-gray-50; }
.priority-high { @apply text-red-600 font-semibold; }
.priority-medium { @apply text-yellow-600 font-medium; }
.priority-low { @apply text-green-600; }
.client-table { @apply w-full border border-gray-200 rounded-lg overflow-hidden; }
.table-row { @apply border-b hover:bg-gray-50; }
.action-buttons { @apply flex gap-2; }
.tool-card { @apply bg-white p-4 rounded-lg text-center shadow hover:shadow-lg transition; }
.tool-grid { @apply grid grid-cols-2 md:grid-cols-4 gap-4; }