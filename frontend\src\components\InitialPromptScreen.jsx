import { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  ToggleButtonGroup,
  ToggleButton,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  DesktopWindows as DesktopIcon,
  PhoneIphone as MobileIcon,
  Send as SendIcon,
  FormatBold as BoldIcon,
  FormatItalic as ItalicIcon,
  FormatListBulleted as ListIcon,
  Code as CodeIcon,
  Link as LinkIcon,
  Image as ImageIcon
} from '@mui/icons-material';

function InitialPromptScreen({ onGenerate, isGenerating, prototypeName }) {
  const [prompt, setPrompt] = useState('');
  const [deviceType, setDeviceType] = useState('desktop');
  const [generationStatus, setGenerationStatus] = useState(null);
  const [designPlan, setDesignPlan] = useState(null);

  const handleGenerateWrapper = async (prompt, deviceType) => {
    setGenerationStatus('planning');
    setDesignPlan(null);
    try {
      await onGenerate(prompt, deviceType, (progress) => {
        setGenerationStatus(progress.status);
        if (progress.status === 'plan_complete') {
          setDesignPlan(progress.plan);
        }
      });
    } finally {
      setGenerationStatus(null);
    }
  };

  const handleDeviceChange = (event, newDeviceType) => {
    if (newDeviceType !== null) {
      setDeviceType(newDeviceType);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!prompt.trim()) return;
    
    handleGenerateWrapper(prompt, deviceType);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        width: '100%',
        backgroundImage: 'linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%)',
        p: 3
      }}
    >
      <Box sx={{ mb: 6, textAlign: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: '12px',
              backgroundColor: '#8B5CF6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mr: 1.5
            }}
          >
            <Typography variant="h6" fontWeight={700} sx={{ color: 'white' }}>
              J
            </Typography>
          </Box>
          <Typography variant="h6" fontWeight={600} sx={{ color: 'white' }}>
            JustPrototype
          </Typography>
        </Box>
        
        <Typography variant="h4" fontWeight={700} sx={{ color: 'white', mb: 1 }}>
          What would you like to design today?
        </Typography>
        
        {prototypeName && (
          <Typography variant="body1" sx={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            Creating: <span style={{ color: '#A855F7', fontWeight: 600 }}>{prototypeName}</span>
          </Typography>
        )}
      </Box>

      <Paper
        elevation={3}
        sx={{
          width: '100%',
          maxWidth: '700px',
          borderRadius: 3,
          overflow: 'hidden',
          backgroundColor: 'white',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
        }}
      >
        <Box sx={{ p: 2, borderBottom: '1px solid rgba(0, 0, 0, 0.1)', backgroundColor: '#f9fafb' }}>
          <ToggleButtonGroup
            value={deviceType}
            exclusive
            onChange={handleDeviceChange}
            size="small"
            aria-label="device type"
          >
            <ToggleButton 
              value="desktop" 
              aria-label="desktop"
              sx={{
                px: 2,
                py: 0.75,
                borderRadius: '6px 0 0 6px',
                '&.Mui-selected': {
                  backgroundColor: '#8B5CF6',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: '#7C3AED',
                  }
                }
              }}
            >
              <DesktopIcon fontSize="small" sx={{ mr: 0.5 }} />
              Desktop
            </ToggleButton>
            <ToggleButton 
              value="mobile" 
              aria-label="mobile"
              sx={{
                px: 2,
                py: 0.75,
                borderRadius: '0 6px 6px 0',
                '&.Mui-selected': {
                  backgroundColor: '#8B5CF6',
                  color: 'white',
                  '&:hover': {
                    backgroundColor: '#7C3AED',
                  }
                }
              }}
            >
              <MobileIcon fontSize="small" sx={{ mr: 0.5 }} />
              Mobile
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>
        
        <Box sx={{ p: 2 }}>
          <form onSubmit={handleSubmit}>
            <TextField
              multiline
              rows={6}
              fullWidth
              placeholder="Describe your needs for a single page...
Include style, features, or purpose for best results.
Reference images also welcome."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  backgroundColor: '#ffffff',
                  '& fieldset': {
                    borderColor: 'rgba(0, 0, 0, 0.1)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(139, 92, 246, 0.5)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: '#8B5CF6',
                  },
                }
              }}
            />
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2, alignItems: 'center' }}>
              <Box>
                <Tooltip title="Bold">
                  <IconButton size="small" sx={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                    <BoldIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Italic">
                  <IconButton size="small" sx={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                    <ItalicIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="List">
                  <IconButton size="small" sx={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                    <ListIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Code">
                  <IconButton size="small" sx={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                    <CodeIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Link">
                  <IconButton size="small" sx={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                    <LinkIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Image">
                  <IconButton size="small" sx={{ color: 'rgba(0, 0, 0, 0.6)' }}>
                    <ImageIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
              
              <Button
                type="submit"
                variant="contained"
                disabled={isGenerating || !prompt.trim()}
                endIcon={<SendIcon />}
                sx={{
                  backgroundColor: '#8B5CF6',
                  '&:hover': {
                    backgroundColor: '#7C3AED',
                  },
                  borderRadius: 2,
                  px: 3,
                  py: 1,
                  fontWeight: 600
                }}
              >
                {isGenerating ? 
                  (generationStatus === 'planning' ? 'Planning...' : 
                   generationStatus === 'generating' ? 'Generating...' : 'Processing...') 
                  : 'Generate'}
              </Button>
            </Box>

            {generationStatus && (
              <Box sx={{ mt: 2, p: 2, backgroundColor: '#f8fafc', borderRadius: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Status: {generationStatus === 'planning' ? 'Creating design plan...' : 
                          generationStatus === 'generating' ? 'Generating HTML...' : 'Processing...'}
                </Typography>
              </Box>
            )}

            {designPlan && (
              <Box sx={{ mt: 2, p: 2, backgroundColor: '#f0fdf4', borderRadius: 2 }}>
                <Typography variant="subtitle2" sx={{ mb: 1, color: '#166534' }}>
                  Design Plan
                </Typography>
                <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                  {designPlan}
                </Typography>
              </Box>
            )}
          </form>
        </Box>
      </Paper>
    </Box>
  );
}

export default InitialPromptScreen;
