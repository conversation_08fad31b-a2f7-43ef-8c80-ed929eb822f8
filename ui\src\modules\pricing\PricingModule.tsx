import React, { useState } from 'react';
import styles from './PricingModule.module.css';
import { PricingPlan } from './types';
import { PricingCard } from './components/PricingCard';
import { PricingHeader } from './components/PricingHeader';
import { PricingFAQ } from './components/PricingFAQ';
import { PricingComparison } from './components/PricingComparison';

interface PricingModuleProps {
  /**
   * Array of pricing plans to display
   */
  plans: PricingPlan[];

  /**
   * Optional current plan ID for highlighting
   */
  currentPlanId?: string;

  /**
   * Optional callback when a plan is selected
   */
  onSelectPlan?: (planId: string) => void;

  /**
   * Optional FAQ items
   */
  faqItems?: Array<{ question: string; answer: string }>;

  /**
   * Optional comparison table data
   */
  comparisonData?: {
    features: Array<{ name: string; description?: string }>;
    planFeatures: Record<string, Array<boolean | string>>;
  };

  /**
   * Optional custom header content
   */
  customHeader?: React.ReactNode;

  /**
   * Optional custom footer content
   */
  customFooter?: React.ReactNode;

  /**
   * Optional className for additional styling
   */
  className?: string;

  /**
   * Optional billing cycle (monthly/yearly)
   */
  defaultBillingCycle?: 'monthly' | 'yearly';

  /**
   * Optional yearly discount percentage
   */
  yearlyDiscountPercentage?: number;
}

/**
 * A reusable, modular pricing module that can be used in any project
 */
export const PricingModule: React.FC<PricingModuleProps> = ({
  plans,
  currentPlanId,
  onSelectPlan,
  faqItems,
  comparisonData,
  customHeader,
  customFooter,
  className = '',
  defaultBillingCycle = 'monthly',
  yearlyDiscountPercentage = 0
}) => {
  // Always use monthly billing cycle
  const billingCycle = 'monthly';

  // Use plans as-is without yearly price calculations
  const calculatedPlans = plans;

  const handlePlanSelect = (planId: string) => {
    if (onSelectPlan) {
      onSelectPlan(planId);
    }
  };

  return (
    <div className={`${styles.pricingModule} ${className}`}>
      {customHeader || (
        <PricingHeader
          billingCycle={billingCycle}
          onBillingCycleChange={setBillingCycle}
          yearlyDiscountPercentage={yearlyDiscountPercentage}
        />
      )}

      <div className={styles.plansContainer}>
        {calculatedPlans.map(plan => (
          <PricingCard
            key={plan.id}
            plan={plan}
            billingCycle={billingCycle}
            isCurrentPlan={plan.id === currentPlanId}
            onSelect={() => handlePlanSelect(plan.id)}
          />
        ))}
      </div>

      {comparisonData && (
        <PricingComparison
          features={comparisonData.features}
          planFeatures={comparisonData.planFeatures}
          plans={calculatedPlans}
        />
      )}

      {faqItems && faqItems.length > 0 && (
        <PricingFAQ items={faqItems} />
      )}

      {customFooter && (
        <div className={styles.customFooter}>
          {customFooter}
        </div>
      )}
    </div>
  );
};
