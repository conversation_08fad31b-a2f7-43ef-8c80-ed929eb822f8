-- Migration: Add Session Management for Readdy-Style Intent Generation
-- Version: 001
-- Created: 2024-01-XX
-- Description: Core tables for session-based context management and element interaction tracking

-- =====================================================
-- 1. PROTOTYPE SESSIONS TABLE
-- =====================================================
-- Stores page sessions with context but without full HTML in main queries
CREATE TABLE prototype_sessions (
    id VARCHAR(36) PRIMARY KEY,
    prototype_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    page_url VARCHAR(500) NOT NULL,
    page_html TEXT NOT NULL,
    session_state VARCHAR(20) DEFAULT 'active' CHECK (session_state IN ('active', 'editing', 'completed', 'expired')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_accessed TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '24 hours'),

    -- Foreign key constraints
    CONSTRAINT fk_prototype_sessions_prototype FOREIGN KEY (prototype_id) REFERENCES prototypes(id) ON DELETE CASCADE,
    CONSTRAINT fk_prototype_sessions_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for prototype_sessions
CREATE INDEX idx_user_prototype ON prototype_sessions (user_id, prototype_id);
CREATE INDEX idx_session_state ON prototype_sessions (session_state);
CREATE INDEX idx_last_accessed ON prototype_sessions (last_accessed);

-- =====================================================
-- 2. ELEMENT INTERACTIONS TABLE
-- =====================================================
-- Tracks specific element interactions for context building
CREATE TABLE element_interactions (
    id VARCHAR(36) PRIMARY KEY,
    session_id VARCHAR(36) NOT NULL,
    element_selector VARCHAR(500) NOT NULL,
    element_code TEXT NOT NULL,
    element_type VARCHAR(50) DEFAULT 'unknown',
    user_intent TEXT,
    ai_suggestion TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    -- Foreign key constraints
    CONSTRAINT fk_element_interactions_session FOREIGN KEY (session_id) REFERENCES prototype_sessions(id) ON DELETE CASCADE
);

-- Create indexes for element_interactions
CREATE INDEX idx_session_interactions ON element_interactions (session_id, created_at);
