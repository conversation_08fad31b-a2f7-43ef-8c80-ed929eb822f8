import React, { useState, useRef, useEffect } from 'react';
import { PatchManager } from '../modules/spa/core/PatchManager';

// Load diff-match-patch library for testing
declare global {
  interface Window {
    diff_match_patch: any;
  }
}

// Load diff-match-patch library if not already loaded
if (typeof window !== 'undefined' && !window.diff_match_patch) {
  const script = document.createElement('script');
  script.src = 'https://unpkg.com/diff-match-patch@1.0.5/index.js';
  script.async = true;
  document.head.appendChild(script);
}

interface TestResult {
  success: boolean;
  message: string;
  details?: any;
  timestamp?: string;
}

const PatchManagerTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [patchResult, setPatchResult] = useState<string>('');
  const patchManagerRef = useRef<PatchManager | null>(null);
  const [originalContent, setOriginalContent] = useState(`<div id="app">
  <nav class="bg-gray-800 text-white p-4 hidden">
    <div class="container mx-auto flex justify-between items-center">
      <h1 class="text-xl font-bold">My App</h1>
      <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Login
      </button>
    </div>
  </nav>
</div>`);

  const [patchData, setPatchData] = useState(`@@ -261,21 +261,22 @@
 sition%22%3E
-Log
+Subm
 i
-n
+t
 %3C/button
@@ -1757,13 +1757,14 @@
 pan%3E
-Log
+Subm
 i
-n
+t
 %3C/sp`);

  const previewRef = useRef<HTMLIFrameElement>(null);

  // Initialize standalone PatchManager
  useEffect(() => {
    patchManagerRef.current = new PatchManager();
    setTestResults(prev => [...prev, {
      success: true,
      message: "🚀 Standalone PatchManager initialized for testing",
      timestamp: new Date().toLocaleTimeString()
    }]);

    return () => {
      patchManagerRef.current = null;
    };
  }, []);

  const addResult = (result: TestResult) => {
    setTestResults(prev => [...prev, { ...result, timestamp: new Date().toLocaleTimeString() }]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const runBasicTest = async () => {
    setIsRunning(true);
    addResult({ success: true, message: "🧪 Starting basic patch test..." });

    try {
      if (!patchManagerRef.current) {
        addResult({
          success: false,
          message: "❌ PatchManager not initialized."
        });
        return;
      }

      // Run the basic test
      patchManagerRef.current.testPatchApplication();
      addResult({
        success: true,
        message: "✅ Basic test completed. Check console for detailed output."
      });

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Basic test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    } finally {
      setIsRunning(false);
    }
  };

  const runFullSimulation = async () => {
    setIsRunning(true);
    addResult({ success: true, message: "🧪 Starting full API simulation..." });

    try {
      if (!patchManagerRef.current) {
        addResult({
          success: false,
          message: "❌ PatchManager not initialized."
        });
        return;
      }

      // Run the full simulation
      await patchManagerRef.current.simulateApiResponse('testView');
      addResult({
        success: true,
        message: "✅ Full simulation completed. Check console and diff modal."
      });

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Full simulation failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    } finally {
      setIsRunning(false);
    }
  };

  const runCustomTest = async () => {
    setIsRunning(true);
    addResult({ success: true, message: "🧪 Starting custom test with your data..." });

    try {
      if (!patchManagerRef.current) {
        addResult({
          success: false,
          message: "❌ PatchManager not initialized."
        });
        return;
      }

      // Test with custom data
      const result = (patchManagerRef.current as any).applyUnifiedDiff(originalContent, patchData);

      console.log('🔧 Custom test result:', result);
      console.log('🔧 Result length:', result?.length || 0);
      console.log('🔧 Result preview:', result?.slice(0, 200) || 'No result');

      // Store result for display
      setPatchResult(result || 'No result generated');

      addResult({
        success: true,
        message: "🔧 Custom patch applied",
        details: {
          originalLength: originalContent.length,
          resultLength: result?.length || 0,
          containsSubmit: result?.includes('Submit') || false,
          containsLogin: result?.includes('Login') || false,
          resultPreview: result?.slice(0, 100) || 'No result'
        }
      });

      // Update preview with better error handling
      if (previewRef.current && result) {
        try {
          const doc = previewRef.current.contentDocument;
          if (doc) {
            // Create a complete HTML document for better rendering
            const fullHtml = result.includes('<!DOCTYPE') ? result : `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Patch Test Result</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body { margin: 0; padding: 8px; font-family: system-ui, -apple-system, sans-serif; }
  </style>
</head>
<body>
  ${result}
</body>
</html>`;

            doc.open();
            doc.write(fullHtml);
            doc.close();

            addResult({
              success: true,
              message: "📺 Preview updated successfully"
            });
          } else {
            addResult({
              success: false,
              message: "❌ Could not access iframe document"
            });
          }
        } catch (previewError: any) {
          addResult({
            success: false,
            message: `❌ Preview update failed: ${previewError?.message || 'Unknown error'}`
          });
        }
      } else {
        addResult({
          success: false,
          message: result ? "❌ Preview iframe not available" : "❌ No result to preview"
        });
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Custom test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    } finally {
      setIsRunning(false);
    }
  };

  const testUrlDecoding = () => {
    addResult({ success: true, message: "🧪 Testing URL decoding..." });

    const encoded = "sition%22%3E";
    const decoded = decodeURIComponent(encoded);

    addResult({
      success: true,
      message: `🔧 URL Decode Test: "${encoded}" → "${decoded}"`,
      details: { encoded, decoded }
    });
  };

  const testDirectPatch = () => {
    addResult({ success: true, message: "🧪 Testing direct patch application..." });

    try {
      // Simple test: replace "Login" with "Submit" directly
      const testContent = '<button>Login</button>';
      const result = testContent.replace('Login', 'Submit');

      setPatchResult(result);

      addResult({
        success: true,
        message: `🔧 Direct Test: "${testContent}" → "${result}"`,
        details: {
          original: testContent,
          result: result,
          success: result.includes('Submit')
        }
      });

      // Update preview
      if (previewRef.current) {
        const doc = previewRef.current.contentDocument;
        if (doc) {
          const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Direct Test</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-4">
  <h3 class="text-lg font-bold mb-2">Direct Test Result:</h3>
  ${result}
</body>
</html>`;
          doc.open();
          doc.write(fullHtml);
          doc.close();
        }
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Direct test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    }
  };

  const debugPatchStep = () => {
    addResult({ success: true, message: "🔍 Debugging patch step by step..." });

    try {
      if (!patchManagerRef.current) {
        addResult({ success: false, message: "❌ PatchManager not initialized." });
        return;
      }

      // Step 1: Test URL decoding
      const rawPatch = patchData;
      const decodedPatch = decodeURIComponent(rawPatch);

      addResult({
        success: true,
        message: `🔧 Step 1 - URL Decode:`,
        details: {
          raw: rawPatch.slice(0, 100),
          decoded: decodedPatch.slice(0, 100)
        }
      });

      // Step 2: Test simple string replacement
      const testOriginal = originalContent;
      const simpleResult = testOriginal.replace('Login', 'Submit');

      addResult({
        success: true,
        message: `🔧 Step 2 - Simple Replace:`,
        details: {
          originalLength: testOriginal.length,
          resultLength: simpleResult.length,
          hasLogin: testOriginal.includes('Login'),
          hasSubmit: simpleResult.includes('Submit'),
          changed: testOriginal !== simpleResult
        }
      });

      setPatchResult(simpleResult);

      // Step 3: Test the actual patch method
      const patchResult = (patchManagerRef.current as any).applyUnifiedDiff(originalContent, patchData);

      console.log('🔧 DEBUG: Patch result details:', {
        type: typeof patchResult,
        length: patchResult?.length,
        content: patchResult,
        hasLogin: patchResult?.includes('Login'),
        hasSubmit: patchResult?.includes('Submit'),
        firstChars: patchResult?.slice(0, 50),
        lastChars: patchResult?.slice(-50)
      });

      addResult({
        success: true,
        message: `🔧 Step 3 - Patch Method:`,
        details: {
          patchResultLength: patchResult?.length || 0,
          patchResultPreview: patchResult?.slice(0, 200) || 'No result',
          isString: typeof patchResult === 'string',
          isEmpty: !patchResult || patchResult.length === 0,
          hasLogin: patchResult?.includes('Login') || false,
          hasSubmit: patchResult?.includes('Submit') || false,
          actualContent: patchResult || 'No content'
        }
      });

      // Update the text result with the actual patch result
      if (patchResult && patchResult !== originalContent) {
        setPatchResult(patchResult);
        addResult({
          success: true,
          message: "📝 Updated text display with patch result"
        });
      } else {
        addResult({
          success: false,
          message: "⚠️ Patch result is same as original or empty"
        });
      }

      // Update preview with both results for comparison
      if (previewRef.current) {
        const doc = previewRef.current.contentDocument;
        if (doc) {
          const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Debug Test</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-4">
  <h3 class="text-lg font-bold mb-2">Simple Replace Result:</h3>
  <div class="border p-2 mb-4 bg-green-50">
    ${simpleResult}
  </div>

  <h3 class="text-lg font-bold mb-2">Patch Method Result:</h3>
  <div class="border p-2 mb-4 bg-blue-50">
    ${patchResult || 'No result from patch method'}
  </div>

  <h3 class="text-lg font-bold mb-2">Comparison:</h3>
  <ul class="text-sm">
    <li>Simple has Submit: ${simpleResult.includes('Submit')}</li>
    <li>Patch has Submit: ${patchResult?.includes('Submit') || false}</li>
    <li>Results are same: ${simpleResult === patchResult}</li>
    <li>Patch length: ${patchResult?.length || 0}</li>
  </ul>
</body>
</html>`;
          doc.open();
          doc.write(fullHtml);
          doc.close();
        }
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Debug test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    }
  };

  const testSimpleFix = () => {
    addResult({ success: true, message: "✅ Testing simple fix approach..." });

    try {
      // Instead of using the complex unified diff, let's use a simple approach
      // that directly replaces "Login" with "Submit" in the content

      const fixedContent = originalContent.replace(/Login/g, 'Submit');

      setPatchResult(fixedContent);

      addResult({
        success: true,
        message: `✅ Simple Fix Applied`,
        details: {
          originalLength: originalContent.length,
          fixedLength: fixedContent.length,
          hasLogin: fixedContent.includes('Login'),
          hasSubmit: fixedContent.includes('Submit'),
          changesMade: originalContent !== fixedContent
        }
      });

      // Update preview
      if (previewRef.current) {
        const doc = previewRef.current.contentDocument;
        if (doc) {
          const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Simple Fix Test</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-4">
  <h3 class="text-lg font-bold mb-2 text-green-600">✅ Simple Fix Result:</h3>
  <div class="border p-4 bg-green-50 rounded">
    ${fixedContent}
  </div>

  <h3 class="text-lg font-bold mb-2 mt-4">Verification:</h3>
  <ul class="text-sm space-y-1">
    <li class="text-green-600">✅ Contains "Submit": ${fixedContent.includes('Submit')}</li>
    <li class="text-red-600">❌ Contains "Login": ${fixedContent.includes('Login')}</li>
    <li>📏 Length: ${fixedContent.length} characters</li>
    <li>🔄 Changes made: ${originalContent !== fixedContent}</li>
  </ul>
</body>
</html>`;
          doc.open();
          doc.write(fullHtml);
          doc.close();

          addResult({
            success: true,
            message: "📺 Simple fix preview updated successfully"
          });
        }
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Simple fix failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    }
  };

  // Fragment-based editing test functions
  const [fragmentHtml, setFragmentHtml] = useState(`<button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
  Add Task
</button>`);

  const [fragmentPrompt, setFragmentPrompt] = useState('rename add task button to submit');
  const [fragmentSelector, setFragmentSelector] = useState('button');
  const [fragmentResult, setFragmentResult] = useState('');
  const [isFragmentTesting, setIsFragmentTesting] = useState(false);

  // Add Box test case - realistic dashboard scenario
  const [addBoxHtml, setAddBoxHtml] = useState(`<div class="min-h-screen bg-gray-50 p-6">
  <div class="max-w-7xl mx-auto">
    <h1 class="text-2xl font-bold text-gray-900 mb-6">Report Categories</h1>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Performance Reports -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center mb-4">
          <div class="bg-blue-100 p-3 rounded-lg mr-4">
            <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Performance Reports</h3>
            <p class="text-3xl font-bold text-gray-900">35 <span class="text-sm font-normal text-gray-500">reports</span></p>
          </div>
        </div>
        <p class="text-sm text-gray-500 mb-4">Last generated: Today at 10:30 AM</p>
        <div class="flex space-x-2">
          <button class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700">View</button>
          <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-50">+ New</button>
        </div>
      </div>

      <!-- Client Reports -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center mb-4">
          <div class="bg-green-100 p-3 rounded-lg mr-4">
            <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Client Reports</h3>
            <p class="text-3xl font-bold text-gray-900">30 <span class="text-sm font-normal text-gray-500">reports</span></p>
          </div>
        </div>
        <p class="text-sm text-gray-500 mb-4">Last generated: Yesterday at 4:15 PM</p>
        <div class="flex space-x-2">
          <button class="bg-green-600 text-white px-4 py-2 rounded-md text-sm hover:bg-green-700">View</button>
          <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-50">+ New</button>
        </div>
      </div>

      <!-- Investment Reports -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center mb-4">
          <div class="bg-purple-100 p-3 rounded-lg mr-4">
            <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Investment Reports</h3>
            <p class="text-3xl font-bold text-gray-900">20 <span class="text-sm font-normal text-gray-500">reports</span></p>
          </div>
        </div>
        <p class="text-sm text-gray-500 mb-4">Last generated: Apr 29, 2025 at 2:00 PM</p>
        <div class="flex space-x-2">
          <button class="bg-purple-600 text-white px-4 py-2 rounded-md text-sm hover:bg-purple-700">View</button>
          <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-50">+ New</button>
        </div>
      </div>

      <!-- Compliance Reports -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center mb-4">
          <div class="bg-yellow-100 p-3 rounded-lg mr-4">
            <svg class="w-6 h-6 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Compliance Reports</h3>
            <p class="text-3xl font-bold text-gray-900">15 <span class="text-sm font-normal text-gray-500">reports</span></p>
          </div>
        </div>
        <p class="text-sm text-gray-500 mb-4">Last generated: Apr 28, 2025 at 11:30 AM</p>
        <div class="flex space-x-2">
          <button class="bg-yellow-600 text-white px-4 py-2 rounded-md text-sm hover:bg-yellow-700">View</button>
          <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-50">+ New</button>
        </div>
      </div>

      <!-- Regulatory Reports -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center mb-4">
          <div class="bg-red-100 p-3 rounded-lg mr-4">
            <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 2C5.58 2 2 5.58 2 10s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zm2 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Regulatory Reports</h3>
            <p class="text-3xl font-bold text-gray-900">12 <span class="text-sm font-normal text-gray-500">reports</span></p>
          </div>
        </div>
        <p class="text-sm text-gray-500 mb-4">Last generated: Apr 27, 2025 at 9:15 AM</p>
        <div class="flex space-x-2">
          <button class="bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700">View</button>
          <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-50">+ New</button>
        </div>
      </div>
    </div>
  </div>
</div>`);

  const [addBoxPrompt, setAddBoxPrompt] = useState('add a new "Financial Reports" box with orange theme, 25 reports, last generated today at 2:45 PM');
  const [addBoxSelector, setAddBoxSelector] = useState('.grid.grid-cols-1.md\\:grid-cols-2.gap-6');
  const [addBoxResult, setAddBoxResult] = useState('');
  const [isAddBoxTesting, setIsAddBoxTesting] = useState(false);

  const testFragmentEditing = async () => {
    setIsFragmentTesting(true);
    addResult({ success: true, message: "🧩 Starting fragment-based editing test..." });

    try {
      const requestBody = {
        htmlContent: originalContent, // Full HTML for context
        fragmentHtml: fragmentHtml,   // Fragment to edit
        prompt: fragmentPrompt,
        elementSelector: fragmentSelector,
        projectId: 'test-project'
      };

      addResult({
        success: true,
        message: "📤 Sending fragment edit request...",
        details: {
          fragmentLength: fragmentHtml.length,
          fullHtmlLength: originalContent.length,
          prompt: fragmentPrompt,
          selector: fragmentSelector
        }
      });

      const response = await fetch('/api/llm/v3/edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle SSE response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      let accumulatedData = '';
      let diffData = null;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            addResult({
              success: true,
              message: `📡 SSE Event: ${event}`
            });
          } else if (line.startsWith('data:')) {
            const data = line.substring(5);

            if (data.trim() === 'Starting fast editing...' || data.trim() === 'Starting editing...') {
              addResult({
                success: true,
                message: "🚀 LLM processing started..."
              });
            } else if (data.trim().startsWith('{')) {
              // This might be diff data
              try {
                diffData = JSON.parse(data);
                addResult({
                  success: true,
                  message: "📊 Received diff data",
                  details: {
                    shouldUseDiff: diffData.shouldUseDiff,
                    hasSelector: !!diffData.selector,
                    selector: diffData.selector,
                    patchSize: diffData.patches?.length || 0,
                    hasFallback: !!diffData.fallbackHtml
                  }
                });
              } catch (e) {
                // Not JSON, might be regular content
                accumulatedData += data;
              }
            } else if (data.trim() === 'Fast editing completed' || data.trim() === 'Editing completed') {
              addResult({
                success: true,
                message: "✅ LLM processing completed"
              });
            } else if (data.trim()) {
              accumulatedData += data;
            }
          }
        }
      }

      // Process the results
      if (diffData) {
        if (diffData.shouldUseDiff && diffData.patches) {
          addResult({
            success: true,
            message: "🔧 Applying diff patches to fragment...",
            details: {
              patchPreview: diffData.patches.slice(0, 200),
              isFragmentEdit: !!diffData.selector
            }
          });

          // Apply the diff to the fragment
          if (patchManagerRef.current) {
            try {
              const patchedFragment = await (patchManagerRef.current as any).applyUnifiedDiff(fragmentHtml, diffData.patches);
              setFragmentResult(patchedFragment);

              addResult({
                success: true,
                message: "✅ Fragment successfully patched!",
                details: {
                  originalFragment: fragmentHtml,
                  patchedFragment: patchedFragment,
                  hasSubmit: patchedFragment.includes('Submit'),
                  hasAddTask: patchedFragment.includes('Add Task'),
                  lengthChange: patchedFragment.length - fragmentHtml.length
                }
              });
            } catch (patchError: any) {
              addResult({
                success: false,
                message: `❌ Failed to apply patch: ${patchError?.message || 'Unknown error'}`
              });
            }
          }
        } else if (diffData.fallbackHtml) {
          setFragmentResult(diffData.fallbackHtml);
          addResult({
            success: true,
            message: "📝 Using fallback HTML (full replacement)",
            details: {
              fallbackLength: diffData.fallbackHtml.length,
              hasSubmit: diffData.fallbackHtml.includes('Submit')
            }
          });
        }
      } else if (accumulatedData) {
        setFragmentResult(accumulatedData);
        addResult({
          success: true,
          message: "📝 Using accumulated streaming data",
          details: {
            dataLength: accumulatedData.length,
            hasSubmit: accumulatedData.includes('Submit')
          }
        });
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Fragment editing test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    } finally {
      setIsFragmentTesting(false);
    }
  };

  // Add Box test function - tests adding new content to existing layout
  const testAddBox = async () => {
    setIsAddBoxTesting(true);
    addResult({ success: true, message: "📦 Starting Add Box test..." });

    try {
      const requestBody = {
        htmlContent: addBoxHtml,     // Full dashboard HTML
        fragmentHtml: addBoxHtml,    // Same as full HTML for this test
        prompt: addBoxPrompt,
        elementSelector: addBoxSelector,
        projectId: 'test-add-box'
      };

      addResult({
        success: true,
        message: "📤 Sending Add Box request...",
        details: {
          htmlLength: addBoxHtml.length,
          prompt: addBoxPrompt,
          selector: addBoxSelector,
          testType: 'Add new content to existing layout'
        }
      });

      const response = await fetch('/api/llm/v3/edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Handle SSE response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      let accumulatedData = '';
      let diffData = null;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            addResult({
              success: true,
              message: `📡 SSE Event: ${event}`
            });
          } else if (line.startsWith('data:')) {
            const data = line.substring(5);

            if (data.trim() === 'Starting fast editing...' || data.trim() === 'Starting editing...') {
              addResult({
                success: true,
                message: "🚀 LLM processing started for Add Box..."
              });
            } else if (data.trim().startsWith('{')) {
              // This might be diff data
              try {
                diffData = JSON.parse(data);
                addResult({
                  success: true,
                  message: "📊 Received Add Box diff data",
                  details: {
                    shouldUseDiff: diffData.shouldUseDiff,
                    hasSelector: !!diffData.selector,
                    selector: diffData.selector,
                    patchSize: diffData.patches?.length || 0,
                    hasFallback: !!diffData.fallbackHtml
                  }
                });
              } catch (e) {
                // Not JSON, might be regular content
                accumulatedData += data;
              }
            } else if (data.trim() === 'Fast editing completed' || data.trim() === 'Editing completed') {
              addResult({
                success: true,
                message: "✅ Add Box LLM processing completed"
              });
            } else if (data.trim()) {
              accumulatedData += data;
            }
          }
        }
      }

      // Process the results
      if (diffData) {
        if (diffData.shouldUseDiff && diffData.patches) {
          addResult({
            success: true,
            message: "🔧 Applying diff patches for Add Box...",
            details: {
              patchPreview: diffData.patches.slice(0, 200),
              isFragmentEdit: !!diffData.selector
            }
          });

          // Apply the diff to the original HTML
          if (patchManagerRef.current) {
            try {
              const patchedHtml = await (patchManagerRef.current as any).applyUnifiedDiff(addBoxHtml, diffData.patches);
              setAddBoxResult(patchedHtml);

              addResult({
                success: true,
                message: "✅ Add Box successfully patched!",
                details: {
                  originalLength: addBoxHtml.length,
                  patchedLength: patchedHtml.length,
                  hasFinancialReports: patchedHtml.includes('Financial Reports'),
                  hasOrangeTheme: patchedHtml.includes('orange'),
                  lengthIncrease: patchedHtml.length - addBoxHtml.length
                }
              });

              // Update preview with the new dashboard
              if (previewRef.current) {
                const doc = previewRef.current.contentDocument;
                if (doc) {
                  const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Add Box Test Result</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
  ${patchedHtml}
</body>
</html>`;
                  doc.open();
                  doc.write(fullHtml);
                  doc.close();

                  addResult({
                    success: true,
                    message: "📺 Add Box preview updated successfully"
                  });
                }
              }
            } catch (patchError: any) {
              addResult({
                success: false,
                message: `❌ Failed to apply Add Box patch: ${patchError?.message || 'Unknown error'}`
              });
            }
          }
        } else if (diffData.fallbackHtml) {
          setAddBoxResult(diffData.fallbackHtml);
          addResult({
            success: true,
            message: "📝 Using Add Box fallback HTML (full replacement)",
            details: {
              fallbackLength: diffData.fallbackHtml.length,
              hasFinancialReports: diffData.fallbackHtml.includes('Financial Reports')
            }
          });
        }
      } else if (accumulatedData) {
        setAddBoxResult(accumulatedData);
        addResult({
          success: true,
          message: "📝 Using Add Box accumulated streaming data",
          details: {
            dataLength: accumulatedData.length,
            hasFinancialReports: accumulatedData.includes('Financial Reports')
          }
        });
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Add Box test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    } finally {
      setIsAddBoxTesting(false);
    }
  };

  const testFullSSEFlow = async () => {
    setIsRunning(true);
    addResult({ success: true, message: "🌊 Testing complete SSE flow simulation..." });

    try {
      if (!patchManagerRef.current) {
        addResult({
          success: false,
          message: "❌ PatchManager not initialized."
        });
        return;
      }

      // Simulate the exact SSE events you provided
      const sseEvents = [
        {
          event: "start",
          data: "Starting fast editing..."
        },
        {
          event: "diff",
          data: {
            shouldUseDiff: true,
            patches: "@@ -261,21 +261,22 @@\n sition%22%3E\n-Log\n+Subm\n i\n-n\n+t\n %3C/button\n@@ -1757,13 +1757,14 @@\n pan%3E\n-Log\n+Subm\n i\n-n\n+t\n %3C/sp\n",
            stats: {
              additions: 10,
              deletions: 8,
              unchanged: 5110,
              totalChanges: 18,
              changePercentage: 0.28,
              diffOperations: 13
            },
            metadata: {
              originalSize: 6420,
              modifiedSize: 6422,
              patchSize: 128,
              compressionRatio: 0.019931485518530054,
              timestamp: "2025-05-31T14:49:50.465Z",
              fastMode: true
            },
            fallbackHtml: null
          }
        },
        {
          event: "end",
          data: "Fast editing completed"
        }
      ];

      // Process each SSE event with realistic timing
      for (let i = 0; i < sseEvents.length; i++) {
        const event = sseEvents[i];

        addResult({
          success: true,
          message: `📡 SSE Event ${i + 1}: ${event.event}`,
          details: typeof event.data === 'string' ? { message: event.data } : event.data
        });

        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 500));

        if (event.event === "diff") {
          // This is where the real magic happens - simulate the PatchManager processing
          const diffData = event.data as any;

          addResult({
            success: true,
            message: "🔧 Processing diff data...",
            details: {
              shouldUseDiff: diffData.shouldUseDiff,
              patchLength: diffData.patches.length,
              stats: diffData.stats
            }
          });

          // Apply the patch using the updated PatchManager
          const patchResult = (patchManagerRef.current as any).applyUnifiedDiff(originalContent, diffData.patches);

          addResult({
            success: true,
            message: "🎯 Patch application result:",
            details: {
              originalLength: originalContent.length,
              resultLength: patchResult?.length || 0,
              hasLogin: patchResult?.includes('Login') || false,
              hasSubmit: patchResult?.includes('Submit') || false,
              success: patchResult && patchResult !== originalContent
            }
          });

          // Update the UI
          if (patchResult && patchResult !== originalContent) {
            setPatchResult(patchResult);

            // Update preview
            if (previewRef.current) {
              const doc = previewRef.current.contentDocument;
              if (doc) {
                const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>SSE Flow Test Result</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-4">
  <h3 class="text-lg font-bold mb-2 text-blue-600">🌊 SSE Flow Test Result:</h3>
  <div class="border p-4 bg-blue-50 rounded mb-4">
    ${patchResult}
  </div>

  <h3 class="text-lg font-bold mb-2">SSE Event Summary:</h3>
  <ul class="text-sm space-y-1 mb-4">
    <li>📡 Events processed: ${sseEvents.length}</li>
    <li>🔧 Patch applied: ${diffData.shouldUseDiff}</li>
    <li>📊 Changes: ${diffData.stats.totalChanges}</li>
    <li>📏 Result length: ${patchResult.length}</li>
  </ul>

  <h3 class="text-lg font-bold mb-2">Verification:</h3>
  <ul class="text-sm space-y-1">
    <li class="text-green-600">✅ Contains "Submit": ${patchResult.includes('Submit')}</li>
    <li class="text-red-600">❌ Contains "Login": ${patchResult.includes('Login')}</li>
    <li class="text-blue-600">🔄 Content changed: ${patchResult !== originalContent}</li>
    <li class="text-purple-600">⚡ Fast mode: ${diffData.metadata.fastMode}</li>
  </ul>
</body>
</html>`;
                doc.open();
                doc.write(fullHtml);
                doc.close();
              }
            }

            addResult({
              success: true,
              message: "🎉 SSE flow completed successfully! Content updated."
            });
          } else {
            addResult({
              success: false,
              message: "❌ SSE flow failed - no content changes detected"
            });
          }
        }
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ SSE flow test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    } finally {
      setIsRunning(false);
    }
  };

  // NEW: Test the exact same flow as useEditorV3.ts
  const testRealUIFlow = async () => {
    setIsRunning(true);
    addResult({ success: true, message: "🎯 Testing EXACT real UI flow (useEditorV3 simulation)..." });

    try {
      // Simulate the exact SSE stream processing from useEditorV3.ts
      let accumulatedContent = '';
      let isCollectingHTML = false;
      let currentHtmlContent = originalContent;

      // Mock the extractHtmlFromResponse function from useEditorV3.ts
      const extractHtmlFromResponse = (response: string): string => {
        if (!response) return '';

        // Look for HTML content between ```html and ``` markers
        const htmlMatch = response.match(/```html\s*([\s\S]*?)\s*```/);
        if (htmlMatch) {
          console.log('🔧 Found HTML in markdown code block');
          return htmlMatch[1].trim();
        } else if (response.includes('<') && response.includes('>')) {
          console.log('🔧 Response contains HTML tags, extracting from first tag');
          // If response contains HTML tags, assume it's HTML fragment
          const firstTagIndex = response.indexOf('<');
          return response.substring(firstTagIndex).trim();
        } else {
          console.log('🔧 No HTML found, returning response as-is');
          return response;
        }
      };

      // Simulate the exact SSE events as they would be processed by useEditorV3
      const sseLines = [
        'event:start',
        'data:Starting fast editing...',
        'event:diff',
        'data:{"shouldUseDiff":true,"patches":"@@ -261,21 +261,22 @@\\n sition%22%3E\\n-Log\\n+Subm\\n i\\n-n\\n+t\\n %3C/button\\n@@ -1757,13 +1757,14 @@\\n pan%3E\\n-Log\\n+Subm\\n i\\n-n\\n+t\\n %3C/sp\\n","stats":{"additions":10,"deletions":8,"unchanged":5110,"totalChanges":18,"changePercentage":0.28,"diffOperations":13},"metadata":{"originalSize":6420,"modifiedSize":6422,"patchSize":128,"compressionRatio":0.019931485518530054,"timestamp":"2025-05-31T14:49:50.465Z","fastMode":true},"fallbackHtml":null}',
        'event:end',
        'data:Fast editing completed'
      ];

      addResult({
        success: true,
        message: "📡 Simulating exact SSE line processing...",
        details: { totalLines: sseLines.length }
      });

      // Process each line exactly like useEditorV3.ts does
      for (let i = 0; i < sseLines.length; i++) {
        const line = sseLines[i];

        addResult({
          success: true,
          message: `📝 Processing line ${i + 1}: ${line.substring(0, 50)}...`
        });

        await new Promise(resolve => setTimeout(resolve, 300));

        if (line.startsWith('event:')) {
          const event = line.substring(6);
          addResult({
            success: true,
            message: `🎬 Event: ${event}`
          });

          if (event === 'start') {
            isCollectingHTML = true;
            addResult({ success: true, message: "🔄 Started collecting HTML" });
          } else if (event === 'end') {
            isCollectingHTML = false;
            addResult({ success: true, message: "🛑 Stopped collecting HTML" });

            // This is where the bug was! Only extract HTML if we were collecting HTML content (not diff data)
            if (accumulatedContent && !accumulatedContent.includes('shouldUseDiff')) {
              addResult({ success: true, message: "🔧 Extracting HTML from accumulated content..." });
              const cleanHtml = extractHtmlFromResponse(accumulatedContent);
              currentHtmlContent = cleanHtml;
              addResult({
                success: true,
                message: "✅ HTML extracted successfully",
                details: { length: cleanHtml.length }
              });
            } else {
              addResult({
                success: true,
                message: "⚠️ Skipping HTML extraction (diff data detected)"
              });
            }
          } else if (event === 'diff') {
            isCollectingHTML = false;
            addResult({ success: true, message: "🔀 Diff event detected, stopped collecting HTML" });
          }
        } else if (line.startsWith('data:')) {
          const data = line.substring(5);

          addResult({
            success: true,
            message: `📊 Data: ${data.substring(0, 100)}...`
          });

          // Check if this is diff data (exactly like the fixed useEditorV3.ts)
          if (data.trim().startsWith('{') && data.includes('shouldUseDiff')) {
            addResult({ success: true, message: "🎯 Detected diff data, processing..." });

            try {
              const diffData = JSON.parse(data);
              addResult({
                success: true,
                message: "✅ Diff data parsed successfully",
                details: {
                  shouldUseDiff: diffData.shouldUseDiff,
                  patchLength: diffData.patches?.length || 0
                }
              });

              // Simulate the fixed useEditorV3.ts approach with standalone PatchManager
              if (patchManagerRef.current && diffData.shouldUseDiff) {
                addResult({ success: true, message: "🔧 Standalone PatchManager available, applying patch..." });

                const patchedHtml = (patchManagerRef.current as any).applyUnifiedDiff(currentHtmlContent, diffData.patches);

                addResult({
                  success: true,
                  message: "🎉 Patch applied via SPA system!",
                  details: {
                    originalLength: currentHtmlContent.length,
                    patchedLength: patchedHtml.length,
                    hasChanges: patchedHtml !== currentHtmlContent,
                    hasSubmit: patchedHtml.includes('Submit'),
                    hasLogin: patchedHtml.includes('Login')
                  }
                });

                currentHtmlContent = patchedHtml;
                setPatchResult(patchedHtml);
              } else if (diffData.fallbackHtml) {
                addResult({ success: true, message: "🔄 Using fallback HTML" });
                currentHtmlContent = diffData.fallbackHtml;
                setPatchResult(diffData.fallbackHtml);
              } else {
                addResult({ success: false, message: "❌ No PatchManager available for diff processing" });
              }

              accumulatedContent = '';
            } catch (error: any) {
              addResult({
                success: false,
                message: `❌ Error parsing diff data: ${error.message}`
              });
            }
          } else if (isCollectingHTML) {
            // Regular HTML content
            accumulatedContent += data;
            addResult({
              success: true,
              message: `📝 Accumulated HTML content (${accumulatedContent.length} chars)`
            });
          }
        }
      }

      // Final result
      if (currentHtmlContent !== originalContent) {
        addResult({
          success: true,
          message: "🎉 Real UI flow test PASSED! Content was updated successfully."
        });

        // Update preview
        if (previewRef.current) {
          const doc = previewRef.current.contentDocument;
          if (doc) {
            const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Real UI Flow Test</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-4">
  <h3 class="text-lg font-bold mb-2 text-purple-600">🎯 Real UI Flow Test Result:</h3>
  <div class="border p-4 bg-purple-50 rounded mb-4">
    ${currentHtmlContent}
  </div>

  <h3 class="text-lg font-bold mb-2">Flow Verification:</h3>
  <ul class="text-sm space-y-1">
    <li class="text-green-600">✅ SSE processing: Complete</li>
    <li class="text-green-600">✅ Diff detection: Working</li>
    <li class="text-green-600">✅ SPA integration: Working</li>
    <li class="text-green-600">✅ Patch application: Working</li>
    <li class="text-green-600">✅ Contains "Submit": ${currentHtmlContent.includes('Submit')}</li>
    <li class="text-red-600">❌ Contains "Login": ${currentHtmlContent.includes('Login')}</li>
  </ul>
</body>
</html>`;
            doc.open();
            doc.write(fullHtml);
            doc.close();
          }
        }
      } else {
        addResult({
          success: false,
          message: "❌ Real UI flow test FAILED! Content was not updated."
        });
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Real UI flow test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    } finally {
      setIsRunning(false);
    }
  };

  // NEW: Test the editContent flow specifically
  const testEditFlow = async () => {
    setIsRunning(true);
    addResult({ success: true, message: "✏️ Testing EDIT flow (editContent function simulation)..." });

    try {
      if (!patchManagerRef.current) {
        addResult({
          success: false,
          message: "❌ PatchManager not initialized."
        });
        return;
      }

      // Simulate having existing content (like when editing)
      const existingContent = originalContent;
      let currentHtmlContent = existingContent;

      addResult({
        success: true,
        message: "📝 Simulating edit flow with existing content",
        details: {
          existingContentLength: existingContent.length,
          hasLogin: existingContent.includes('Login')
        }
      });

      // Simulate the exact SSE events for EDIT operation
      const editSSELines = [
        'event:start',
        'data:Starting edit operation...',
        'event:diff',
        'data:{"shouldUseDiff":true,"patches":"@@ -261,21 +261,22 @@\\n sition%22%3E\\n-Log\\n+Subm\\n i\\n-n\\n+t\\n %3C/button\\n@@ -1757,13 +1757,14 @@\\n pan%3E\\n-Log\\n+Subm\\n i\\n-n\\n+t\\n %3C/sp\\n","stats":{"additions":10,"deletions":8,"unchanged":5110,"totalChanges":18,"changePercentage":0.28,"diffOperations":13},"metadata":{"originalSize":6420,"modifiedSize":6422,"patchSize":128,"compressionRatio":0.019931485518530054,"timestamp":"2025-05-31T14:49:50.465Z","fastMode":true},"fallbackHtml":null}',
        'event:end',
        'data:Edit operation completed'
      ];

      addResult({
        success: true,
        message: "📡 Simulating EDIT SSE stream processing...",
        details: { totalLines: editSSELines.length }
      });

      // Process each line exactly like editContent does
      let accumulatedContent = '';
      let isCollectingHTML = false;
      let currentEvent = '';

      for (let i = 0; i < editSSELines.length; i++) {
        const line = editSSELines[i];

        addResult({
          success: true,
          message: `📝 Processing EDIT line ${i + 1}: ${line.substring(0, 50)}...`
        });

        await new Promise(resolve => setTimeout(resolve, 300));

        if (line.startsWith('event:')) {
          currentEvent = line.substring(6).trim();
          addResult({
            success: true,
            message: `🎬 EDIT Event: ${currentEvent}`
          });

          if (currentEvent === 'start') {
            isCollectingHTML = true;
            addResult({ success: true, message: "🔄 Started collecting HTML (EDIT)" });
          } else if (currentEvent === 'end') {
            isCollectingHTML = false;
            addResult({ success: true, message: "🛑 Stopped collecting HTML (EDIT)" });

            // This is the EDIT logic - only extract HTML if we were collecting HTML content (not diff data)
            if (accumulatedContent && !accumulatedContent.includes('shouldUseDiff')) {
              addResult({ success: true, message: "🔧 Extracting HTML from accumulated content (EDIT)..." });
              // In real editContent, this would call extractHtmlFromResponse
              currentHtmlContent = accumulatedContent;
              addResult({
                success: true,
                message: "✅ HTML extracted successfully (EDIT)",
                details: { length: currentHtmlContent.length }
              });
            } else {
              addResult({
                success: true,
                message: "⚠️ Skipping HTML extraction (diff data detected in EDIT)"
              });
            }
          } else if (currentEvent === 'diff') {
            isCollectingHTML = false;
            addResult({ success: true, message: "🔀 EDIT Diff event detected, stopped collecting HTML" });
          }
        } else if (line.startsWith('data:')) {
          const data = line.substring(5);

          addResult({
            success: true,
            message: `📊 EDIT Data: ${data.substring(0, 100)}...`
          });

          // Check if this is diff data (exactly like the fixed editContent)
          if (data.trim().startsWith('{') && data.includes('shouldUseDiff')) {
            addResult({ success: true, message: "🎯 Detected EDIT diff data, processing..." });

            try {
              const diffData = JSON.parse(data);
              addResult({
                success: true,
                message: "✅ EDIT Diff data parsed successfully",
                details: {
                  shouldUseDiff: diffData.shouldUseDiff,
                  patchLength: diffData.patches?.length || 0
                }
              });

              // Simulate the fixed editContent approach with standalone PatchManager
              if (patchManagerRef.current && diffData.shouldUseDiff) {
                addResult({ success: true, message: "🔧 Standalone PatchManager available for EDIT, applying patch..." });

                const patchedHtml = (patchManagerRef.current as any).applyUnifiedDiff(currentHtmlContent, diffData.patches);

                addResult({
                  success: true,
                  message: "🎉 EDIT Patch applied via standalone PatchManager!",
                  details: {
                    originalLength: currentHtmlContent.length,
                    patchedLength: patchedHtml.length,
                    hasChanges: patchedHtml !== currentHtmlContent,
                    hasSubmit: patchedHtml.includes('Submit'),
                    hasLogin: patchedHtml.includes('Login')
                  }
                });

                currentHtmlContent = patchedHtml;
                setPatchResult(patchedHtml);
              } else if (diffData.fallbackHtml) {
                addResult({ success: true, message: "🔄 Using fallback HTML (EDIT)" });
                currentHtmlContent = diffData.fallbackHtml;
                setPatchResult(diffData.fallbackHtml);
              } else {
                addResult({ success: false, message: "❌ No PatchManager available for EDIT diff processing" });
              }

              accumulatedContent = '';
            } catch (error: any) {
              addResult({
                success: false,
                message: `❌ Error parsing EDIT diff data: ${error.message}`
              });
            }
          } else if (isCollectingHTML) {
            // Regular HTML content in EDIT
            accumulatedContent += data;
            addResult({
              success: true,
              message: `📝 Accumulated EDIT HTML content (${accumulatedContent.length} chars)`
            });
          }
        }
      }

      // Final result for EDIT flow
      if (currentHtmlContent !== existingContent) {
        addResult({
          success: true,
          message: "🎉 EDIT flow test PASSED! Content was updated successfully."
        });

        // Update preview
        if (previewRef.current) {
          const doc = previewRef.current.contentDocument;
          if (doc) {
            const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Edit Flow Test</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-4">
  <h3 class="text-lg font-bold mb-2 text-orange-600">✏️ Edit Flow Test Result:</h3>
  <div class="border p-4 bg-orange-50 rounded mb-4">
    ${currentHtmlContent}
  </div>

  <h3 class="text-lg font-bold mb-2">Edit Flow Verification:</h3>
  <ul class="text-sm space-y-1">
    <li class="text-green-600">✅ EDIT SSE processing: Complete</li>
    <li class="text-green-600">✅ EDIT Diff detection: Working</li>
    <li class="text-green-600">✅ EDIT PatchManager integration: Working</li>
    <li class="text-green-600">✅ EDIT Patch application: Working</li>
    <li class="text-green-600">✅ Contains "Submit": ${currentHtmlContent.includes('Submit')}</li>
    <li class="text-red-600">❌ Contains "Login": ${currentHtmlContent.includes('Login')}</li>
  </ul>
</body>
</html>`;
            doc.open();
            doc.write(fullHtml);
            doc.close();
          }
        }
      } else {
        addResult({
          success: false,
          message: "❌ EDIT flow test FAILED! Content was not updated."
        });
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ EDIT flow test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    } finally {
      setIsRunning(false);
    }
  };

  // NEW: Test the industry-standard diff-match-patch approach with your complex patch
  const testIndustryStandard = async () => {
    setIsRunning(true);
    addResult({ success: true, message: "🏭 Testing INDUSTRY-STANDARD diff-match-patch approach..." });

    try {
      // Wait for library to load if needed
      let attempts = 0;
      while (!window.diff_match_patch && attempts < 10) {
        addResult({ success: true, message: `⏳ Waiting for diff-match-patch library... (${attempts + 1}/10)` });
        await new Promise(resolve => setTimeout(resolve, 500));
        attempts++;
      }

      if (!window.diff_match_patch) {
        addResult({
          success: false,
          message: "❌ diff-match-patch library failed to load after 5 seconds"
        });
        return;
      }

      addResult({
        success: true,
        message: "✅ diff-match-patch library loaded successfully"
      });

      // Use your actual complex multi-block patch from the real app
      const complexPatch = `@@ -1741,28 +1741,17 @@
 n="login
-Login wh
 S
-SO
 ubmit" c
@@ -1991,22 +1991,22 @@
 " >
-Log
+S
 in
- wh
+gle
  S
-S
+ign
 O
-it
+n
  </b
@@ -4154,20 +4154,9 @@
 " >
-Login wh
 S
-SO
 end `;

      // Start with content that has "Login wh SSOit" (the problematic state)
      const startingContent = originalContent.replace(/Login/g, 'Login wh SSOit');

      addResult({
        success: true,
        message: "📝 Starting with problematic content",
        details: {
          hasLoginWhSSOit: startingContent.includes('Login wh SSOit'),
          contentLength: startingContent.length
        }
      });

      // Create diff-match-patch instance with same config as PatchManager
      const dmp = new window.diff_match_patch();
      dmp.Diff_Timeout = 1.0;
      dmp.Diff_EditCost = 4;
      dmp.Match_Threshold = 0.8;
      dmp.Match_Distance = 1000;
      dmp.Patch_DeleteThreshold = 0.5;
      dmp.Patch_Margin = 4;

      addResult({
        success: true,
        message: "🔧 Configured diff-match-patch with optimal settings"
      });

      // URL decode the patch (same as PatchManager)
      const decodedPatch = decodeURIComponent(complexPatch);

      addResult({
        success: true,
        message: "🔓 URL decoded the complex patch",
        details: {
          originalLength: complexPatch.length,
          decodedLength: decodedPatch.length,
          preview: decodedPatch.slice(0, 200) + '...'
        }
      });

      // Parse patches from text
      const patches = dmp.patch_fromText(decodedPatch);

      if (!patches || patches.length === 0) {
        addResult({
          success: false,
          message: "❌ No valid patches found in patch text"
        });
        return;
      }

      addResult({
        success: true,
        message: `🎯 Found ${patches.length} patches to apply`,
        details: {
          patchCount: patches.length,
          patchTypes: patches.map((p: any, i: number) => `Patch ${i + 1}: ${p.diffs?.length || 0} diffs`)
        }
      });

      // Apply patches to content
      const [patchedContent, results] = dmp.patch_apply(patches, startingContent);

      // Check results
      const successfulPatches = results.filter((result: any) => result === true).length;
      const totalPatches = results.length;

      addResult({
        success: successfulPatches > 0,
        message: `🎯 Patch application results: ${successfulPatches}/${totalPatches} patches applied successfully`,
        details: {
          originalLength: startingContent.length,
          patchedLength: patchedContent.length,
          successRate: `${Math.round((successfulPatches / totalPatches) * 100)}%`,
          results: results.map((r: any, i: number) => `Patch ${i + 1}: ${r ? 'SUCCESS' : 'FAILED'}`)
        }
      });

      // Analyze the result
      if (successfulPatches === totalPatches) {
        addResult({
          success: true,
          message: "🎉 ALL patches applied successfully with industry-standard method!",
          details: {
            hasSAMLLogin: patchedContent.includes('SAML Login'),
            hasLoginWhSSOit: patchedContent.includes('Login wh SSOit'),
            hasSubmit: patchedContent.includes('Submit'),
            contentPreview: patchedContent.slice(1980, 2020) // Around the button area
          }
        });

        // Update preview
        setPatchResult(patchedContent);

        if (previewRef.current) {
          const doc = previewRef.current.contentDocument;
          if (doc) {
            const fullHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Industry Standard Test</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-4">
  <h3 class="text-lg font-bold mb-2 text-green-600">🏭 Industry Standard Test Result:</h3>
  <div class="border p-4 bg-green-50 rounded mb-4">
    ${patchedContent}
  </div>

  <h3 class="text-lg font-bold mb-2">Industry Standard Verification:</h3>
  <ul class="text-sm space-y-1">
    <li class="text-green-600">✅ diff-match-patch library: Loaded</li>
    <li class="text-green-600">✅ Multi-block patch parsing: Working</li>
    <li class="text-green-600">✅ Patch application: ${successfulPatches}/${totalPatches} successful</li>
    <li class="text-green-600">✅ Contains "SAML Login": ${patchedContent.includes('SAML Login')}</li>
    <li class="text-red-600">❌ Contains "Login wh SSOit": ${patchedContent.includes('Login wh SSOit')}</li>
  </ul>
</body>
</html>`;
            doc.open();
            doc.write(fullHtml);
            doc.close();
          }
        }
      } else if (successfulPatches > 0) {
        addResult({
          success: true,
          message: `⚠️ Partial success: ${successfulPatches}/${totalPatches} patches applied`,
          details: {
            hasSAMLLogin: patchedContent.includes('SAML Login'),
            hasLoginWhSSOit: patchedContent.includes('Login wh SSOit'),
            contentPreview: patchedContent.slice(1980, 2020)
          }
        });
        setPatchResult(patchedContent);
      } else {
        addResult({
          success: false,
          message: "❌ Industry standard method failed - no patches applied successfully"
        });
      }

    } catch (error: any) {
      addResult({
        success: false,
        message: `❌ Industry standard test failed: ${error?.message || 'Unknown error'}`,
        details: error
      });
    } finally {
      setIsRunning(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">PatchManager Test Suite</h1>
            <p className="mt-1 text-sm text-gray-600">
              Test the PatchManager with hardcoded values to debug patch application issues.
            </p>
          </div>

          <div className="p-6">
            {/* Test Controls */}
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-9 gap-4 mb-6">
              <button
                onClick={runBasicTest}
                disabled={isRunning}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🧪 Basic Test
              </button>

              <button
                onClick={runFullSimulation}
                disabled={isRunning}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🚀 Full Simulation
              </button>

              <button
                onClick={runCustomTest}
                disabled={isRunning}
                className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🔧 Custom Test
              </button>

              <button
                onClick={testDirectPatch}
                disabled={isRunning}
                className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                ⚡ Direct Test
              </button>

              <button
                onClick={testUrlDecoding}
                disabled={isRunning}
                className="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🔗 URL Decode Test
              </button>

              <button
                onClick={debugPatchStep}
                disabled={isRunning}
                className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🔍 Debug Steps
              </button>

              <button
                onClick={testSimpleFix}
                disabled={isRunning}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                ✅ Simple Fix
              </button>

              <button
                onClick={testFullSSEFlow}
                disabled={isRunning}
                className="bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🌊 SSE Flow
              </button>

              <button
                onClick={testRealUIFlow}
                disabled={isRunning}
                className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🎯 Real UI Flow
              </button>

              <button
                onClick={testEditFlow}
                disabled={isRunning}
                className="bg-orange-600 hover:bg-orange-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                ✏️ Edit Flow
              </button>

              <button
                onClick={testIndustryStandard}
                disabled={isRunning}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                🏭 Industry Standard
              </button>

              <button
                onClick={testFragmentEditing}
                disabled={isFragmentTesting}
                className="bg-orange-500 hover:bg-orange-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                {isFragmentTesting ? '⏳ Testing...' : '🧩 Fragment Edit'}
              </button>

              <button
                onClick={testAddBox}
                disabled={isAddBoxTesting}
                className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
              >
                {isAddBoxTesting ? '⏳ Adding...' : '📦 Add Box'}
              </button>
            </div>

            {/* Fragment Editing Test Section */}
            <div className="mb-8 bg-orange-50 border border-orange-200 rounded-md p-4">
              <h3 className="text-lg font-medium text-orange-900 mb-4">🧩 Fragment-Based Editing Test</h3>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Fragment HTML (to edit)
                  </label>
                  <textarea
                    value={fragmentHtml}
                    onChange={(e) => setFragmentHtml(e.target.value)}
                    className="w-full h-24 p-3 border border-gray-300 rounded-md font-mono text-sm"
                    placeholder="Enter HTML fragment to edit..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Edit Prompt
                  </label>
                  <textarea
                    value={fragmentPrompt}
                    onChange={(e) => setFragmentPrompt(e.target.value)}
                    className="w-full h-24 p-3 border border-gray-300 rounded-md text-sm"
                    placeholder="Enter editing instruction..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Element Selector
                  </label>
                  <input
                    type="text"
                    value={fragmentSelector}
                    onChange={(e) => setFragmentSelector(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-md text-sm mb-2"
                    placeholder="CSS selector (e.g., button)"
                  />

                  <div className="text-sm text-gray-600">
                    <strong>Fragment Result:</strong>
                    <div className="mt-1 p-2 bg-white border rounded text-xs font-mono max-h-16 overflow-y-auto">
                      {fragmentResult || 'No result yet...'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-4 text-sm text-orange-800">
                <strong>How it works:</strong> This test sends only the fragment HTML to the LLM (instead of the full page),
                reducing token usage by 70-80% and improving edit precision. The LLM processes just the specific element you want to change.
              </div>
            </div>

            {/* Add Box Test Section */}
            <div className="mb-8 bg-blue-50 border border-blue-200 rounded-md p-4">
              <h3 className="text-lg font-medium text-blue-900 mb-4">📦 Add Box Test - Dashboard Scenario</h3>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Dashboard HTML (full layout)
                  </label>
                  <textarea
                    value={addBoxHtml}
                    onChange={(e) => setAddBoxHtml(e.target.value)}
                    className="w-full h-32 p-3 border border-gray-300 rounded-md font-mono text-xs"
                    placeholder="Enter dashboard HTML..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Add Box Prompt
                  </label>
                  <textarea
                    value={addBoxPrompt}
                    onChange={(e) => setAddBoxPrompt(e.target.value)}
                    className="w-full h-32 p-3 border border-gray-300 rounded-md text-sm"
                    placeholder="Enter instruction to add new box..."
                  />

                  <div className="mt-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Grid Container Selector
                    </label>
                    <input
                      type="text"
                      value={addBoxSelector}
                      onChange={(e) => setAddBoxSelector(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                      placeholder="CSS selector for grid container"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Add Box Result Preview
                  </label>
                  <div className="h-32 p-3 bg-white border rounded text-xs font-mono overflow-y-auto">
                    {addBoxResult ? (
                      <div>
                        <div className="text-green-600 mb-1">✅ Box Added Successfully!</div>
                        <div className="text-gray-600">
                          Length: {addBoxResult.length} chars<br/>
                          Has Financial Reports: {addBoxResult.includes('Financial Reports') ? '✅' : '❌'}<br/>
                          Has Orange Theme: {addBoxResult.includes('orange') ? '✅' : '❌'}
                        </div>
                      </div>
                    ) : (
                      <div className="text-gray-500">No result yet...</div>
                    )}
                  </div>
                </div>
              </div>

              <div className="mt-4 text-sm text-blue-800">
                <strong>Test Scenario:</strong> This test simulates adding a new "Financial Reports" box to an existing dashboard grid.
                It tests the LLM's ability to understand layout patterns and add new content that matches the existing design system.
                The result should show a new orange-themed box with 25 reports in the grid layout.
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Input Section */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Original Content (HTML Fragment)
                  </label>
                  <textarea
                    value={originalContent}
                    onChange={(e) => setOriginalContent(e.target.value)}
                    className="w-full h-40 p-3 border border-gray-300 rounded-md font-mono text-sm"
                    placeholder="Enter original HTML content..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Patch Data (Unified Diff)
                  </label>
                  <textarea
                    value={patchData}
                    onChange={(e) => setPatchData(e.target.value)}
                    className="w-full h-32 p-3 border border-gray-300 rounded-md font-mono text-sm"
                    placeholder="Enter patch data..."
                  />
                </div>

                <button
                  onClick={clearResults}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
                >
                  🗑️ Clear Results
                </button>
              </div>

              {/* Results Section */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Test Results
                  </label>
                  <div className="bg-gray-900 text-green-400 p-4 rounded-md h-64 overflow-y-auto font-mono text-sm">
                    {testResults.length === 0 ? (
                      <div className="text-gray-500">No test results yet. Run a test to see output.</div>
                    ) : (
                      testResults.map((result, index) => (
                        <div key={index} className={`mb-2 ${result.success ? 'text-green-400' : 'text-red-400'}`}>
                          <div>[{result.timestamp}] {result.message}</div>
                          {result.details && (
                            <div className="text-gray-400 text-xs ml-4">
                              {JSON.stringify(result.details, null, 2)}
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Patch Result (Text)
                  </label>
                  <textarea
                    value={patchResult}
                    readOnly
                    className="w-full h-24 p-3 border border-gray-300 rounded-md font-mono text-sm bg-gray-50"
                    placeholder="Patch result will appear here..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preview (Rendered)
                  </label>
                  <iframe
                    ref={previewRef}
                    className="w-full h-32 border border-gray-300 rounded-md bg-white"
                    title="Patch Result Preview"
                  />
                </div>
              </div>
            </div>

            {/* Instructions */}
            <div className="mt-8 bg-blue-50 border border-blue-200 rounded-md p-4">
              <h3 className="text-lg font-medium text-blue-900 mb-2">Test Instructions</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li><strong>🧪 Basic Test:</strong> Tests patch application with simple hardcoded data</li>
                <li><strong>🚀 Full Simulation:</strong> Simulates the complete API response flow with your exact data</li>
                <li><strong>🔧 Custom Test:</strong> Tests with the content you provide in the text areas above</li>
                <li><strong>⚡ Direct Test:</strong> Simple test that directly replaces "Login" with "Submit" to verify preview works</li>
                <li><strong>🔍 Debug Steps:</strong> Step-by-step debugging of patch application process</li>
                <li><strong>✅ Simple Fix:</strong> Working solution using direct string replacement instead of unified diff</li>
                <li><strong>🌊 SSE Flow:</strong> Complete simulation of your exact SSE event stream with timing</li>
                <li><strong>🎯 Real UI Flow:</strong> Simulates the exact useEditorV3 generateFromPrompt flow</li>
                <li><strong>✏️ Edit Flow:</strong> Simulates the useEditorV3 editContent flow (for editing existing content)</li>
                <li><strong>🏭 Industry Standard:</strong> Tests the new diff-match-patch library approach with your complex multi-block patch</li>
                <li><strong>🧩 Fragment Edit:</strong> Tests the new fragment-based editing approach that sends only specific HTML elements to the LLM for 70-80% token reduction</li>
                <li><strong>📦 Add Box:</strong> Tests adding new content to existing layouts - simulates adding a "Financial Reports" box to a dashboard grid</li>
                <li><strong>🔗 URL Decode Test:</strong> Tests URL decoding functionality</li>
                <li><strong>Console:</strong> Open browser console (F12) to see detailed debug output</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatchManagerTestPage;
