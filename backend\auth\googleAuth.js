const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const { upsertUser } = require('../services/promptDbService');

// User serialization (for session)
passport.serializeUser((user, done) => {
  console.log('[Auth Debug] Serializing user:', user.id, 'dbId:', user.dbId);
  // Store both the provider ID and the database ID
  done(null, {
    id: user.id,
    dbId: user.dbId,
    provider: user.provider
  });
});

passport.deserializeUser(async (serializedUser, done) => {
  try {
    console.log('[Auth Debug] Deserializing user:', serializedUser.id, 'dbId:', serializedUser.dbId);

    // If we have a database ID, we can fetch the full user from the database
    if (serializedUser.dbId) {
      // In a production app, you would fetch the full user from the database here
      // For now, we'll just use what we have in the session
      done(null, serializedUser);
    } else {
      // If we don't have a database ID, just use what we have
      done(null, serializedUser);
    }
  } catch (error) {
    console.error('[Auth Debug] Error deserializing user:', error);
    done(error);
  }
});

// Configure Google OAuth strategy
passport.use(new GoogleStrategy(
  {
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: process.env.GOOGLE_CALLBACK_URL || '/api/auth/google/callback',
  },
  async (_accessToken, _refreshToken, profile, done) => {
    try {
      console.log('[Auth Debug] Google auth callback received profile:', profile.id);

      // Extract user data from profile
      const email = profile.emails && profile.emails[0] ? profile.emails[0].value : null;
      const photo_url = profile.photos && profile.photos[0] ? profile.photos[0].value : null;

      if (!email) {
        return done(new Error('Email is required for authentication'));
      }

      // Save user to database
      const userData = {
        google_id: profile.id,
        email: email,
        display_name: profile.displayName,
        photo_url: photo_url,
        auth_provider: 'google',
        email_verified: true // Google already verified the email
      };

      console.log('[Auth Debug] Upserting user with data:', userData);

      // Upsert user in database
      const userId = await upsertUser(userData);
      console.log('[Auth Debug] User upserted with database ID:', userId);

      // Return user object for session
      return done(null, {
        id: profile.id,
        dbId: userId, // Include the database ID
        displayName: profile.displayName,
        email: email,
        photo: photo_url,
        provider: profile.provider,
      });
    } catch (error) {
      console.error('[Auth Debug] Error in Google auth callback:', error);
      return done(error);
    }
  }
));

function ensureAuthenticated(req, res, next) {
  // Add CORS headers for all authenticated routes
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Cookie, X-Requested-With, Accept, Cache-Control');
  res.header('Access-Control-Expose-Headers', 'Set-Cookie, X-Auth-Status');

  console.log('[Auth Debug] ensureAuthenticated called for path:', req.path);
  console.log('[Auth Debug] Origin:', req.headers.origin);
  console.log('[Auth Debug] isAuthenticated:', req.isAuthenticated && req.isAuthenticated());

  if (req.isAuthenticated && req.isAuthenticated()) {
    return next();
  }

  // Set CORS headers even for error responses
  res.status(401).json({ error: 'Not authenticated' });
}

module.exports = {
  passport,
  ensureAuthenticated,
};
