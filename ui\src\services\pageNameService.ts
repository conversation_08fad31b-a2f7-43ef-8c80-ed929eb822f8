/**
 * Page Name Service
 *
 * Handles clean page name generation using LLM API with fallback logic.
 * Provides consistent page naming across all page creation scenarios.
 */

export interface PageNameGenerationResult {
  success: boolean;
  pageName: string;
  source: 'api' | 'fallback';
  error?: string;
}

export interface PageNameGenerationOptions {
  maxRetries?: number;
  timeout?: number;
  fallbackEnabled?: boolean;
}

/**
 * Default options for page name generation
 */
const DEFAULT_OPTIONS: Required<PageNameGenerationOptions> = {
  maxRetries: 2,
  timeout: 5000,
  fallbackEnabled: true
};

/**
 * Generates a clean, professional page name from user input using LLM API
 *
 * @param prompt - User input describing the page
 * @param options - Generation options
 * @returns Promise<PageNameGenerationResult>
 */
export async function generateCleanPageName(
  prompt: string,
  options: PageNameGenerationOptions = {}
): Promise<PageNameGenerationResult> {
  const config = { ...DEFAULT_OPTIONS, ...options };

  if (!prompt?.trim()) {
    return {
      success: false,
      pageName: 'New Page',
      source: 'fallback',
      error: 'Empty prompt provided'
    };
  }

  // Try API generation with retries
  for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
    try {
      const result = await generatePageNameFromAPI(prompt, config.timeout);
      if (result.success) {
        return {
          success: true,
          pageName: result.pageName,
          source: 'api'
        };
      }
    } catch (error) {
      console.warn(`Page name API attempt ${attempt} failed:`, error);

      // If this is the last attempt and fallback is disabled, return error
      if (attempt === config.maxRetries && !config.fallbackEnabled) {
        return {
          success: false,
          pageName: 'New Page',
          source: 'fallback',
          error: `API failed after ${config.maxRetries} attempts: ${error.message}`
        };
      }
    }
  }

  // Use fallback if API fails and fallback is enabled
  if (config.fallbackEnabled) {
    const fallbackName = generatePageNameFallback(prompt);
    return {
      success: true,
      pageName: fallbackName,
      source: 'fallback'
    };
  }

  return {
    success: false,
    pageName: 'New Page',
    source: 'fallback',
    error: 'API failed and fallback disabled'
  };
}

/**
 * Generates page name using the LLM API
 */
async function generatePageNameFromAPI(prompt: string, timeout: number): Promise<{ success: boolean; pageName: string }> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch('/api/page_gen/suggest/page_title', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({ query: prompt }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`);
    }

    const result = await response.json();

    if (result.success && result.title?.trim()) {
      return {
        success: true,
        pageName: result.title.trim()
      };
    }

    throw new Error('API returned invalid response format');
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

/**
 * Fallback page name generation using rule-based logic
 */
function generatePageNameFallback(prompt: string): string {
  const cleanPrompt = prompt.toLowerCase().trim();

  // Common page patterns - expanded list with more keywords
  const patterns = [
    { keywords: ['login', 'sign in', 'signin'], name: 'Login' },
    { keywords: ['signup', 'sign up', 'register'], name: 'Sign Up' },
    { keywords: ['contact', 'contact us'], name: 'Contact' },
    { keywords: ['about', 'about us'], name: 'About' },
    { keywords: ['pricing', 'price'], name: 'Pricing' },
    { keywords: ['dashboard', 'admin'], name: 'Dashboard' },
    { keywords: ['profile', 'account'], name: 'Profile' },
    { keywords: ['settings', 'setting', 'config'], name: 'Settings' },
    { keywords: ['help', 'support', 'faq'], name: 'Help' },
    { keywords: ['blog', 'news', 'article'], name: 'Blog' },
    { keywords: ['gallery', 'portfolio'], name: 'Gallery' },
    { keywords: ['service', 'services'], name: 'Services' },
    { keywords: ['product', 'products'], name: 'Products' },
    { keywords: ['home', 'homepage'], name: 'Home' },
    { keywords: ['team', 'our team'], name: 'Team' },
    { keywords: ['career', 'careers', 'jobs'], name: 'Careers' }
  ];

  // Check for pattern matches
  for (const pattern of patterns) {
    if (pattern.keywords.some(keyword => cleanPrompt.includes(keyword))) {
      return pattern.name;
    }
  }

  // Extract meaningful words
  const words = cleanPrompt
    .replace(/^(create|make|build|add|generate|design)\s+/i, '') // Remove action words
    .replace(/\s+(page|section|component|form|modal)\s*$/i, '') // Remove common suffixes
    .split(/\s+/)
    .filter(word => word.length > 2 && !['the', 'and', 'for', 'with', 'that', 'this'].includes(word))
    .slice(0, 2); // Take first 2 meaningful words

  if (words.length > 0) {
    return words
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  return 'New Page';
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use generateCleanPageName instead
 */
export async function generatePageNameFromPrompt(prompt: string): Promise<string> {
  const result = await generateCleanPageName(prompt);
  return result.pageName;
}
