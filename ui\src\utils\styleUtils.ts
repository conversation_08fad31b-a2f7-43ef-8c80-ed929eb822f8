/**
 * Converts a kebab-case string to camelCase
 * @param str The kebab-case string to convert
 * @returns The camelCase version of the string
 */
export function kebabToCamelCase(str: string): string {
  return str.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
}

/**
 * Applies CSS properties to an element
 * @param element The element to apply styles to
 * @param cssText The CSS text to parse and apply
 */
export function applyStyles(element: HTMLElement, cssText: string): void {
  // Quick exit for empty CSS
  if (!cssText.trim()) return;
  
  // Parse CSS properties
  const properties = cssText.split(';')
    .filter(Boolean)
    .map(prop => {
      const [key, value] = prop.split(':').map(p => p.trim());
      return { key, value };
    })
    .filter(({ key, value }) => key && value);
  
  // Apply properties in batch
  properties.forEach(({ key, value }) => {
    const camelKey = kebabToCamelCase(key);
    try {
      (element.style as any)[camelKey] = value;
    } catch (error) {
      console.warn(`Failed to apply style "${key}: ${value}"`, error);
    }
  });
}

/**
 * Applies a border radius value based on predefined sizes
 * @param element The element to apply the border radius to
 * @param value The border radius value or size name
 */
export function applyBorderRadius(element: HTMLElement, value: string): void {
  switch (value) {
    case 'None':
      element.style.borderRadius = '0';
      break;
    case 'XS':
      element.style.borderRadius = '4px';
      break;
    case 'SM':
      element.style.borderRadius = '8px';
      break;
    case 'MD':
      element.style.borderRadius = '12px';
      break;
    case 'LG':
      element.style.borderRadius = '16px';
      break;
    case 'XL':
      element.style.borderRadius = '24px';
      break;
    case 'Full':
      element.style.borderRadius = '9999px';
      break;
    default:
      element.style.borderRadius = value;
  }
}
