import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON>Loader, FiMonitor, FiSmartphone, FiZap, FiTarget, FiLink, FiMenu, FiChevronDown, FiPlus } from 'react-icons/fi';
import { CreateProjectModal } from '../components/CreateProjectModal';
import { ProjectDropdown } from '../components/ProjectDropdown';
import { PagesSidebar } from '../components/PagesSidebar';
import { PromptModal } from '../components/PromptModal';
import { Project, Page, getProjectList, updateProject, deleteProject } from '../services/pageGenService';

// CSS to hide textarea scrollbar
const textareaStyle = `
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
`;

/**
 * PromptInputPageV3 - Readdy.ai style initial prompt page
 * Sophisticated design matching StartingPage.png reference
 */

type DeviceType = 'desktop' | 'mobile';

export function PromptInputPageV3() {
  const [prompt, setPrompt] = useState('');
  const [deviceType, setDeviceType] = useState<DeviceType>('desktop');
  const [isGenerating, setIsGenerating] = useState(false);
  const navigate = useNavigate();

  // Project management state
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [isLoadingProjects, setIsLoadingProjects] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showPromptModal, setShowPromptModal] = useState(false);
  const [showPagesSidebar, setShowPagesSidebar] = useState(false);
  const [isCreatingNewPage, setIsCreatingNewPage] = useState(false);

  // Load projects on component mount
  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    setIsLoadingProjects(true);
    try {
      const response = await getProjectList(1, 50); // Load first 50 projects
      setProjects(response.projects);

      // Auto-select the latest project if available (projects are sorted by created_at DESC)
      if (response.projects.length > 0) {
        setSelectedProject(response.projects[0]); // Always select the latest project
      }
    } catch (error) {
      console.error('Error loading projects:', error);
    } finally {
      setIsLoadingProjects(false);
    }
  };

  // Event handlers
  const handleProjectCreated = (project: Project) => {
    setProjects(prev => [project, ...prev]);
    setSelectedProject(project);
    setShowCreateModal(false);
  };

  const handleProjectSelect = (project: Project) => {
    setSelectedProject(project);
  };

  const handleCreateNewProject = () => {
    setShowCreateModal(true);
  };

  const handleProjectRename = async (project: Project, newName: string) => {
    try {
      const response = await updateProject(project.id, {
        title: newName,
        description: project.description
      });

      if (response.success) {
        // Update the project in the local state
        setProjects(prev => prev.map(p =>
          p.id === project.id ? { ...p, title: newName } : p
        ));

        // Update selected project if it's the one being renamed
        if (selectedProject?.id === project.id) {
          setSelectedProject(prev => prev ? { ...prev, title: newName } : null);
        }
      }
    } catch (error) {
      console.error('Error renaming project:', error);
      alert('Failed to rename project. Please try again.');
    }
  };

  const handleProjectDelete = async (project: Project) => {
    try {
      const response = await deleteProject(project.id);

      if (response.success) {
        // Remove the project from local state
        setProjects(prev => prev.filter(p => p.id !== project.id));

        // If the deleted project was selected, select another one or clear selection
        if (selectedProject?.id === project.id) {
          const remainingProjects = projects.filter(p => p.id !== project.id);
          setSelectedProject(remainingProjects.length > 0 ? remainingProjects[0] : null);
        }
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      alert('Failed to delete project. Please try again.');
    }
  };

  const handlePageSelect = (page: Page) => {
    // Navigate to refactored editor with the selected page
    navigate('/editor-v3-refactored', {
      state: {
        sessionId: page.id,
        projectId: selectedProject?.id,
        loadExistingPage: true
      }
    });
  };

  const handleCreatePage = () => {
    if (selectedProject) {
      // Set new page creation mode
      setIsCreatingNewPage(true);

      // Clear the current prompt to start fresh
      setPrompt('');

      // Close the pages sidebar to focus on the textarea
      setShowPagesSidebar(false);

      // Focus on the textarea (we'll add a ref for this)
      setTimeout(() => {
        const textarea = document.querySelector('textarea');
        if (textarea) {
          textarea.focus();
        }
      }, 100);
    }
  };

  const handlePromptGenerate = (promptText: string) => {
    setPrompt(promptText);
    setShowPromptModal(false);
    // Trigger the normal submit flow
    handleSubmit(null, promptText);
  };

  const handleSubmit = async (e: React.FormEvent | null, customPrompt?: string) => {
    if (e) e.preventDefault();
    const promptToUse = customPrompt || prompt;
    if (!promptToUse.trim() || isGenerating) return;

    setIsGenerating(true);

    try {
      // Navigate to plan review page with the prompt
      navigate('/plan-v3', {
        state: {
          prompt: promptToUse.trim(),
          deviceType,
          projectId: selectedProject?.id,
          isNewPage: isCreatingNewPage // Pass the new page creation flag
        }
      });

      // Reset new page creation mode
      setIsCreatingNewPage(false);
    } catch (error) {
      console.error('Error:', error);
      setIsGenerating(false);
    }
  };

  // Determine what to show based on project state
  const shouldShowCreateProject = !isLoadingProjects && projects.length === 0;
  const shouldShowPromptInput = !isLoadingProjects && projects.length > 0;

  return (
    <>
      <div className="h-screen bg-gray-50 flex flex-col overflow-hidden">
        <style>{textareaStyle}</style>

        {/* Header with Project Management */}
        {shouldShowPromptInput && (
          <div className="bg-white border-b border-gray-200 px-4 py-3">
            <div className="flex items-center justify-between max-w-7xl mx-auto">
              <div className="flex items-center space-x-3">
                {/* Left Pane Toggle */}
                <button
                  onClick={() => setShowPagesSidebar(!showPagesSidebar)}
                  className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                  disabled={!selectedProject}
                >
                  <FiMenu className="w-5 h-5" />
                </button>

                {/* Project Dropdown */}
                <ProjectDropdown
                  projects={projects}
                  selectedProject={selectedProject}
                  onProjectSelect={handleProjectSelect}
                  onCreateNew={handleCreateNewProject}
                  onProjectRename={handleProjectRename}
                  onProjectDelete={handleProjectDelete}
                  isLoading={isLoadingProjects}
                />
              </div>

              <div className="text-sm text-gray-500">
                {selectedProject ? `Project: ${selectedProject.title}` : 'No project selected'}
              </div>
            </div>
          </div>
        )}

        {/* Pages Sidebar */}
        <PagesSidebar
          isOpen={showPagesSidebar}
          onClose={() => setShowPagesSidebar(false)}
          project={selectedProject}
          onPageSelect={handlePageSelect}
          onCreatePage={handleCreatePage}
        />

        {/* Main Content */}
        <div className="flex-1 flex flex-col justify-center px-4 overflow-hidden">
          {isLoadingProjects ? (
            /* Show Loading State */
            <div className="w-full max-w-md mx-auto text-center">
              <FiLoader className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading your projects...</h2>
              <p className="text-gray-600">Please wait while we fetch your projects</p>
            </div>
          ) : shouldShowCreateProject ? (
            /* Show Create Project Screen when no projects exist */
            <div className="w-full max-w-md mx-auto">
              <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome to JustPrototype</h1>
                <p className="text-gray-600">Create your first project to get started</p>
              </div>
              <CreateProjectModal
                isOpen={true}
                onClose={() => {}} // Don't allow closing when no projects
                onProjectCreated={handleProjectCreated}
                showCancel={false}
              />
            </div>
          ) : shouldShowPromptInput ? (
            /* Show Prompt Input when projects exist */
            <div className="w-full max-w-3xl mx-auto">
              {/* Header */}
              <div className="text-center mb-4">
                <h1 className="text-4xl font-bold text-gray-900">
                  {isCreatingNewPage ? (
                    <>
                      Describe your new
                      <span className="block text-blue-600">page</span>
                    </>
                  ) : (
                    <>
                      What would you like to
                      <span className="block text-blue-600">design today?</span>
                    </>
                  )}
                </h1>
                {isCreatingNewPage && (
                  <button
                    onClick={() => setIsCreatingNewPage(false)}
                    className="mt-2 text-sm text-gray-500 hover:text-gray-700 underline"
                  >
                    Cancel new page creation
                  </button>
                )}
              </div>

              {/* Prompt Input - Main Focus Area */}
              <form onSubmit={handleSubmit} className="mb-3">
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
                  <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder={isCreatingNewPage
                      ? "Describe the new page you want to create...\n\nFor example: 'A contact page with a form, company address, and map. Include fields for name, email, message, and a submit button.'"
                      : "Describe your prototype idea in detail...\n\nFor example: 'A modern landing page for a SaaS product with pricing section, testimonials, and contact form. Use a clean, professional design with blue and white colors.'"
                    }
                    className="w-full h-40 px-6 py-4 text-gray-900 placeholder-gray-500 border-0 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 no-scrollbar text-base leading-relaxed"
                    disabled={isGenerating}
                  />

                  {/* Device Selection and Submit */}
                  <div className="flex items-center justify-between px-6 py-3 bg-gray-50 border-t border-gray-100">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600 mr-2">Target:</span>
                      <button
                        type="button"
                        onClick={() => setDeviceType('desktop')}
                        className={`px-3 py-1 text-sm rounded-md transition-colors ${
                          deviceType === 'desktop'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                      >
                        <FiMonitor className="inline mr-1" />
                        Desktop
                      </button>
                      <button
                        type="button"
                        onClick={() => setDeviceType('mobile')}
                        className={`px-3 py-1 text-sm rounded-md transition-colors ${
                          deviceType === 'mobile'
                            ? 'bg-blue-600 text-white'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                        }`}
                      >
                        <FiSmartphone className="inline mr-1" />
                        Mobile
                      </button>
                    </div>

                    <button
                      type="submit"
                      disabled={!prompt.trim() || isGenerating}
                      className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                    >
                      {isGenerating ? (
                        <>
                          <FiLoader className="animate-spin" />
                          <span>{isCreatingNewPage ? 'Creating Page...' : 'Creating...'}</span>
                        </>
                      ) : (
                        <>
                          <FiSend />
                          <span>{isCreatingNewPage ? 'Create Page' : 'Create'}</span>
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </form>

              {/* Examples - Compact */}
              <div className="text-center">
                <p className="text-xs text-gray-500 mb-1">Quick examples:</p>
                <div className="flex flex-wrap justify-center gap-1 max-w-4xl mx-auto">
                  {(isCreatingNewPage ? [
                    'Contact page with form and company information',
                    'About us page with team members and company story',
                    'Services page with pricing and feature comparison',
                    'Blog page with article list and search functionality'
                  ] : [
                    'Modern SaaS landing page with pricing and contact form',
                    'E-commerce product page with shopping cart',
                    'Dashboard with user profile and settings',
                    'Portfolio website with project gallery'
                  ]).map((example, index) => (
                    <button
                      key={index}
                      onClick={() => setPrompt(example)}
                      className="px-2 py-1 text-xs text-blue-600 bg-blue-50 rounded-full hover:bg-blue-100 transition-colors disabled:opacity-50"
                      disabled={isGenerating}
                    >
                      {example}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          ) : null}
        </div>
      </div>

      {/* Modals */}
      <CreateProjectModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onProjectCreated={handleProjectCreated}
        showCancel={true}
      />

      <PromptModal
        open={showPromptModal}
        onClose={() => setShowPromptModal(false)}
        onGenerate={handlePromptGenerate}
      />
    </>
  );
}
