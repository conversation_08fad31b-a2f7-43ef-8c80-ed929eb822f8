import { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Alert,
  IconButton,
  Divider,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Close as CloseIcon,
  Check as CheckIcon,
} from '@mui/icons-material';
import { llmService, PROVIDERS } from '../services/llmService';

function ApiSettings({ open, onClose }) {
  const [provider, setProvider] = useState(llmService.getCurrentProvider());
  const [model, setModel] = useState(llmService.getCurrentModel());
  // API keys are now stored in the backend
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [error, setError] = useState('');

  const providers = llmService.getProviders();
  const [availableModels, setAvailableModels] = useState(llmService.getModels());

  useEffect(() => {
    // Reset state when dialog opens
    if (open) {
      const currentProvider = llmService.getCurrentProvider();
      setProvider(currentProvider);
      setModel(llmService.getCurrentModel());
      setAvailableModels(llmService.getModelsForProvider(currentProvider));
      setSaveSuccess(false);
      setError('');
    }
  }, [open]);

  const handleProviderChange = (event) => {
    const newProvider = event.target.value;
    setProvider(newProvider);

    // Update available models for this provider
    const models = llmService.getModelsForProvider(newProvider);
    setAvailableModels(models);

    // Set default model for this provider
    const savedModel = localStorage.getItem(`${newProvider}_model`);
    if (savedModel) {
      setModel(savedModel);
    } else if (models.length > 0) {
      setModel(models[0].id);
    } else {
      setModel('');
    }
  };

  const handleModelChange = (event) => {
    setModel(event.target.value);
  };

  const handleSave = () => {
    try {
      llmService.setProvider(provider);
      llmService.setModel(model);

      setSaveSuccess(true);
      setError('');

      // Reset success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (err) {
      setError('Failed to save settings: ' + err.message);
    }
  };

  const getProviderInfo = (providerId) => {
    switch (providerId) {
      case PROVIDERS.OPENAI:
        return {
          name: 'OpenAI',
          description: 'Uses GPT-4 Turbo for generating HTML code.',
          apiUrl: 'https://platform.openai.com/api-keys',
        };
      case PROVIDERS.DEEPSEEK:
        return {
          name: 'Deepseek',
          description: 'Uses Deepseek Coder for generating HTML code.',
          apiUrl: 'https://platform.deepseek.com',
        };
      case PROVIDERS.ANTHROPIC:
        return {
          name: 'Anthropic',
          description: 'Uses Claude 3 Opus for generating HTML code.',
          apiUrl: 'https://console.anthropic.com/keys',
        };
      case PROVIDERS.MISTRAL:
        return {
          name: 'Mistral AI',
          description: 'Uses Mistral Large for generating HTML code.',
          apiUrl: 'https://console.mistral.ai/api-keys/',
        };
      case PROVIDERS.GOOGLE:
        return {
          name: 'Google AI',
          description: 'Uses Gemini Pro for generating HTML code.',
          apiUrl: 'https://aistudio.google.com/app/apikey',
        };
      default:
        return {
          name: 'Unknown Provider',
          description: 'No information available.',
          apiUrl: '#',
        };
    }
  };

  const currentProviderInfo = getProviderInfo(provider);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        pb: 1
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <SettingsIcon sx={{ mr: 1, color: 'primary.main' }} />
          <Typography variant="h6" component="div">
            Model Settings
          </Typography>
        </Box>
        <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ pt: 3 }}>
        {saveSuccess && (
          <Alert
            icon={<CheckIcon fontSize="inherit" />}
            severity="success"
            sx={{ mb: 3 }}
          >
            Settings saved successfully!
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <FormControl fullWidth sx={{ mb: 3 }}>
          <InputLabel id="provider-select-label">LLM Provider</InputLabel>
          <Select
            labelId="provider-select-label"
            id="provider-select"
            value={provider}
            label="LLM Provider"
            onChange={handleProviderChange}
          >
            {providers.map((p) => (
              <MenuItem key={p.id} value={p.id}>{p.name}</MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl fullWidth sx={{ mb: 3 }}>
          <InputLabel id="model-select-label">Model</InputLabel>
          <Select
            labelId="model-select-label"
            id="model-select"
            value={model}
            label="Model"
            onChange={handleModelChange}
            disabled={availableModels.length === 0}
          >
            {availableModels.map((m) => (
              <MenuItem key={m.id} value={m.id}>
                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                  <Typography variant="body2">{m.name}</Typography>
                  <Typography variant="caption" color="text.secondary">{m.description}</Typography>
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            {currentProviderInfo.name}
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {currentProviderInfo.description}
          </Typography>
        </Box>

        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
          API keys are now securely stored on the server.
        </Typography>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          color="primary"
          disabled={!provider}
        >
          Save Settings
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default ApiSettings;
