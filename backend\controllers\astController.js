const astStore = require('../services/ast/astStore');
const llmService = require('../services/llmServiceV2');
const withSSE = require('../services/sse/withSSE');

/**
 * GET /api/ast/current
 * Get current AST state
 */
async function getCurrentAST(req, res) {
  try {
    const current = astStore.getCurrentSnapshot();
    res.json(current);
  } catch (error) {
    console.error('Error getting current AST:', error);
    res.status(500).json({ error: error.message });
  }
}

// Store active SSE connections
const activeConnections = new Map();

/**
 * POST /api/ast/plan
 * Generate initial AST from plan
 */
async function generatePlan(req, res) {
  const { prompt, provider = 'openai' } = req.body;
  const requestId = req.get('X-Request-ID') || req.query.requestId;
  const acceptHeader = req.headers.accept || '';
  const isSSE = acceptHeader.includes('text/event-stream');

  if (!prompt) {
    return res.status(400).json({ error: 'Prompt is required' });
  }

  // Handle SSE connection
  if (isSSE) {
    const effectiveRequestId = requestId || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    console.log(`New SSE connection for request: ${effectiveRequestId}`);
    
    // Set up SSE headers
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('X-Accel-Buffering', 'no');
    res.flushHeaders();
    
    // Send initial connection established event
    res.write(`event: start\ndata: ${
      JSON.stringify({ 
        type: 'start',
        message: 'SSE connection established',
        requestId: effectiveRequestId 
      })
    }\n\n`);
    
    try {
      // The llmService will handle the SSE events directly through the response object
      await llmService.generatePlan(prompt, res, provider);
    } catch (error) {
      console.error('Error in SSE handler:', error);
      // Only send error if the response hasn't been closed
      if (!res.writableEnded) {
        res.write(`event: error\ndata: ${
          JSON.stringify({ 
            type: 'error',
            error: error.message || 'Internal server error' 
          })
        }\n\n`);
        res.end();
      }
    }
    
    return; // End of SSE handler
  }
  
  // Handle regular HTTP request
  try {
    // For non-SSE requests, we'll capture the output in a buffer
    let outputBuffer = [];
    
    // Create a mock response object to capture the output
    const mockRes = {
      write: (data) => {
        outputBuffer.push(data);
        return true;
      },
      end: () => {},
      setHeader: () => {},
      flushHeaders: () => {}
    };
    
    // Call the service with the mock response
    await llmService.generatePlan(prompt, mockRes, provider);
    
    // Process the captured output
    const result = outputBuffer.join('');
    
    res.json({
      success: true,
      result: result
    });
  } catch (error) {
    console.error('Error generating plan:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to generate plan'
    });
  }
}

/**
 * POST /api/ast/patch
 * Apply patch to AST
 */
async function patchAST(req, res) {
  const { snapshotId, patch } = req.body;

  if (!snapshotId || !patch) {
    return res.status(400).json({ error: 'Snapshot ID and patch are required' });
  }

  try {
    // Validate patch operations
    const validation = astStore.schemaValidator.validatePatch(patch);
    if (!validation.valid) {
      return res.status(400).json({ error: validation.errors.join(', ') });
    }

    // Apply patch and get new version
    const result = astStore.applyPatch(snapshotId, patch);
    res.json(result);
  } catch (error) {
    console.error('Error applying patch:', error);
    res.status(500).json({ error: error.message });
  }
}

/**
 * GET /api/ast/snapshots
 * List available snapshots
 */
async function listSnapshots(req, res) {
  try {
    const snapshots = astStore.listSnapshots();
    res.json(snapshots);
  } catch (error) {
    console.error('Error listing snapshots:', error);
    res.status(500).json({ error: error.message });
  }
}

/**
 * GET /api/ast/snapshots/:id
 * Get specific snapshot
 */
async function getSnapshot(req, res) {
  try {
    const snapshot = astStore.getSnapshot(req.params.id);
    if (!snapshot) {
      return res.status(404).json({ error: 'Snapshot not found' });
    }
    res.json(snapshot);
  } catch (error) {
    console.error('Error getting snapshot:', error);
    res.status(500).json({ error: error.message });
  }
}

// Apply withSSE middleware only for non-SSE endpoints
const wrappedGeneratePlan = (req, res, next) => {
  const acceptHeader = req.headers.accept || '';
  const isSSE = acceptHeader.includes('text/event-stream');
  
  if (isSSE) {
    // For SSE connections, don't use withSSE middleware
    generatePlan(req, res, next);
  } else {
    // For regular HTTP requests, use withSSE middleware
    withSSE(generatePlan)(req, res, next);
  }
};

module.exports = {
  getCurrentAST,
  generatePlan: wrappedGeneratePlan,
  patchAST,
  listSnapshots,
  getSnapshot
};
