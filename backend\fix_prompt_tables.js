// <PERSON>ript to fix the prompt tables
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// Create a new pool using the connection string from environment variables
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function fixPromptTables() {
  console.log('Starting to fix prompt tables...');
  
  try {
    // Check if the database is accessible
    const pingResult = await pool.query('SELECT 1 as ping');
    console.log(`Database connection: ${pingResult.rows[0].ping === 1 ? 'SUCCESS' : 'FAILED'}`);
    
    // Read and execute the db_prompt_schema.sql file
    const sqlPath = path.join(__dirname, 'db_prompt_schema.sql');
    if (fs.existsSync(sqlPath)) {
      console.log('Reading db_prompt_schema.sql...');
      const sql = fs.readFileSync(sqlPath, 'utf8');
      
      console.log('Executing SQL...');
      await pool.query(sql);
      console.log('Successfully executed db_prompt_schema.sql');
      
      // Check if tables exist now
      const tables = ['prompts', 'prompt_iterations'];
      for (const table of tables) {
        const tableCheck = await pool.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          ) as exists
        `, [table]);
        
        console.log(`Table '${table}' exists: ${tableCheck.rows[0].exists}`);
      }
      
      // Test inserting a prompt
      try {
        console.log('Testing prompt insertion...');
        
        // Get a valid user ID
        const userResult = await pool.query('SELECT id FROM users LIMIT 1');
        
        if (userResult.rows.length > 0) {
          const userId = userResult.rows[0].id;
          console.log(`Found user with ID: ${userId}`);
          
          // Insert a test prompt
          const promptResult = await pool.query(`
            INSERT INTO prompts (user_id, prompt_text)
            VALUES ($1, $2)
            RETURNING id
          `, [userId, 'Test prompt from fix_prompt_tables.js']);
          
          const promptId = promptResult.rows[0].id;
          console.log(`Inserted test prompt with ID: ${promptId}`);
          
          // Insert a test prompt iteration
          const iterationResult = await pool.query(`
            INSERT INTO prompt_iterations (prompt_id, iteration_number, input_text, output_text)
            VALUES ($1, $2, $3, $4)
            RETURNING id
          `, [promptId, 1, 'Test input', 'Test output']);
          
          const iterationId = iterationResult.rows[0].id;
          console.log(`Inserted test prompt iteration with ID: ${iterationId}`);
          
          console.log('Test insertion successful!');
        } else {
          console.log('No users found in the database. Cannot test prompt insertion.');
        }
      } catch (testError) {
        console.error('Error testing prompt insertion:', testError);
      }
    } else {
      console.error(`db_prompt_schema.sql not found at ${sqlPath}`);
    }
    
    console.log('Fix prompt tables process completed.');
  } catch (error) {
    console.error('Error fixing prompt tables:', error);
  } finally {
    // Close the pool
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the script
fixPromptTables().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
