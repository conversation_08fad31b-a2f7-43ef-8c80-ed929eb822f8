.container {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  max-width: none;
  display: flex;
  flex-direction: column;
  background: #f9fafb;
}

/* Loading state */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
}

.spinner {
  width: 3rem;
  height: 3rem;
  border: 0.25rem solid rgba(79, 70, 229, 0.2);
  border-radius: 50%;
  border-top-color: #4f46e5;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loadingText {
  font-size: 1rem;
  color: #6b7280;
}

/* Error state */
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  text-align: center;
}

.errorIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  background-color: #fee2e2;
  color: #ef4444;
  font-size: 2rem;
  font-weight: bold;
  border-radius: 50%;
  margin-bottom: 1.5rem;
}

.errorTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
}

.errorMessage {
  font-size: 1rem;
  color: #6b7280;
  max-width: 500px;
  margin-bottom: 1.5rem;
}

.backButton {
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.backButton:hover {
  background-color: #4338ca;
}

/* Header */
.header {
  max-width: 900px;
  width: 100%;
  margin: 0 auto 2rem auto;
  background: none;
  box-shadow: none;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.prototypeInfo {
  flex: 1;
}

.prototypeName {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  margin: 0 0 0.5rem 0;
}

.sharedBy {
  font-size: 0.875rem;
  color: #6b7280;
}

.ownerName {
  font-weight: 500;
  color: #4b5563;
}

.accessInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.publicBadge, .privateBadge {
  display: inline-flex;
  align-items: center;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  margin-bottom: 0.5rem;
}

.publicBadge {
  background-color: #f3f4f6;
  color: #4b5563;
}

.privateBadge {
  background-color: #e0f2fe;
  color: #0369a1;
}

.accessLevel {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.expiryInfo {
  font-size: 0.75rem;
  color: #ef4444;
}

/* Prototype content */
.prototypeContainer {
  flex: 1;
  width: 100vw;
  height: 100%;
  max-width: 100vw;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: none;
  border: none;
  border-radius: 0;
  overflow: hidden;
}

.prototypeFrame {
  width: 100vw;
  height: 80vh;
  min-height: 400px;
  border: none;
  display: block;
  background: #fff;
  box-shadow: 0 2px 16px rgba(0,0,0,0.08);
  border-radius: 12px;
}

.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #6b7280;
  font-size: 0.875rem;
}

/* Comments section */
.commentsSection {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.commentsTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
}

.commentsList {
  margin-bottom: 1.5rem;
}

.emptyComments {
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  color: #6b7280;
  font-size: 0.875rem;
  text-align: center;
}

.addComment {
  margin-top: 1rem;
}

.commentInput {
  width: 100%;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #111827;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  resize: vertical;
  margin-bottom: 0.75rem;
  transition: border-color 0.2s;
}

.commentInput:focus {
  outline: none;
  border-color: #a5b4fc;
  box-shadow: 0 0 0 3px rgba(165, 180, 252, 0.5);
}

.commentButton {
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.commentButton:hover {
  background-color: #4338ca;
}

/* Edit controls */
.editControls {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
}

.editButton {
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.editButton:hover {
  background-color: #4338ca;
}

/* Branding link */
.brandingLink {
  position: fixed;
  top: 16px;
  left: 24px;
  color: #2563eb;
  font-weight: 700;
  font-size: 18px;
  text-decoration: none;
  letter-spacing: 1px;
  z-index: 10;
}

/* Responsive styles */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
  }

  .accessInfo {
    align-items: flex-start;
    margin-top: 1rem;
  }
}
