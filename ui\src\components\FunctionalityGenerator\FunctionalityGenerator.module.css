/* Styles for the functionality generator component */
.generatorButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ff9800;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.generatorButton:hover {
  background-color: #f57c00;
}

.generatorButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.generatorIcon {
  margin-right: 6px;
}

.generatorModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.generatorModalContent {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.generatorModalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.generatorModalTitle {
  font-size: 18px;
  font-weight: 600;
}

.generatorModalClose {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
}

.generatorModalBody {
  margin-bottom: 16px;
}

.generatorModalLabel {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.generatorModalInput {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 16px;
}

.generatorModalTextarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-height: 100px;
  resize: vertical;
}

.generatorModalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.generatorModalButton {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  border: none;
}

.generatorModalCancel {
  background-color: #f5f5f5;
  color: #333;
}

.generatorModalGenerate {
  background-color: #ff9800;
  color: white;
}

.generatorModalGenerate:hover {
  background-color: #f57c00;
}

.generatorModalGenerate:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
