/**
 * Automated Test Scenarios for EditorV3 Refactored
 * Run these in browser console for quick testing
 */

// Test Scenarios Object
const testScenarios = {
  basic: {
    name: "Basic Content Generation & Editing",
    initialPrompt: "Create a modern landing page with hero section, features, and contact form",
    edits: [
      "Change the hero background to a blue gradient",
      "Add a call-to-action button in the hero section",
      "Make the features section have 3 columns",
      "Change all buttons to have rounded corners",
      "Add a footer with social media links"
    ]
  },
  
  ecommerce: {
    name: "E-commerce Product Page",
    initialPrompt: "Create an e-commerce product page with image gallery, product details, and buy button",
    edits: [
      "Add a product reviews section below the details",
      "Change the buy button color to green",
      "Add a related products section",
      "Make the image gallery have thumbnail navigation",
      "Add a quantity selector next to the buy button"
    ]
  },
  
  portfolio: {
    name: "Portfolio Website",
    initialPrompt: "Create a personal portfolio website with about section, projects gallery, and contact form",
    edits: [
      "Add a skills section with progress bars",
      "Change the projects gallery to a masonry layout",
      "Add social media icons to the header",
      "Make the contact form have validation styling",
      "Add a testimonials section"
    ]
  },
  
  multipage: {
    name: "Multi-page Business Site",
    initialPrompt: "Create a business website with navigation to About, Services, and Contact pages",
    edits: [
      "Click on the About link to create About page",
      "Click on the Services link to create Services page", 
      "Click on the Contact link to create Contact page",
      "Use the Link All Pages button to connect navigation",
      "Switch between pages to test navigation"
    ]
  },
  
  complex: {
    name: "Complex Layout Changes",
    initialPrompt: "Create a blog homepage with article cards, sidebar, and header navigation",
    edits: [
      "Move the sidebar from right to left side",
      "Change the article layout to a masonry grid",
      "Add a search bar to the header",
      "Make the header sticky on scroll",
      "Add pagination at the bottom"
    ]
  }
};

// Helper Functions
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const logTest = (message, type = 'info') => {
  const colors = {
    info: 'color: #2563eb',
    success: 'color: #059669', 
    error: 'color: #dc2626',
    warning: 'color: #d97706'
  };
  console.log(`%c🧪 ${message}`, colors[type]);
};

const waitForElement = async (selector, timeout = 10000) => {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    const element = document.querySelector(selector);
    if (element) return element;
    await delay(100);
  }
  throw new Error(`Element ${selector} not found within ${timeout}ms`);
};

const waitForNoLoading = async (timeout = 30000) => {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    const loadingElements = document.querySelectorAll('[class*="loading"], [class*="generating"], .animate-spin');
    if (loadingElements.length === 0) return;
    await delay(500);
  }
  throw new Error(`Loading did not complete within ${timeout}ms`);
};

// Test Execution Functions
const runBasicTest = async (scenario) => {
  logTest(`Starting test: ${scenario.name}`);
  
  try {
    // 1. Enter initial prompt
    logTest('Step 1: Entering initial prompt');
    const textarea = await waitForElement('textarea');
    textarea.value = scenario.initialPrompt;
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
    
    // 2. Submit prompt
    const submitButton = await waitForElement('button[type="submit"]');
    submitButton.click();
    
    // 3. Wait for generation to complete
    logTest('Step 2: Waiting for initial generation...');
    await waitForNoLoading();
    logTest('Initial generation completed', 'success');
    
    // 4. Run edit tests
    for (let i = 0; i < scenario.edits.length; i++) {
      const edit = scenario.edits[i];
      logTest(`Step ${i + 3}: Testing edit - "${edit}"`);
      
      // Enter edit prompt
      const editTextarea = await waitForElement('textarea');
      editTextarea.value = edit;
      editTextarea.dispatchEvent(new Event('input', { bubbles: true }));
      
      // Submit edit
      const editSubmitButton = await waitForElement('button[type="submit"]');
      editSubmitButton.click();
      
      // Wait for edit to complete
      await waitForNoLoading();
      logTest(`Edit ${i + 1} completed`, 'success');
      
      // Small delay between edits
      await delay(1000);
    }
    
    logTest(`✅ Test "${scenario.name}" completed successfully!`, 'success');
    return true;
    
  } catch (error) {
    logTest(`❌ Test "${scenario.name}" failed: ${error.message}`, 'error');
    return false;
  }
};

const runMultipageTest = async () => {
  logTest('Starting multi-page test');
  
  try {
    // 1. Create initial page with navigation
    logTest('Step 1: Creating page with navigation');
    const textarea = await waitForElement('textarea');
    textarea.value = "Create a business website with navigation bar containing Home, About, Services, and Contact links";
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
    
    const submitButton = await waitForElement('button[type="submit"]');
    submitButton.click();
    
    await waitForNoLoading();
    logTest('Initial page with navigation created', 'success');
    
    // 2. Test navigation clicks (this would need to be done manually in the iframe)
    logTest('Step 2: Manual step - Click navigation links in the preview to create pages');
    logTest('Step 3: Manual step - Use "Link All Pages" button to connect navigation');
    logTest('Step 4: Manual step - Test page switching in the sidebar');
    
    logTest('Multi-page test setup completed - manual steps required', 'warning');
    return true;
    
  } catch (error) {
    logTest(`❌ Multi-page test failed: ${error.message}`, 'error');
    return false;
  }
};

// Performance Testing
const measurePerformance = async (scenario) => {
  logTest(`Measuring performance for: ${scenario.name}`);
  
  const startTime = performance.now();
  
  try {
    // Enter prompt
    const textarea = await waitForElement('textarea');
    textarea.value = scenario.initialPrompt;
    textarea.dispatchEvent(new Event('input', { bubbles: true }));
    
    // Submit and measure
    const submitTime = performance.now();
    const submitButton = await waitForElement('button[type="submit"]');
    submitButton.click();
    
    // Wait for completion
    await waitForNoLoading();
    const endTime = performance.now();
    
    const totalTime = endTime - startTime;
    const generationTime = endTime - submitTime;
    
    logTest(`⏱️ Performance Results:`, 'info');
    logTest(`   Total time: ${totalTime.toFixed(2)}ms`, 'info');
    logTest(`   Generation time: ${generationTime.toFixed(2)}ms`, 'info');
    
    return { totalTime, generationTime };
    
  } catch (error) {
    logTest(`❌ Performance test failed: ${error.message}`, 'error');
    return null;
  }
};

// Main Test Runner
const runAllTests = async () => {
  logTest('🚀 Starting comprehensive test suite');
  
  const results = {};
  
  for (const [key, scenario] of Object.entries(testScenarios)) {
    if (key === 'multipage') {
      // Skip automated multipage test as it requires manual interaction
      continue;
    }
    
    logTest(`\n--- Testing ${scenario.name} ---`);
    results[key] = await runBasicTest(scenario);
    
    // Delay between tests
    await delay(2000);
  }
  
  // Summary
  logTest('\n📊 Test Results Summary:', 'info');
  const passed = Object.values(results).filter(r => r).length;
  const total = Object.keys(results).length;
  
  logTest(`Passed: ${passed}/${total}`, passed === total ? 'success' : 'warning');
  
  if (passed === total) {
    logTest('🎉 All tests passed!', 'success');
  } else {
    logTest('⚠️ Some tests failed - check logs above', 'warning');
  }
  
  return results;
};

// Quick Test Functions
const quickTest = async () => {
  logTest('🏃‍♂️ Running quick test');
  return await runBasicTest(testScenarios.basic);
};

const performanceTest = async () => {
  logTest('⚡ Running performance test');
  return await measurePerformance(testScenarios.basic);
};

// Export for console use
window.editorTests = {
  scenarios: testScenarios,
  runAllTests,
  runBasicTest,
  runMultipageTest,
  measurePerformance,
  quickTest,
  performanceTest,
  
  // Helper to run specific test
  run: (scenarioName) => {
    const scenario = testScenarios[scenarioName];
    if (!scenario) {
      logTest(`❌ Scenario "${scenarioName}" not found`, 'error');
      return;
    }
    return runBasicTest(scenario);
  }
};

// Instructions
console.log(`
🧪 EditorV3 Test Suite Loaded!

Available commands:
- editorTests.quickTest()           // Run basic test
- editorTests.performanceTest()     // Measure performance  
- editorTests.runAllTests()         // Run all automated tests
- editorTests.run('basic')          // Run specific test
- editorTests.scenarios             // View all test scenarios

Available test scenarios:
${Object.keys(testScenarios).map(key => `- ${key}: ${testScenarios[key].name}`).join('\n')}

To start testing:
1. Navigate to /editor-v3-refactored
2. Open browser console
3. Run: editorTests.quickTest()
`);
