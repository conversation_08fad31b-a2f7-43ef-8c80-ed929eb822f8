import React, { useState, useEffect } from 'react';
import { <PERSON>File, FiPlus, FiX, FiLoader } from 'react-icons/fi';
import { Project, Page, getPageList } from '../services/pageGenService';

interface PagesSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | null;
  onPageSelect: (page: Page) => void;
  onCreatePage: () => void;
}

export function PagesSidebar({
  isOpen,
  onClose,
  project,
  onPageSelect,
  onCreatePage
}: PagesSidebarProps) {
  const [pages, setPages] = useState<Page[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Load pages when project changes
  useEffect(() => {
    if (project && isOpen) {
      loadPages();
    }
  }, [project, isOpen]);

  const loadPages = async () => {
    if (!project) return;

    setIsLoading(true);
    setError('');

    try {
      const response = await getPageList(project.id);
      setPages(response.sessions);
    } catch (err) {
      console.error('Error loading pages:', err);
      setError('Failed to load pages');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePageClick = (page: Page) => {
    onPageSelect(page);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'editing':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  return (
    <>
      {/* Overlay - only show on mobile and only when sidebar is open */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed left-0 top-0 h-full w-80 bg-white border-r border-gray-200 z-50 transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>

        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <FiFile className="w-5 h-5 text-gray-500" />
            <div>
              <h3 className="font-medium text-gray-900">Pages</h3>
              {project && (
                <p className="text-xs text-gray-500 truncate">{project.title}</p>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors lg:hidden"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {/* Create New Page Button */}
          <div className="p-4 border-b border-gray-200">
            <button
              onClick={onCreatePage}
              className="w-full flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <FiPlus className="w-4 h-4" />
              <span>New Page</span>
            </button>
          </div>

          {/* Pages List */}
          <div className="p-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <FiLoader className="w-5 h-5 animate-spin text-gray-400" />
                <span className="ml-2 text-sm text-gray-500">Loading pages...</span>
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-sm text-red-600">{error}</p>
                <button
                  onClick={loadPages}
                  className="mt-2 text-sm text-blue-600 hover:text-blue-700"
                >
                  Try again
                </button>
              </div>
            ) : pages.length > 0 ? (
              <div className="space-y-2">
                {pages.map((page) => (
                  <button
                    key={page.id}
                    onClick={() => handlePageClick(page)}
                    className="w-full text-left p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {page.title || `Page ${page.id}`}
                        </h4>
                        {page.url && (
                          <p className="text-xs text-gray-500 truncate mt-1">
                            {page.url}
                          </p>
                        )}
                        <p className="text-xs text-gray-400 mt-1">
                          {formatDate(page.updated_at)}
                        </p>
                      </div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(page.status)}`}>
                        {page.status}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiFile className="w-8 h-8 text-gray-300 mx-auto mb-3" />
                <p className="text-sm text-gray-500">No pages yet</p>
                <p className="text-xs text-gray-400 mt-1">
                  Create your first page to get started
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
