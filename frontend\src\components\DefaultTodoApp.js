// Default Todo App HTML implementation
export const defaultTodoAppHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>My Todo List</title>
  <style>
    :root {
      /* Base colors */
      --background: 0 0% 100%;
      --foreground: 222.2 84% 4.9%;
      --card: 0 0% 100%;
      --card-foreground: 222.2 84% 4.9%;
      --popover: 0 0% 100%;
      --popover-foreground: 222.2 84% 4.9%;
      --primary: 221.2 83.2% 53.3%;
      --primary-foreground: 210 40% 98%;
      --secondary: 210 40% 96.1%;
      --secondary-foreground: 222.2 47.4% 11.2%;
      --muted: 210 40% 96.1%;
      --muted-foreground: 215.4 16.3% 46.9%;
      --accent: 210 40% 96.1%;
      --accent-foreground: 222.2 47.4% 11.2%;
      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 210 40% 98%;
      --border: 214.3 31.8% 91.4%;
      --input: 214.3 31.8% 91.4%;
      --ring: 221.2 83.2% 53.3%;

      /* Border radius */
      --radius-sm: 0.3rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.8rem;
      --radius-full: 9999px;

      /* Shadows */
      --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
      background-color: #f5f7fb;
      color: hsl(var(--foreground));
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 2rem;
    }

    .container {
      width: 100%;
      max-width: 600px;
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .card {
      background-color: hsl(var(--card));
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-md);
      padding: 2rem;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .card:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }

    .header {
      text-align: center;
      margin-bottom: 1.5rem;
    }

    .header h1 {
      font-size: 2rem;
      font-weight: 700;
      color: hsl(var(--foreground));
      margin-bottom: 0.5rem;
    }

    .header p {
      color: hsl(var(--muted-foreground));
      font-size: 1rem;
    }

    .input-container {
      display: flex;
      gap: 0.5rem;
      margin-bottom: 1.5rem;
    }

    .input-field {
      flex: 1;
      padding: 0.75rem 1rem;
      border: 1px solid hsl(var(--border));
      border-radius: var(--radius-md);
      font-size: 1rem;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
    }

    .input-field:focus {
      outline: none;
      border-color: hsl(var(--ring));
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }

    .add-button {
      background-color: hsl(var(--primary));
      color: hsl(var(--primary-foreground));
      border: none;
      border-radius: var(--radius-md);
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .add-button:hover {
      background-color: hsl(221.2 83.2% 47%);
    }

    .todo-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }

    .todo-item {
      display: flex;
      align-items: center;
      padding: 1rem;
      background-color: white;
      border-radius: var(--radius-md);
      box-shadow: var(--shadow-sm);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .todo-item:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }

    .todo-checkbox {
      appearance: none;
      width: 1.25rem;
      height: 1.25rem;
      border: 2px solid hsl(var(--primary));
      border-radius: var(--radius-sm);
      margin-right: 1rem;
      cursor: pointer;
      position: relative;
      transition: background-color 0.2s ease, border-color 0.2s ease;
    }

    .todo-checkbox:checked {
      background-color: hsl(var(--primary));
      border-color: hsl(var(--primary));
    }

    .todo-checkbox:checked::after {
      content: "✓";
      position: absolute;
      color: white;
      font-size: 0.75rem;
      font-weight: bold;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .todo-text {
      flex: 1;
      font-size: 1rem;
      transition: color 0.2s ease, text-decoration 0.2s ease;
    }

    .todo-item.completed .todo-text {
      text-decoration: line-through;
      color: hsl(var(--muted-foreground));
    }

    .delete-button {
      opacity: 0;
      background-color: transparent;
      border: none;
      color: hsl(var(--destructive));
      cursor: pointer;
      font-size: 1.25rem;
      transition: opacity 0.2s ease, color 0.2s ease;
    }

    .todo-item:hover .delete-button {
      opacity: 1;
    }

    .delete-button:hover {
      color: hsl(0 84.2% 50%);
    }

    .empty-state {
      text-align: center;
      padding: 2rem;
      color: hsl(var(--muted-foreground));
    }

    .empty-state p {
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="card">
      <div class="header">
        <h1>My Todo List</h1>
        <p>Stay organized, get things done</p>
      </div>
      
      <div class="input-container">
        <input type="text" class="input-field" id="new-task" placeholder="Add new task...">
        <button class="add-button" id="add-task">Add</button>
      </div>
      
      <div class="todo-list" id="todo-list">
        <!-- Todo items will be added here -->
        <div class="todo-item">
          <input type="checkbox" class="todo-checkbox">
          <span class="todo-text">Complete project proposal</span>
          <button class="delete-button">×</button>
        </div>
        
        <div class="todo-item completed">
          <input type="checkbox" class="todo-checkbox" checked>
          <span class="todo-text">Schedule team meeting</span>
          <button class="delete-button">×</button>
        </div>
        
        <div class="todo-item">
          <input type="checkbox" class="todo-checkbox">
          <span class="todo-text">Research new technologies</span>
          <button class="delete-button">×</button>
        </div>
        
        <div class="todo-item">
          <input type="checkbox" class="todo-checkbox">
          <span class="todo-text">Prepare presentation slides</span>
          <button class="delete-button">×</button>
        </div>
        
        <div class="todo-item completed">
          <input type="checkbox" class="todo-checkbox" checked>
          <span class="todo-text">Review client feedback</span>
          <button class="delete-button">×</button>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const newTaskInput = document.getElementById('new-task');
      const addTaskButton = document.getElementById('add-task');
      const todoList = document.getElementById('todo-list');
      
      // Add new task
      function addTask() {
        const taskText = newTaskInput.value.trim();
        if (taskText === '') return;
        
        const todoItem = document.createElement('div');
        todoItem.className = 'todo-item';
        
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'todo-checkbox';
        checkbox.addEventListener('change', toggleComplete);
        
        const text = document.createElement('span');
        text.className = 'todo-text';
        text.textContent = taskText;
        
        const deleteButton = document.createElement('button');
        deleteButton.className = 'delete-button';
        deleteButton.textContent = '×';
        deleteButton.addEventListener('click', deleteTask);
        
        todoItem.appendChild(checkbox);
        todoItem.appendChild(text);
        todoItem.appendChild(deleteButton);
        
        todoList.appendChild(todoItem);
        newTaskInput.value = '';
      }
      
      // Toggle complete status
      function toggleComplete() {
        const todoItem = this.parentElement;
        if (this.checked) {
          todoItem.classList.add('completed');
        } else {
          todoItem.classList.remove('completed');
        }
      }
      
      // Delete task
      function deleteTask() {
        const todoItem = this.parentElement;
        todoItem.style.opacity = '0';
        todoItem.style.transform = 'translateX(100px)';
        todoItem.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        
        setTimeout(() => {
          todoList.removeChild(todoItem);
        }, 300);
      }
      
      // Event listeners
      addTaskButton.addEventListener('click', addTask);
      newTaskInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          addTask();
        }
      });
      
      // Initialize existing tasks
      document.querySelectorAll('.todo-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', toggleComplete);
      });
      
      document.querySelectorAll('.delete-button').forEach(button => {
        button.addEventListener('click', deleteTask);
      });
    });
  </script>
</body>
</html>`;
