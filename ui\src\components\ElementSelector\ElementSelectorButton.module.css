.elementSelectorContainer {
  position: relative;
  display: inline-block;
}

/* Screen reader only class for accessibility */
:global(.sr-only) {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.selectorButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #555;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selectorButton:hover {
  background-color: #e0e0e0;
  color: #333;
}

.selectorButton.active {
  background-color: #2196f3;
  color: white;
  border-color: #1976d2;
}

.selectorButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.propertiesOverlay {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  z-index: 1000;
  width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  overflow: hidden;
}

/* Selection box styles */
.selectionBox {
  position: absolute;
  border: 1px dashed #2196f3;
  background-color: rgba(33, 150, 243, 0.1);
  pointer-events: none;
  z-index: 10;
}

/* Selected elements list */
.selectedElementsList {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.elementPill {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 16px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
}

.elementPill:hover {
  background-color: #e0e0e0;
}

.elementPill.active {
  background-color: #e3f2fd;
  border-color: #90caf9;
}

.removeButton {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #666;
  margin-left: 4px;
  cursor: pointer;
  padding: 2px;
}

.removeButton:hover {
  color: #f44336;
}

/* Element info display */
.elementInfo {
  margin-top: 8px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
}

.elementPath {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.tagName {
  color: #333;
  font-weight: 600;
}

.elementId {
  color: #2196f3;
}

.elementClass {
  color: #4caf50;
}

/* Action buttons */
.actionButtons {
  display: flex;
  gap: 4px;
  margin-top: 8px;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #555;
  cursor: pointer;
}

.actionButton:hover {
  background-color: #e0e0e0;
  color: #333;
}
