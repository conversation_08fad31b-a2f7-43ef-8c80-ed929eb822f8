const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

async function applyVersionSchema() {
  try {
    console.log('🔄 Applying prototype versions schema...');
    
    // Read the schema file
    const schemaPath = path.join(__dirname, 'db_prototype_versions_schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Execute the schema
    await pool.query(schema);
    
    console.log('✅ Prototype versions schema applied successfully!');
    
    // Test the functions
    console.log('🧪 Testing database functions...');
    
    // Test get_next_version_number function
    const testResult = await pool.query('SELECT get_next_version_number(1) as next_version');
    console.log('📊 Next version number for prototype 1:', testResult.rows[0].next_version);
    
    console.log('✅ All tests passed!');
    
  } catch (error) {
    console.error('❌ Error applying schema:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the script
if (require.main === module) {
  applyVersionSchema();
}

module.exports = applyVersionSchema;
