/**
 * Editor Comparison Page - Side-by-side testing of Original vs Refactored
 * Helps with thorough testing and comparison of both implementations
 */

import React, { useState } from 'react';
import { FiArrowRight, FiRefreshCw, FiCopy, FiCheck } from 'react-icons/fi';

export function EditorComparisonPage() {
  const [testPrompt, setTestPrompt] = useState('Create a modern SaaS landing page with hero section, features, and contact form');
  const [editPrompt, setEditPrompt] = useState('Change the hero background to a blue gradient and add a call-to-action button');
  const [copied, setCopied] = useState<string | null>(null);

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(type);
      setTimeout(() => setCopied(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const testScenarios = [
    {
      name: "Basic Content Generation",
      prompt: "Create a simple landing page with header, hero section, and footer",
      edits: [
        "Change the background color to blue",
        "Add a features section with 3 cards",
        "Make the header sticky"
      ]
    },
    {
      name: "E-commerce Site",
      prompt: "Create an e-commerce product page with product gallery, description, and buy button",
      edits: [
        "Add a product reviews section",
        "Change the buy button to green",
        "Add a related products section"
      ]
    },
    {
      name: "Portfolio Website",
      prompt: "Create a personal portfolio with about section, projects gallery, and contact form",
      edits: [
        "Add a skills section with progress bars",
        "Make the projects gallery a grid layout",
        "Add social media links to the footer"
      ]
    },
    {
      name: "Multi-page Navigation",
      prompt: "Create a business website with navigation to About, Services, and Contact pages",
      edits: [
        "Click on About link to create About page",
        "Click on Services link to create Services page",
        "Use Link All Pages to connect navigation"
      ]
    },
    {
      name: "Complex Layout Changes",
      prompt: "Create a blog homepage with article cards and sidebar",
      edits: [
        "Move the sidebar to the left side",
        "Change the article layout to a masonry grid",
        "Add a search bar to the header"
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <h1 className="text-3xl font-bold text-gray-900">EditorV3 Comparison Testing</h1>
          <p className="text-gray-600 mt-2">
            Side-by-side testing of Original vs Refactored implementations
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Quick Access Links */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Original EditorV3</h2>
            <p className="text-gray-600 mb-4">
              The current implementation (2,352 lines, monolithic)
            </p>
            <a
              href="/editor-v3"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Open Original Editor
              <FiArrowRight className="ml-2 w-4 h-4" />
            </a>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Refactored EditorV3</h2>
            <p className="text-gray-600 mb-4">
              The new modular implementation (1,800 lines across 6 files)
            </p>
            <a
              href="/editor-v3-refactored"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Open Refactored Editor
              <FiArrowRight className="ml-2 w-4 h-4" />
            </a>
          </div>
        </div>

        {/* Custom Test Input */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Custom Test Scenario</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Initial Generation Prompt
              </label>
              <textarea
                value={testPrompt}
                onChange={(e) => setTestPrompt(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="Enter your initial prompt..."
              />
              <button
                onClick={() => copyToClipboard(testPrompt, 'prompt')}
                className="mt-2 inline-flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
              >
                {copied === 'prompt' ? <FiCheck className="w-3 h-3 mr-1" /> : <FiCopy className="w-3 h-3 mr-1" />}
                {copied === 'prompt' ? 'Copied!' : 'Copy Prompt'}
              </button>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Edit Test Prompt
              </label>
              <textarea
                value={editPrompt}
                onChange={(e) => setEditPrompt(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={2}
                placeholder="Enter your edit prompt..."
              />
              <button
                onClick={() => copyToClipboard(editPrompt, 'edit')}
                className="mt-2 inline-flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
              >
                {copied === 'edit' ? <FiCheck className="w-3 h-3 mr-1" /> : <FiCopy className="w-3 h-3 mr-1" />}
                {copied === 'edit' ? 'Copied!' : 'Copy Edit'}
              </button>
            </div>
          </div>
        </div>

        {/* Predefined Test Scenarios */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Predefined Test Scenarios</h2>
          <p className="text-gray-600 mb-6">
            Click on any scenario to copy the prompts and test both implementations
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {testScenarios.map((scenario, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors">
                <h3 className="font-semibold text-gray-900 mb-2">{scenario.name}</h3>
                
                <div className="mb-3">
                  <p className="text-sm font-medium text-gray-700 mb-1">Initial Prompt:</p>
                  <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">{scenario.prompt}</p>
                  <button
                    onClick={() => copyToClipboard(scenario.prompt, `scenario-${index}-prompt`)}
                    className="mt-1 inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                  >
                    {copied === `scenario-${index}-prompt` ? <FiCheck className="w-3 h-3 mr-1" /> : <FiCopy className="w-3 h-3 mr-1" />}
                    {copied === `scenario-${index}-prompt` ? 'Copied!' : 'Copy'}
                  </button>
                </div>

                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Edit Tests:</p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {scenario.edits.map((edit, editIndex) => (
                      <li key={editIndex} className="flex items-start">
                        <span className="text-gray-400 mr-2">{editIndex + 1}.</span>
                        <span className="flex-1">{edit}</span>
                        <button
                          onClick={() => copyToClipboard(edit, `scenario-${index}-edit-${editIndex}`)}
                          className="ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                          title="Copy edit prompt"
                        >
                          {copied === `scenario-${index}-edit-${editIndex}` ? 
                            <FiCheck className="w-3 h-3" /> : 
                            <FiCopy className="w-3 h-3" />
                          }
                        </button>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Testing Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-8">
          <h2 className="text-lg font-semibold text-blue-900 mb-3">Testing Instructions</h2>
          <ol className="text-blue-800 space-y-2 list-decimal list-inside">
            <li>Open both editor versions in separate browser tabs</li>
            <li>Copy a test prompt from above and paste it into both editors</li>
            <li>Compare the initial generation quality and speed</li>
            <li>Copy edit prompts and test the edit functionality</li>
            <li>Compare the edit accuracy, speed, and user experience</li>
            <li>Test multi-page functionality by adding navigation and clicking links</li>
            <li>Note any differences in behavior, performance, or quality</li>
          </ol>
        </div>

        {/* Performance Comparison */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 mt-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Performance Comparison Checklist</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Original EditorV3</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span>Initial generation speed</span>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span>Edit operation speed</span>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span>Multi-page functionality</span>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span>Error handling</span>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span>User experience</span>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span>Code quality output</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Refactored EditorV3</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span>Initial generation speed</span>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span>Edit operation speed</span>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span>Multi-page functionality</span>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span>Error handling</span>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span>User experience</span>
                </div>
                <div className="flex items-center">
                  <input type="checkbox" className="mr-2" />
                  <span>Code quality output</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default EditorComparisonPage;
