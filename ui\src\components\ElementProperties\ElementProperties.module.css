.propertiesPanel {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  overflow: hidden;
  font-family: 'Inter', sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
}

.elementInfo {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.elementType {
  font-weight: 600;
  color: #333;
}

.elementId {
  color: #0047AB;
}

.elementClass {
  color: #4682B4;
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
}

.closeButton:hover {
  background-color: #f5f5f5;
  color: #333;
}

.propertiesContainer {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.propertyRow {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.propertyLabel {
  font-size: 13px;
  font-weight: 500;
  color: #555;
  text-transform: capitalize;
}

.textInput,
.numberInput,
.selectInput,
.colorText {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  width: 100%;
}

.textInput:focus,
.numberInput:focus,
.selectInput:focus,
.colorText:focus {
  outline: none;
  border-color: #0047AB;
  box-shadow: 0 0 0 2px rgba(0, 71, 171, 0.1);
}

.colorInputContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.colorInput {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;
}

.colorText {
  flex: 1;
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid #eee;
}

.saveButton,
.cancelButton {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
}

.saveButton {
  background-color: #0047AB;
  color: white;
  border: none;
}

.saveButton:hover {
  background-color: #003d91;
}

.cancelButton {
  background-color: transparent;
  color: #666;
  border: 1px solid #ddd;
}

.cancelButton:hover {
  background-color: #f5f5f5;
  color: #333;
}

/* Property group styles */
.propertyGroup {
  margin-bottom: 16px;
}

.propertyGroupTitle {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.propertyGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.advancedSection {
  margin-top: 16px;
}

.advancedHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 0;
}

.advancedTitle {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.advancedContent {
  margin-top: 8px;
}

.cssTextarea {
  width: 100%;
  min-height: 80px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 13px;
  resize: vertical;
}
