import { useState, useRef, useEffect } from 'react';
import {
  Paper,
  Box,
  Button,
  Tabs,
  Tab,
  ButtonGroup,
  Typography,
  Tooltip,
  TextField,
  Divider,
  Chip,
  CircularProgress,
  ToggleButton,
  ToggleButtonGroup,
  Snackbar,
  Alert,
  IconButton
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  Code as CodeIcon,
  Save as SaveIcon,
  Download as DownloadIcon,
  ContentCopy as ContentCopyIcon,
  Check as CheckIcon,
  Chat as ChatIcon,
  Send as SendIcon,
  TouchApp as TouchAppIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';

function PrototypePreview({ html, onSave, onExport, conversationMode = false, onFollowUpPrompt, isGenerating = false }) {
  const [activeTab, setActiveTab] = useState(0);
  const [copied, setCopied] = useState(false);
  const [followUpPrompt, setFollowUpPrompt] = useState('');
  const [selectionMode, setSelectionMode] = useState(false);
  const [selectedElement, setSelectedElement] = useState(null);
  const [elementInfo, setElementInfo] = useState(null);
  const [showSelectionToast, setShowSelectionToast] = useState(false);

  const iframeRef = useRef(null);

  if (!html) {
    return null;
  }

  // Effect to handle element selection
  useEffect(() => {
    if (!iframeRef.current || activeTab !== 0) return;

    // Since we can't directly access the iframe content due to sandbox restrictions,
    // we'll modify our approach to include the selection functionality in the HTML itself

    // Create a modified HTML that includes our selection script
    const createSelectionEnabledHtml = () => {
      // Selection styles
      const selectionStyles = `
        <style id="selection-styles">
          * {
            cursor: pointer !important;
          }
          .element-highlight {
            outline: 2px solid #A855F7 !important;
            outline-offset: 2px !important;
            position: relative;
          }
          .element-selected {
            outline: 3px solid #A855F7 !important;
            outline-offset: 3px !important;
            box-shadow: 0 0 0 5px rgba(168, 85, 247, 0.3) !important;
          }
        </style>
      `;

      // Selection script
      const selectionScript = `
        <script>
          // Function to handle element selection
          function setupElementSelection() {
            const allElements = document.querySelectorAll('body *');

            allElements.forEach(element => {
              // Skip script and style elements
              if (element.tagName === 'SCRIPT' || element.tagName === 'STYLE') return;

              // Hover effect
              element.addEventListener('mouseover', (e) => {
                e.stopPropagation();
                element.classList.add('element-highlight');
              });

              element.addEventListener('mouseout', (e) => {
                e.stopPropagation();
                element.classList.remove('element-highlight');
              });

              // Selection
              element.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                // Remove previous selection
                const previousSelected = document.querySelector('.element-selected');
                if (previousSelected) {
                  previousSelected.classList.remove('element-selected');
                }

                // Add new selection
                element.classList.add('element-selected');

                // Get element info
                const tagName = element.tagName.toLowerCase();
                const id = element.id ? \`#\${element.id}\` : '';
                const classes = Array.from(element.classList)
                  .filter(cls => cls !== 'element-highlight' && cls !== 'element-selected')
                  .map(cls => \`.\${cls}\`)
                  .join('');

                const text = element.textContent.trim().substring(0, 50) +
                  (element.textContent.trim().length > 50 ? '...' : '');

                // Send message to parent window
                window.parent.postMessage({
                  type: 'elementSelected',
                  data: {
                    selector: \`\${tagName}\${id}\${classes}\`,
                    tagName,
                    id: element.id || '',
                    classes: Array.from(element.classList)
                      .filter(cls => cls !== 'element-highlight' && cls !== 'element-selected'),
                    text,
                    html: element.outerHTML
                  }
                }, '*');
              });
            });
          }

          // Run setup when DOM is loaded
          document.addEventListener('DOMContentLoaded', setupElementSelection);

          // If DOM is already loaded, run setup immediately
          if (document.readyState === 'interactive' || document.readyState === 'complete') {
            setupElementSelection();
          }
        </script>
      `;

      // Insert the selection styles and script into the HTML
      let modifiedHtml = html;

      // Add selection styles to head
      if (modifiedHtml.includes('</head>')) {
        modifiedHtml = modifiedHtml.replace('</head>', `${selectionMode ? selectionStyles : ''}</head>`);
      } else if (modifiedHtml.includes('<html>')) {
        modifiedHtml = modifiedHtml.replace('<html>', `<html><head>${selectionMode ? selectionStyles : ''}</head>`);
      } else {
        modifiedHtml = `<html><head>${selectionMode ? selectionStyles : ''}</head>${modifiedHtml}</html>`;
      }

      // Add selection script to body
      if (modifiedHtml.includes('</body>')) {
        modifiedHtml = modifiedHtml.replace('</body>', `${selectionMode ? selectionScript : ''}</body>`);
      } else if (modifiedHtml.includes('</html>')) {
        modifiedHtml = modifiedHtml.replace('</html>', `<body>${selectionMode ? selectionScript : ''}</body></html>`);
      } else {
        modifiedHtml = `${modifiedHtml}<body>${selectionMode ? selectionScript : ''}</body>`;
      }

      return modifiedHtml;
    };

    // Update the iframe src with the modified HTML
    if (iframeRef.current) {
      iframeRef.current.srcdoc = createSelectionEnabledHtml();
    }

    // Set up message listener for element selection
    const handleMessage = (event) => {
      if (event.data && event.data.type === 'elementSelected') {
        setSelectedElement({});  // We can't store the actual DOM element, but we don't need it
        setElementInfo(event.data.data);
        setShowSelectionToast(true);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [selectionMode, activeTab, html]);

  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);

    // Disable selection mode when switching to code tab
    if (newValue === 1 && selectionMode) {
      setSelectionMode(false);
    }
  };

  const handleCopyCode = () => {
    navigator.clipboard.writeText(html);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const toggleSelectionMode = () => {
    setSelectionMode(!selectionMode);
    if (selectionMode) {
      // Clear selection when turning off selection mode
      setSelectedElement(null);
      setElementInfo(null);
    }
  };

  const handleFollowUpSubmit = (e) => {
    e.preventDefault();
    if (!followUpPrompt.trim()) return;

    let enhancedPrompt = followUpPrompt;

    // If an element is selected, include its information in the prompt
    if (selectedElement && elementInfo) {
      enhancedPrompt = `Please modify the following element: ${elementInfo.selector}\n\n` +
        `Element type: ${elementInfo.tagName}\n` +
        `Element content: ${elementInfo.text}\n\n` +
        `User request: ${followUpPrompt}`;
    }

    onFollowUpPrompt(enhancedPrompt);
    setFollowUpPrompt('');
  };

  const handleClearSelection = () => {
    setSelectedElement(null);
    setElementInfo(null);

    // We need to refresh the iframe to clear the selection
    // since we can't directly access its content due to sandbox restrictions
    if (iframeRef.current) {
      iframeRef.current.srcdoc = iframeRef.current.srcdoc;
    }
  };

  return (
    <Paper
      elevation={0}
      sx={{
        border: '1px solid',
        borderColor: 'divider',
        borderRadius: 1,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        height: '100%'
      }}
    >
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        p: 0.75,
        borderBottom: '1px solid',
        borderColor: 'divider',
        backgroundColor: 'rgba(55, 48, 163, 0.95)'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              minHeight: 'auto',
              '& .MuiTabs-indicator': {
                height: 2,
                borderRadius: '2px 2px 0 0'
              },
              '& .MuiTab-root': {
                minHeight: 'auto',
                py: 0.75,
                px: 1.5,
                fontSize: '0.8rem',
                color: 'rgba(255, 255, 255, 0.7)',
                '&.Mui-selected': {
                  color: 'white'
                }
              }
            }}
          >
            <Tab
              icon={<VisibilityIcon fontSize="small" />}
              iconPosition="start"
              label="Preview"
              sx={{ fontWeight: 600 }}
            />
            <Tab
              icon={<CodeIcon fontSize="small" />}
              iconPosition="start"
              label="HTML Code"
              sx={{ fontWeight: 600 }}
            />
          </Tabs>

          {/* Element selection toggle button - only show in preview mode */}
          {activeTab === 0 && (
            <Tooltip title={selectionMode ? "Exit Selection Mode" : "Select Elements"}>
              <IconButton
                size="small"
                color={selectionMode ? "primary" : "default"}
                onClick={toggleSelectionMode}
                sx={{
                  ml: 1,
                  color: selectionMode ? 'primary.main' : 'rgba(255, 255, 255, 0.7)',
                  '&:hover': {
                    color: 'white'
                  }
                }}
              >
                <TouchAppIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}

          {/* Selected element info */}
          {selectionMode && elementInfo && (
            <Box sx={{
              ml: 2,
              display: 'flex',
              alignItems: 'center',
              backgroundColor: 'rgba(168, 85, 247, 0.2)',
              borderRadius: 1,
              py: 0.25,
              px: 1,
              color: 'white'
            }}>
              <Typography variant="body2" fontWeight={600} sx={{ mr: 1, fontSize: '0.75rem' }}>
                Selected: <code style={{ color: '#A855F7' }}>{elementInfo.selector}</code>
              </Typography>
              <Tooltip title="Clear Selection">
                <IconButton
                  size="small"
                  onClick={handleClearSelection}
                  sx={{
                    p: 0.25,
                    color: 'rgba(255, 255, 255, 0.7)',
                    '&:hover': {
                      color: 'white'
                    }
                  }}
                >
                  <CancelIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          )}
        </Box>

        <Box sx={{ display: 'flex', gap: 0.5 }}>
          {activeTab === 1 && (
            <Tooltip title={copied ? "Copied!" : "Copy HTML"}>
              <IconButton
                size="small"
                color={copied ? "success" : "default"}
                onClick={handleCopyCode}
                sx={{
                  color: copied ? 'success.main' : 'rgba(255, 255, 255, 0.7)',
                  '&:hover': {
                    color: 'white'
                  }
                }}
              >
                {copied ? <CheckIcon fontSize="small" /> : <ContentCopyIcon fontSize="small" />}
              </IconButton>
            </Tooltip>
          )}
          <Tooltip title="Save Prototype">
            <IconButton
              size="small"
              onClick={onSave}
              sx={{
                color: 'rgba(255, 255, 255, 0.7)',
                '&:hover': {
                  color: 'white'
                }
              }}
            >
              <SaveIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Export HTML">
            <IconButton
              size="small"
              onClick={onExport}
              sx={{
                color: 'rgba(255, 255, 255, 0.7)',
                '&:hover': {
                  color: 'white'
                }
              }}
            >
              <DownloadIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Box sx={{
        display: activeTab === 0 ? 'flex' : 'none',
        flexGrow: 1,
        overflow: 'hidden'
      }}>
        <Box sx={{
          width: '100%',
          height: '100%',
          overflow: 'hidden',
          backgroundColor: '#0F172A'
        }}>
          <iframe
            ref={iframeRef}
            title="Prototype Preview"
            style={{ width: '100%', height: '100%', border: 'none' }}
            sandbox="allow-scripts"
          />
        </Box>
      </Box>

      <Box sx={{
        display: activeTab === 1 ? 'flex' : 'none',
        flexGrow: 1,
        overflow: 'hidden'
      }}>
        <Box sx={{
          width: '100%',
          height: '100%',
          overflow: 'auto',
          backgroundColor: '#1F2937',
          p: 3,
          fontFamily: '"Fira Code", "Roboto Mono", monospace'
        }}>
          <Typography
            component="pre"
            sx={{
              margin: 0,
              whiteSpace: 'pre-wrap',
              fontSize: '0.875rem',
              color: '#F9FAFB',
              fontFamily: 'inherit',
              lineHeight: 1.5
            }}
          >
            {html}
          </Typography>
        </Box>
      </Box>

      {/* Follow-up prompt section */}
      {conversationMode && onFollowUpPrompt && (
        <Box sx={{
          p: 1.5,
          borderTop: '1px solid',
          borderColor: 'divider',
          backgroundColor: 'rgba(55, 48, 163, 0.95)'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <ChatIcon color="primary" fontSize="small" sx={{ mr: 1 }} />
            <Typography variant="subtitle2" fontWeight={600} sx={{ color: 'white' }}>
              Conversation Mode
            </Typography>
            <Chip
              size="small"
              label="Active"
              color="primary"
              variant="outlined"
              sx={{ ml: 1, height: 20, fontSize: '0.7rem' }}
            />
          </Box>

          {/* Selected element info in the prompt section */}
          {elementInfo && (
            <Box sx={{
              mb: 1.5,
              p: 1.5,
              borderRadius: 1,
              backgroundColor: 'rgba(168, 85, 247, 0.15)',
              border: '1px solid rgba(168, 85, 247, 0.2)'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="subtitle2" color="primary.main" fontWeight={600}>
                  Selected Element
                </Typography>
                <Tooltip title="Clear Selection">
                  <IconButton
                    size="small"
                    onClick={handleClearSelection}
                    sx={{ p: 0.25 }}
                  >
                    <CancelIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>

              <Box sx={{ mb: 1 }}>
                <Typography variant="caption" color="text.secondary" component="div">
                  Selector:
                </Typography>
                <Typography variant="body2" component="code" sx={{
                  display: 'block',
                  p: 0.5,
                  backgroundColor: 'rgba(0,0,0,0.15)',
                  borderRadius: 0.5,
                  fontFamily: 'monospace',
                  fontSize: '0.85rem',
                  color: '#A855F7'
                }}>
                  {elementInfo.selector}
                </Typography>
              </Box>

              <Box>
                <Typography variant="caption" color="text.secondary" component="div">
                  Content:
                </Typography>
                <Typography variant="body2" sx={{
                  fontStyle: 'italic',
                  color: 'rgba(255, 255, 255, 0.7)'
                }}>
                  {elementInfo.text}
                </Typography>
              </Box>
            </Box>
          )}

          <form onSubmit={handleFollowUpSubmit} style={{ display: 'flex', gap: '8px' }}>
            <TextField
              placeholder={elementInfo
                ? `Describe how to modify the selected ${elementInfo.tagName} element...`
                : "Enter a follow-up prompt to refine your prototype..."}
              value={followUpPrompt}
              onChange={(e) => setFollowUpPrompt(e.target.value)}
              fullWidth
              size="small"
              variant="outlined"
              disabled={isGenerating}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1,
                  backgroundColor: 'rgba(30, 30, 60, 0.6)',
                  '& fieldset': {
                    borderColor: 'rgba(168, 85, 247, 0.3)',
                  },
                  '&:hover fieldset': {
                    borderColor: 'rgba(168, 85, 247, 0.5)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: 'rgba(168, 85, 247, 0.8)',
                  },
                },
                '& .MuiInputBase-input': {
                  color: 'rgba(255, 255, 255, 0.9)',
                }
              }}
            />
            <Button
              type="submit"
              variant="contained"
              color="primary"
              disabled={isGenerating || !followUpPrompt.trim()}
              endIcon={isGenerating ? <CircularProgress size={16} color="inherit" /> : <SendIcon />}
              sx={{
                borderRadius: 1,
                minWidth: '80px',
                backgroundColor: '#8B5CF6',
                '&:hover': {
                  backgroundColor: '#7C3AED',
                }
              }}
            >
              {isGenerating ? 'Sending...' : 'Send'}
            </Button>
          </form>
        </Box>
      )}

      {/* Selection toast notification */}
      <Snackbar
        open={showSelectionToast}
        autoHideDuration={3000}
        onClose={() => setShowSelectionToast(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          severity="success"
          variant="filled"
          onClose={() => setShowSelectionToast(false)}
          sx={{ width: '100%' }}
        >
          Element selected! Now you can provide specific instructions to modify it.
        </Alert>
      </Snackbar>
    </Paper>
  );
}

export default PrototypePreview;
