const { jest } = require('@jest/globals');
const { v4: uuidv4 } = require('uuid');

// Mock the schemaValidator
jest.mock('../../services/ast/schemaValidator');
const schemaValidator = require('../../services/ast/schemaValidator');

// Import the ASTStore
const { ASTStore } = require('../../services/ast/astStore');

// Setup default mock implementations
beforeEach(() => {
  jest.clearAllMocks();
  
  // Reset schema validator mocks
  schemaValidator.validateTree.mockReturnValue({ valid: true, errors: [] });
  schemaValidator.validatePatch.mockReturnValue({ valid: true, errors: [] });
});

describe('ASTStore', () => {
  let store;
  
  beforeEach(() => {
    // Create a new instance before each test
    store = new ASTStore();
    // Reset all mocks
    jest.clearAllMocks();
  });
  
  describe('createEmptyPage', () => {
    it('should create a valid empty page', () => {
      const page = store.createEmptyPage();
      
      expect(page).toHaveProperty('id');
      expect(page.type).toBe('Page');
      expect(page.props).toEqual({
        title: 'Untitled Page',
        meta: {},
        styles: {}
      });
      expect(Array.isArray(page.children)).toBe(true);
      expect(page.children.length).toBe(0);
    });
  });
  
  describe('getCurrentAST', () => {
    it('should return a deep copy of the current AST', () => {
      const originalAST = store.getCurrentAST();
      const copyAST = store.getCurrentAST();
      
      // Should be deep equal but not the same reference
      expect(copyAST).toEqual(originalAST);
      expect(copyAST).not.toBe(originalAST);
    });
  });
  
  describe('setCurrentAST', () => {
    it('should update the current AST and save a snapshot', () => {
      const newAST = {
        id: 'test-id',
        type: 'Page',
        props: { title: 'Test Page' },
        children: []
      };
      
      // Mock validation
      schemaValidator.validateTree.mockReturnValueOnce({ valid: true, errors: [] });
      
      store.setCurrentAST(newAST);
      
      // Verify the AST was updated
      const currentAST = store.getCurrentAST();
      expect(currentAST).toEqual(newAST);
      
      // Verify validation was called
      expect(schemaValidator.validateTree).toHaveBeenCalledWith(newAST);
    });
    
    it('should throw an error for invalid AST', () => {
      const invalidAST = { type: 'Invalid' };
      
      // Mock validation to fail
      const validationError = new Error('Invalid AST structure');
      schemaValidator.validateTree.mockReturnValueOnce({ 
        valid: false, 
        errors: ['Invalid AST structure'] 
      });
      
      // Should throw an error
      expect(() => {
        store.setCurrentAST(invalidAST);
      }).toThrow('Invalid AST: Invalid AST structure');
    });
  });
  
  describe('saveSnapshot', () => {
    it('should save the current state as a snapshot', () => {
      const snapshotName = 'Test Snapshot';
      const snapshotId = store.saveSnapshot(snapshotName);
      
      // Should return a snapshot ID
      expect(snapshotId).toBeDefined();
      
      // Should be able to get the snapshot
      const snapshot = store.getSnapshot(snapshotId);
      expect(snapshot).toBeDefined();
      expect(snapshot.name).toBe(snapshotName);
    });
  });
  
  describe('getSnapshot', () => {
    it('should return a snapshot by ID', () => {
      const snapshotName = 'Test Get Snapshot';
      const snapshotId = store.saveSnapshot(snapshotName);
      
      const snapshot = store.getSnapshot(snapshotId);
      
      expect(snapshot).toBeDefined();
      expect(snapshot.id).toBe(snapshotId);
      expect(snapshot.name).toBe(snapshotName);
      expect(snapshot.ast).toBeDefined();
    });
    
    it('should return null for non-existent snapshot', () => {
      const snapshot = store.getSnapshot('non-existent-id');
      expect(snapshot).toBeNull();
    });
  });
  
  describe('listSnapshots', () => {
    it('should return a list of all snapshots', () => {
      // Clear initial snapshots
      store = new ASTStore();
      
      // Save test snapshots
      const snapshot1 = store.saveSnapshot('Snapshot 1');
      const snapshot2 = store.saveSnapshot('Snapshot 2');
      
      const snapshots = store.listSnapshots();
      
      expect(Array.isArray(snapshots)).toBe(true);
      expect(snapshots.length).toBe(3); // 2 we just created + initial snapshot
      
      // Check that the snapshots have the expected properties
      snapshots.forEach(snapshot => {
        expect(snapshot).toHaveProperty('id');
        expect(snapshot).toHaveProperty('name');
        expect(snapshot).toHaveProperty('timestamp');
        expect(snapshot).toHaveProperty('ast');
      });
    });
  });
  
  describe('applyPatch', () => {
    it('should apply a patch to a snapshot', () => {
      // Create a test snapshot
      const snapshotId = store.saveSnapshot('Test Patch');
      const originalAST = store.getSnapshot(snapshotId).ast;
      
      // Define a patch operation
      const patch = [
        {
          op: 'add',
          path: '/children',
          value: [
            { type: 'div', id: 'test-div' }
          ]
        }
      ];
      
      // Mock validation
      schemaValidator.validatePatch.mockReturnValueOnce({ valid: true, errors: [] });
      schemaValidator.validateTree.mockReturnValueOnce({ valid: true, errors: [] });
      
      // Apply the patch
      const result = store.applyPatch(snapshotId, patch);
      
      // Verify the result
      expect(result.success).toBe(true);
      expect(result.snapshotId).not.toBe(snapshotId); // Should create a new snapshot
      
      // Get the patched AST
      const patchedAST = store.getSnapshot(result.snapshotId).ast;
      
      // Verify the patch was applied
      expect(patchedAST.children).toHaveLength(1);
      expect(patchedAST.children[0].type).toBe('div');
      expect(patchedAST.children[0].id).toBe('test-div');
      
      // Original snapshot should remain unchanged
      const originalSnapshot = store.getSnapshot(snapshotId);
      expect(originalSnapshot.ast.children).toBeUndefined();
    });
    
    it('should validate the patch before applying', () => {
      const snapshotId = store.saveSnapshot('Test Validation');
      const invalidPatch = [
        { op: 'invalid', path: '/invalid', value: 'test' }
      ];
      
      // Mock validation to fail
      schemaValidator.validatePatch.mockReturnValueOnce({ 
        valid: false, 
        errors: ['Invalid operation'] 
      });
      
      // Should throw an error
      expect(() => {
        store.applyPatch(snapshotId, invalidPatch);
      }).toThrow('Invalid patch: Invalid operation');
    });
    
    it('should validate the resulting AST after applying the patch', () => {
      const snapshotId = store.saveSnapshot('Test Result Validation');
      const patch = [
        {
          op: 'add',
          path: '/invalidProp',
          value: 'invalid value'
        }
      ];
      
      // Mock validation to pass the patch but fail the resulting AST
      schemaValidator.validatePatch.mockReturnValueOnce({ valid: true, errors: [] });
      schemaValidator.validateTree.mockReturnValueOnce({ 
        valid: false, 
        errors: ['Invalid AST structure'] 
      });
      
      // Should throw an error
      expect(() => {
        store.applyPatch(snapshotId, patch);
      }).toThrow('Invalid AST after applying patch: Invalid AST structure');
    });
  });
  
  describe('findNodeById', () => {
    it('should find a node by ID in the AST', () => {
      // Create a test AST with nested nodes
      const testAST = {
        id: 'root',
        type: 'div',
        children: [
          {
            id: 'child1',
            type: 'span',
            children: [
              { id: 'grandchild1', type: 'text', content: 'Hello' }
            ]
          },
          { id: 'child2', type: 'p', content: 'World' }
        ]
      };
      
      // Set the test AST
      schemaValidator.validateTree.mockReturnValueOnce({ valid: true, errors: [] });
      store.setCurrentAST(testAST);
      
      // Test finding different nodes
      const rootNode = store.findNodeById('root');
      expect(rootNode).toBeDefined();
      expect(rootNode.id).toBe('root');
      
      const child1 = store.findNodeById('child1');
      expect(child1).toBeDefined();
      expect(child1.id).toBe('child1');
      
      const grandchild1 = store.findNodeById('grandchild1');
      expect(grandchild1).toBeDefined();
      expect(grandchild1.id).toBe('grandchild1');
      
      const child2 = store.findNodeById('child2');
      expect(child2).toBeDefined();
      expect(child2.id).toBe('child2');
      
      // Test non-existent node
      const nonExistent = store.findNodeById('non-existent');
      expect(nonExistent).toBeUndefined();
    });
  });
});
