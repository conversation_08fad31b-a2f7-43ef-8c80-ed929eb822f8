import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  TextField
} from '@mui/material';
import {
  Check as CheckIcon,
  Edit as EditIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';
import { llmService } from '../services/llmService';

function PlanningScreen({ prompt, onGenerateImplementation }) {
  const [plan, setPlan] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [editedPlan, setEditedPlan] = useState('');
  const [planSections, setPlanSections] = useState([]);

  // Generate the plan when component mounts
  useEffect(() => {
    // Start with empty plan to show streaming effect
    setPlan('# Generating Design Plan...');
    setIsLoading(false); // Set to false to show content immediately

    // Start the streaming simulation after a short delay
    setTimeout(() => {
      generatePlan();
    }, 300);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Parse plan into sections for better display
  useEffect(() => {
    if (plan) {
      const sections = parsePlanIntoSections(plan);
      setPlanSections(sections);
    }
  }, [plan]);

  const parsePlanIntoSections = (planText) => {
    // Split by headers (lines starting with #)
    const lines = planText.split('\n');
    const sections = [];
    let currentSection = { title: 'Overview', content: [] };

    for (const line of lines) {
      if (line.startsWith('##') || line.startsWith('# ')) {
        // If we have content in the current section, save it
        if (currentSection.content.length > 0) {
          sections.push({...currentSection});
        }
        // Start a new section
        currentSection = {
          title: line.replace(/^#+\s+/, ''),
          content: []
        };
      } else if (line.trim()) {
        // Add non-empty lines to the current section
        currentSection.content.push(line);
      }
    }

    // Add the last section
    if (currentSection.content.length > 0) {
      sections.push(currentSection);
    }

    return sections;
  };

  const generatePlan = async () => {
    // Create a simulated streaming effect for better UX
    const simulateStreamingPlan = () => {
      // Initial placeholder sections
      const initialPlan = `# Design Plan for Todo App

## Overall Layout Structure
Analyzing requirements...

## Component Breakdown
Waiting...

## Key Design Elements
Waiting...

## Interaction Patterns
Waiting...

## Visual Styling Approach
Waiting...`;

      setPlan(initialPlan);

      // Simulate streaming updates for each section
      setTimeout(() => {
        setPlan(prev => prev.replace('## Overall Layout Structure\nAnalyzing requirements...',
          `## Overall Layout Structure
- Main container with centered content
- Header section with title and subtitle
- Input section with text field and add button
- Task list container with individual task items
- Clean, card-based design with proper spacing`));
      }, 800);

      setTimeout(() => {
        setPlan(prev => prev.replace('## Component Breakdown\nWaiting...',
          `## Component Breakdown
- Header Component: App title and description
- Input Component: Text field and add button
- Task Item Component: Checkbox, task text, and delete button
- Task List Component: Container for all task items
- Empty State Component: Displayed when no tasks exist`));
      }, 1600);

      setTimeout(() => {
        setPlan(prev => prev.replace('## Key Design Elements\nWaiting...',
          `## Key Design Elements
- Clean white cards with subtle shadows for depth
- Consistent spacing using 8px grid system
- Checkbox with custom styling for completed tasks
- Subtle hover effects on interactive elements
- Visual feedback for completed tasks (strikethrough, reduced opacity)
- Delete buttons that appear on hover`));
      }, 2400);

      setTimeout(() => {
        setPlan(prev => prev.replace('## Interaction Patterns\nWaiting...',
          `## Interaction Patterns
- Add task: Enter text and click add button or press Enter
- Complete task: Click checkbox to toggle completion status
- Delete task: Click delete icon to remove task
- Edit task: Click on task text to edit (optional feature)
- Clear completed: Option to remove all completed tasks
- Smooth animations for adding/removing tasks`));
      }, 3200);

      setTimeout(() => {
        setPlan(prev => prev.replace('## Visual Styling Approach\nWaiting...',
          `## Visual Styling Approach
- Modern, clean aesthetic with ample white space
- Primary color for interactive elements and accents
- Subtle shadows for depth and hierarchy
- Rounded corners on cards and interactive elements
- Consistent typography with clear hierarchy
- Subtle animations for state changes and transitions
- Responsive design that works well on all screen sizes`));

        // Once all sections are complete, fetch the real plan
        fetchActualPlan();
      }, 4000);
    };

    // Start the streaming simulation
    simulateStreamingPlan();

    // Function to fetch the actual plan from the LLM
    const fetchActualPlan = async () => {
      try {
        // Create a planning-specific prompt
        const planningPrompt = `I need a detailed plan for creating: ${prompt}

Please provide a comprehensive design plan with the following sections:
1. Overall Layout Structure
2. Component Breakdown
3. Key Design Elements
4. Interaction Patterns
5. Visual Styling Approach

Focus ONLY on the planning phase. DO NOT generate any HTML or CSS code yet.
Return your response in markdown format with clear section headers.`;

        // Call the LLM service to generate the plan
        const result = await llmService.generateHTML(planningPrompt);

        // Extract the plan text
        let planText = '';
        if (result.type === 'plan_and_implementation') {
          planText = result.designPlan;
        } else if (typeof result === 'string') {
          planText = result;
        } else if (result.html) {
          // If we got HTML instead of a plan, extract text content
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = result.html;
          planText = tempDiv.textContent;
        }

        // Replace the simulated plan with the real one
        setPlan(planText);
        setEditedPlan(planText);
      } catch (error) {
        console.error('Error generating plan:', error);
        // Keep the simulated plan if there's an error
      } finally {
        setIsLoading(false);
      }
    };
  };

  const handleEditToggle = () => {
    if (isEditing) {
      // Save the edited plan
      setPlan(editedPlan);
    } else {
      // Enter edit mode
      setEditedPlan(plan);
    }
    setIsEditing(!isEditing);
  };

  const handlePlanChange = (e) => {
    setEditedPlan(e.target.value);
  };

  const handleGenerateImplementation = () => {
    setIsGenerating(true);

    // Show a progress message
    setPlan(prevPlan => prevPlan + '\n\n## Implementation Progress\n- Starting HTML generation...');

    // Add progress updates
    const progressInterval = setInterval(() => {
      setPlan(prevPlan => {
        // Check if we already have progress section
        if (prevPlan.includes('## Implementation Progress')) {
          const progressSteps = [
            '- Analyzing design requirements...',
            '- Setting up HTML structure...',
            '- Creating CSS styles...',
            '- Implementing JavaScript functionality...',
            '- Optimizing for responsiveness...',
            '- Adding final touches...'
          ];

          // Count how many steps we've already added
          const currentStepCount = (prevPlan.match(/- /g) || []).length - planSections.length;

          // If we have more steps to add and haven't reached the end
          if (currentStepCount < progressSteps.length) {
            return prevPlan + '\n' + progressSteps[currentStepCount];
          }

          return prevPlan;
        }
        return prevPlan;
      });
    }, 800);

    // Call the implementation function after a short delay
    setTimeout(() => {
      clearInterval(progressInterval);
      onGenerateImplementation(plan);
    }, 2000);
  };

  return (
    <Box sx={{ maxWidth: '1200px', mx: 'auto', p: 3, height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight={700} sx={{ mb: 1 }}>
          Design Planning
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Review the design plan below before generating the implementation.
        </Typography>
      </Box>

      <Paper
        elevation={2}
        sx={{
          p: 3,
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
          borderRadius: 2
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" fontWeight={600}>
            Design Plan for: {prompt}
          </Typography>
          <Box>
            <Button
              startIcon={isEditing ? <CheckIcon /> : <EditIcon />}
              onClick={handleEditToggle}
              variant={isEditing ? "contained" : "outlined"}
              color="primary"
              sx={{ mr: 1 }}
            >
              {isEditing ? 'Save Changes' : 'Edit Plan'}
            </Button>
            <Button
              variant="contained"
              color="primary"
              endIcon={isGenerating ? <CircularProgress size={16} color="inherit" /> : <ArrowForwardIcon />}
              onClick={handleGenerateImplementation}
              disabled={isLoading || isGenerating}
            >
              {isGenerating ? 'Generating...' : 'Generate Implementation'}
            </Button>
          </Box>
        </Box>

        {/* Divider removed */}

        <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
          {isEditing ? (
            <TextField
              multiline
              fullWidth
              value={editedPlan}
              onChange={handlePlanChange}
              variant="outlined"
              sx={{
                height: '100%',
                '& .MuiInputBase-root': {
                  height: '100%',
                  alignItems: 'flex-start'
                },
                '& .MuiInputBase-input': {
                  height: '100%',
                  overflow: 'auto'
                }
              }}
            />
          ) : (
            <Box sx={{ px: 2 }}>
              {planSections.map((section, index) => (
                <Box key={index} sx={{ mb: 3 }}>
                  <Typography variant="h6" fontWeight={600} sx={{ mb: 1, color: '#4F46E5' }}>
                    {section.title}
                  </Typography>
                  <Box sx={{ pl: 2 }}>
                    {section.content.map((line, lineIndex) => (
                      <Typography
                        key={lineIndex}
                        variant="body1"
                        sx={{
                          mb: 1,
                          whiteSpace: 'pre-wrap',
                          fontWeight: line.trim().startsWith('-') ? 400 : line.trim().startsWith('*') ? 400 : 'inherit'
                        }}
                      >
                        {line}
                      </Typography>
                    ))}
                  </Box>
                </Box>
              ))}
            </Box>
          )}
        </Box>
      </Paper>
    </Box>
  );
}

export default PlanningScreen;
