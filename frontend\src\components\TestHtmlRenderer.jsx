import React, { useEffect, useRef } from 'react';
import { Box, Paper, Typography, CircularProgress } from '@mui/material';

function TestHtmlRenderer() {
  const iframeRef = useRef(null);
  const [isLoading, setIsLoading] = React.useState(true);

  // Hardcoded HTML for testing
  const hardcodedHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo App</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f9fafb;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            min-height: 100vh;
            color: #1f2937;
        }
        
        .container {
            width: 100%;
            max-width: 500px;
            margin: 40px 20px;
        }
        
        .app-header {
            margin-bottom: 24px;
            text-align: center;
        }
        
        .app-title {
            font-size: 28px;
            font-weight: 700;
            color: #4f46e5;
            margin-bottom: 8px;
        }
        
        .app-subtitle {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 24px;
        }
        
        .todo-card {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            padding: 24px;
            margin-bottom: 24px;
        }
        
        .input-group {
            display: flex;
            margin-bottom: 24px;
        }
        
        .todo-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px 0 0 8px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.2s;
        }
        
        .todo-input:focus {
            border-color: #4f46e5;
        }
        
        .add-button {
            background-color: #4f46e5;
            color: white;
            border: none;
            border-radius: 0 8px 8px 0;
            padding: 0 20px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .add-button:hover {
            background-color: #4338ca;
        }
        
        .todo-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }
        
        .todo-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .todo-item:last-child {
            border-bottom: none;
        }
        
        .todo-checkbox {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 2px solid #d1d5db;
            margin-right: 16px;
            cursor: pointer;
            position: relative;
        }
        
        .todo-checkbox.checked {
            background-color: #4f46e5;
            border-color: #4f46e5;
        }
        
        .todo-checkbox.checked::after {
            content: '';
            position: absolute;
            top: 3px;
            left: 7px;
            width: 4px;
            height: 8px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        
        .todo-text {
            flex: 1;
            font-size: 16px;
        }
        
        .todo-text.completed {
            text-decoration: line-through;
            color: #9ca3af;
        }
        
        .delete-button {
            background-color: transparent;
            color: #ef4444;
            border: none;
            cursor: pointer;
            font-size: 14px;
            padding: 4px 8px;
            border-radius: 4px;
            opacity: 0.8;
            transition: opacity 0.2s;
        }
        
        .delete-button:hover {
            opacity: 1;
            background-color: #fee2e2;
        }
        
        .empty-state {
            text-align: center;
            padding: 32px 0;
            color: #9ca3af;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #d1d5db;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="app-header">
            <h1 class="app-title">Ultra-Premium Todo App</h1>
            <p class="app-subtitle">Organize your tasks with style</p>
        </header>
        
        <div class="todo-card">
            <div class="input-group">
                <input type="text" class="todo-input" placeholder="Add a new task...">
                <button class="add-button">Add</button>
            </div>
            
            <ul class="todo-list">
                <li class="todo-item">
                    <div class="todo-checkbox checked"></div>
                    <span class="todo-text completed">Complete project proposal</span>
                    <button class="delete-button">Delete</button>
                </li>
                <li class="todo-item">
                    <div class="todo-checkbox"></div>
                    <span class="todo-text">Schedule team meeting</span>
                    <button class="delete-button">Delete</button>
                </li>
                <li class="todo-item">
                    <div class="todo-checkbox"></div>
                    <span class="todo-text">Research new technologies</span>
                    <button class="delete-button">Delete</button>
                </li>
            </ul>
        </div>
    </div>

    <script>
        // Add event listeners for the todo app functionality
        document.addEventListener('DOMContentLoaded', function() {
            const todoInput = document.querySelector('.todo-input');
            const addButton = document.querySelector('.add-button');
            const todoList = document.querySelector('.todo-list');
            
            // Add new todo
            addButton.addEventListener('click', function() {
                const todoText = todoInput.value.trim();
                if (todoText) {
                    addTodoItem(todoText);
                    todoInput.value = '';
                }
            });
            
            // Add todo when Enter key is pressed
            todoInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const todoText = todoInput.value.trim();
                    if (todoText) {
                        addTodoItem(todoText);
                        todoInput.value = '';
                    }
                }
            });
            
            // Function to add a new todo item
            function addTodoItem(text) {
                const li = document.createElement('li');
                li.className = 'todo-item';
                
                const checkbox = document.createElement('div');
                checkbox.className = 'todo-checkbox';
                
                const span = document.createElement('span');
                span.className = 'todo-text';
                span.textContent = text;
                
                const deleteButton = document.createElement('button');
                deleteButton.className = 'delete-button';
                deleteButton.textContent = 'Delete';
                
                li.appendChild(checkbox);
                li.appendChild(span);
                li.appendChild(deleteButton);
                
                todoList.appendChild(li);
                
                // Add event listeners for the new todo item
                setupTodoItemListeners(li);
            }
            
            // Set up event listeners for existing todo items
            document.querySelectorAll('.todo-item').forEach(item => {
                setupTodoItemListeners(item);
            });
            
            function setupTodoItemListeners(todoItem) {
                const checkbox = todoItem.querySelector('.todo-checkbox');
                const text = todoItem.querySelector('.todo-text');
                const deleteButton = todoItem.querySelector('.delete-button');
                
                // Toggle completed state
                checkbox.addEventListener('click', function() {
                    checkbox.classList.toggle('checked');
                    text.classList.toggle('completed');
                });
                
                // Delete todo item
                deleteButton.addEventListener('click', function() {
                    todoItem.remove();
                    
                    // Show empty state if no todos left
                    if (todoList.children.length === 0) {
                        showEmptyState();
                    }
                });
            }
            
            // Function to show empty state
            function showEmptyState() {
                const emptyState = document.createElement('div');
                emptyState.className = 'empty-state';
                
                const emptyIcon = document.createElement('div');
                emptyIcon.className = 'empty-icon';
                emptyIcon.textContent = '📝';
                
                const emptyText = document.createElement('p');
                emptyText.textContent = 'No tasks yet. Add a task to get started!';
                
                emptyState.appendChild(emptyIcon);
                emptyState.appendChild(emptyText);
                
                todoList.appendChild(emptyState);
            }
        });
    </script>
</body>
</html>
  `;

  useEffect(() => {
    if (iframeRef.current) {
      try {
        console.log('Writing HTML directly to iframe');
        const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow.document;
        
        // Clear the document first
        iframeDoc.open();
        iframeDoc.write(hardcodedHtml);
        iframeDoc.close();
        
        console.log('HTML written to iframe successfully');
        setIsLoading(false);
      } catch (error) {
        console.error('Error writing HTML to iframe:', error);
      }
    }
  }, []);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      <Box sx={{ p: 2, backgroundColor: '#F3F4F6' }}>
        <Typography variant="h5" fontWeight={600}>Test HTML Renderer</Typography>
        <Typography variant="body2" color="text.secondary">
          This component directly renders hardcoded HTML in an iframe for testing purposes.
        </Typography>
      </Box>
      
      <Box sx={{ flexGrow: 1, p: 2, backgroundColor: '#F3F4F6' }}>
        <Paper
          elevation={2}
          sx={{
            height: '100%',
            borderRadius: 2,
            overflow: 'hidden',
            position: 'relative'
          }}
        >
          {isLoading && (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                zIndex: 10
              }}
            >
              <CircularProgress size={40} sx={{ color: '#8B5CF6', mb: 2 }} />
              <Typography variant="body2" sx={{ color: '#4B5563', fontWeight: 500 }}>
                Loading HTML content...
              </Typography>
            </Box>
          )}
          
          <iframe
            ref={iframeRef}
            title="HTML Preview"
            style={{
              border: 'none',
              width: '100%',
              height: '100%',
              display: 'block',
              backgroundColor: 'white'
            }}
          />
        </Paper>
      </Box>
    </Box>
  );
}

export default TestHtmlRenderer;
