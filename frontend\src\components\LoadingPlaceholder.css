.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.loading-placeholder p {
  margin: 10px 0;
  color: #6b7280;
  font-size: 16px;
}

.loading-placeholder .spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(139, 92, 246, 0.2);
  border-radius: 50%;
  border-top-color: #8b5cf6;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-placeholder .progress-bar {
  width: 200px;
  height: 6px;
  background-color: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
  margin: 20px 0;
  position: relative;
}

.loading-placeholder .progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 30%;
  background-color: #8b5cf6;
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    left: -30%;
    width: 30%;
  }
  50% {
    width: 30%;
  }
  100% {
    left: 100%;
    width: 30%;
  }
}
