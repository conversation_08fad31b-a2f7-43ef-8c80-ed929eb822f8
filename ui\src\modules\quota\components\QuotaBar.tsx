import React from 'react';
import styles from './QuotaBar.module.css';
import { QuotaInfo } from '../QuotaModule';

interface QuotaBarProps {
  /**
   * Quota information
   */
  quota: QuotaInfo;

  /**
   * Item name (e.g., "prototype", "credit")
   */
  itemName?: string;

  /**
   * Optional click handler
   */
  onClick?: () => void;

  /**
   * Optional show labels flag
   */
  showLabels?: boolean;

  /**
   * Optional className for additional styling
   */
  className?: string;
}

/**
 * A progress bar component that displays quota usage
 */
export const QuotaBar: React.FC<QuotaBarProps> = ({
  quota,
  itemName = 'prototype',
  onClick,
  showLabels = true,
  className = ''
}) => {
  const { usedCount, totalCount, remainingCount, plan } = quota;
  const isPro = plan.toLowerCase() !== 'free';

  // Calculate percentage used
  const percentUsed = Math.min(100, Math.round((usedCount / totalCount) * 100));

  // Determine bar color based on usage and plan
  const getBarColor = () => {
    if (remainingCount <= 0) {
      return styles.exceededBar;
    }

    if (remainingCount <= Math.max(1, Math.floor(totalCount * 0.1))) {
      return styles.warningBar;
    }

    return isPro ? styles.proBar : styles.freeBar;
  };

  return (
    <div
      className={`${styles.barContainer} ${className} ${onClick ? styles.clickable : ''}`}
      onClick={onClick}
    >
      {showLabels && (
        <div className={styles.barLabels}>
          <span className={styles.usedLabel}>
            {usedCount} used
          </span>
          <span className={styles.totalLabel}>
            {totalCount} total
          </span>
        </div>
      )}

      <div className={styles.progressBar}>
        <div
          className={`${styles.progressFill} ${getBarColor()}`}
          style={{ width: `${percentUsed}%` }}
        ></div>
      </div>

      {showLabels && remainingCount <= Math.max(1, Math.floor(totalCount * 0.2)) && (
        <div className={styles.remainingLabel}>
          {remainingCount <= 0 ? (
            <span className={styles.exceededLabel}>
              Limit reached
              {!isPro && (
                <button
                  className={styles.upgradeButton}
                  onClick={(e) => {
                    e.stopPropagation(); // Prevent parent click handler
                    window.open("https://justprototype.lemonsqueezy.com/buy/561b5e04-cb9f-4bee-9ed5-a99d8b6cc6fa", '_blank', 'noopener,noreferrer');
                  }}
                >
                  Upgrade
                </button>
              )}
            </span>
          ) : (
            <span className={remainingCount <= 1 ? styles.warningLabel : ''}>
              {remainingCount} {remainingCount === 1 ? itemName : `${itemName}s`} remaining
            </span>
          )}
        </div>
      )}
    </div>
  );
};
