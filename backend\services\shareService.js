const ShareModel = require('../models/Share');
const crypto = require('crypto');
const { Pool } = require('pg');

// Create a PostgreSQL connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// Test database connection
(async () => {
  try {
    const client = await pool.connect();
    console.log('Successfully connected to the database');

    // Test query to get PostgreSQL version
    const result = await client.query('SELECT version()');
    console.log('PostgreSQL version:', result.rows[0].version);

    // Release the client back to the pool
    client.release();
  } catch (err) {
    console.error('Error connecting to the database:', err);
  }
})();

// Function to ensure the shares table exists
async function ensureSharesTableExists() {
  try {
    // Check if the shares table exists
    const tableCheck = await pool.query(
      `SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'shares'
      )`
    );

    console.log('Shares table exists:', tableCheck.rows[0].exists);

    if (!tableCheck.rows[0].exists) {
      console.log('Shares table does not exist. Creating it now...');

      // Create the shares table
      await pool.query(`
        CREATE TABLE IF NOT EXISTS shares (
          id SERIAL PRIMARY KEY,
          prototype_id INTEGER NOT NULL,
          owner_id INTEGER NOT NULL,
          shared_with_email VARCHAR(255),
          shared_with_user_id INTEGER,
          access_level VARCHAR(20) NOT NULL DEFAULT 'view' CHECK (access_level IN ('view', 'comment', 'edit')),
          access_token VARCHAR(64) NOT NULL UNIQUE,
          is_public BOOLEAN NOT NULL DEFAULT FALSE,
          is_active BOOLEAN NOT NULL DEFAULT TRUE,
          expires_at TIMESTAMP WITH TIME ZONE,
          has_accessed BOOLEAN NOT NULL DEFAULT FALSE,
          first_accessed_at TIMESTAMP WITH TIME ZONE,
          last_accessed_at TIMESTAMP WITH TIME ZONE,
          access_count INTEGER NOT NULL DEFAULT 0,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // Create indexes for better performance
      await pool.query(`
        CREATE INDEX IF NOT EXISTS idx_shares_prototype_id ON shares(prototype_id);
        CREATE INDEX IF NOT EXISTS idx_shares_owner_id ON shares(owner_id);
        CREATE INDEX IF NOT EXISTS idx_shares_shared_with_email ON shares(shared_with_email);
        CREATE INDEX IF NOT EXISTS idx_shares_shared_with_user_id ON shares(shared_with_user_id);
        CREATE INDEX IF NOT EXISTS idx_shares_access_token ON shares(access_token);
      `);

      console.log('Shares table created successfully');
    }

    return true;
  } catch (err) {
    console.error('Error ensuring shares table exists:', err);
    return false;
  }
}

// Call the function to ensure the shares table exists
ensureSharesTableExists();

// Function to check if a prototype exists
async function checkPrototypeExists(prototypeId) {
  try {
    const result = await pool.query(
      'SELECT EXISTS(SELECT 1 FROM prototypes WHERE id = $1)',
      [prototypeId]
    );
    return result.rows[0].exists;
  } catch (err) {
    console.error('Error checking if prototype exists:', err);
    return false;
  }
}

// Function to check if a user exists
async function checkUserExists(userId) {
  try {
    const result = await pool.query(
      'SELECT EXISTS(SELECT 1 FROM users WHERE id = $1)',
      [userId]
    );
    return result.rows[0].exists;
  } catch (err) {
    console.error('Error checking if user exists:', err);
    return false;
  }
}

/**
 * Service for managing prototype sharing
 */
const shareService = {
  /**
   * Create a new share for a prototype
   * @param {Object} shareData - Data for the new share
   * @param {string} shareData.prototypeId - ID of the prototype to share
   * @param {string} shareData.ownerId - ID of the user who owns the prototype
   * @param {string} [shareData.sharedWithEmail] - Email of the person to share with (optional for public shares)
   * @param {boolean} [shareData.isPublic=false] - Whether this is a public share
   * @param {string} [shareData.accessLevel='view'] - Access level for the shared user
   * @param {Date} [shareData.expiresAt] - Optional expiration date
   * @returns {Promise<Object>} - The created share object with share URL
   */
  async createShare(shareData) {
    try {
      // For private shares, email is required
      if (!shareData.isPublic && !shareData.sharedWithEmail) {
        throw new Error('Email is required for private shares');
      }

      // Generate a secure random access token
      const accessToken = crypto.randomBytes(32).toString('hex');

      console.log('Creating share with data:', {
        prototypeId: shareData.prototypeId,
        ownerId: shareData.ownerId,
        sharedWithEmail: shareData.sharedWithEmail,
        isPublic: shareData.isPublic,
        accessLevel: shareData.accessLevel || 'view'
      });

      try {
        // Log the query parameters for debugging
        console.log('Attempting to insert share with parameters:', {
          prototype_id: shareData.prototypeId,
          owner_id: shareData.ownerId,
          shared_with_email: shareData.sharedWithEmail ? shareData.sharedWithEmail.toLowerCase() : null,
          is_public: !!shareData.isPublic,
          access_level: shareData.accessLevel || 'view',
          access_token: accessToken,
          expires_at: shareData.expiresAt || null,
          is_active: true
        });

        // Ensure the shares table exists
        const tableExists = await ensureSharesTableExists();
        if (!tableExists) {
          throw new Error('Failed to ensure shares table exists');
        }

        // Check if the prototype exists
        const prototypeExists = await checkPrototypeExists(shareData.prototypeId);
        if (!prototypeExists) {
          console.error(`Prototype with ID ${shareData.prototypeId} does not exist`);
          throw new Error(`Prototype with ID ${shareData.prototypeId} does not exist`);
        }

        // Check if the owner exists
        const ownerExists = await checkUserExists(shareData.ownerId);
        if (!ownerExists) {
          console.error(`User with ID ${shareData.ownerId} does not exist`);
          throw new Error(`User with ID ${shareData.ownerId} does not exist`);
        }

        // Insert the share into the database
        const result = await pool.query(
          `INSERT INTO shares (
            prototype_id,
            owner_id,
            shared_with_email,
            is_public,
            access_level,
            access_token,
            expires_at,
            is_active
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          RETURNING id, created_at, updated_at`,
          [
            shareData.prototypeId,
            shareData.ownerId,
            shareData.sharedWithEmail ? shareData.sharedWithEmail.toLowerCase() : null,
            !!shareData.isPublic,
            shareData.accessLevel || 'view',
            accessToken,
            shareData.expiresAt || null,
            true
          ]
        );

        // Get the inserted share ID and timestamps
        const { id, created_at, updated_at } = result.rows[0];

        // Create the share object with the database ID and timestamps
        const share = {
          id,
          prototypeId: shareData.prototypeId,
          ownerId: shareData.ownerId,
          sharedWithEmail: shareData.sharedWithEmail ? shareData.sharedWithEmail.toLowerCase() : null,
          isPublic: !!shareData.isPublic,
          accessLevel: shareData.accessLevel || 'view',
          accessToken,
          expiresAt: shareData.expiresAt || null,
          isActive: true,
          createdAt: created_at,
          updatedAt: updated_at
        };

        // Generate the share URL - use the frontend URL, not the API URL
        const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
        const shareUrl = `${baseUrl}/shared/${accessToken}`;

        console.log(`Share created successfully with ID: ${id}`);

        return {
          share,
          shareUrl
        };
      } catch (dbError) {
        console.error('Database error when creating share:', dbError);

        // Log more detailed error information
        if (dbError.code) {
          console.error('Error code:', dbError.code);
        }
        if (dbError.detail) {
          console.error('Error detail:', dbError.detail);
        }
        if (dbError.constraint) {
          console.error('Constraint violation:', dbError.constraint);
        }
        if (dbError.table) {
          console.error('Table involved:', dbError.table);
        }
        if (dbError.column) {
          console.error('Column involved:', dbError.column);
        }

        // Check if the error is related to foreign key constraints
        if (dbError.code === '23503') { // Foreign key violation
          console.error('Foreign key violation. Make sure the prototype_id and owner_id exist in their respective tables.');

          // Log the prototype and owner IDs for debugging
          console.error('Prototype ID:', shareData.prototypeId);
          console.error('Owner ID:', shareData.ownerId);
        }

        // If there's a database error, create a dummy share for testing
        console.log('Creating dummy share as fallback');

        const share = {
          id: 'share_' + Math.random().toString(36).substring(2, 15),
          prototypeId: shareData.prototypeId,
          ownerId: shareData.ownerId,
          sharedWithEmail: shareData.sharedWithEmail ? shareData.sharedWithEmail.toLowerCase() : null,
          isPublic: !!shareData.isPublic,
          accessLevel: shareData.accessLevel || 'view',
          accessToken,
          expiresAt: shareData.expiresAt,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // Generate the share URL - use the frontend URL, not the API URL
        const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
        const shareUrl = `${baseUrl}/shared/${accessToken}`;

        return {
          share,
          shareUrl
        };
      }
    } catch (error) {
      // Handle duplicate share
      if (error.code === '23505') { // PostgreSQL unique violation code
        throw new Error('This prototype is already shared with this email');
      }
      throw error;
    }
  },

  /**
   * Get all shares for a prototype
   * @param {string} prototypeId - ID of the prototype
   * @param {string} ownerId - ID of the owner (for verification)
   * @returns {Promise<Array>} - Array of share objects with share URLs
   */
  async getSharesForPrototype(prototypeId, ownerId) {
    try {
      console.log(`Getting shares for prototype: ${prototypeId}, owner: ${ownerId}`);

      try {
        // Query the database for shares
        const sharesResult = await pool.query(
          `SELECT * FROM shares
           WHERE prototype_id = $1 AND owner_id = $2
           ORDER BY created_at DESC`,
          [prototypeId, ownerId]
        );

        // Generate share URLs for each share
        const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

        return sharesResult.rows.map(row => {
          const shareUrl = `${baseUrl}/shared/${row.access_token}`;

          // Convert database row to camelCase for frontend
          const share = {
            id: row.id,
            prototypeId: row.prototype_id,
            ownerId: row.owner_id,
            sharedWithEmail: row.shared_with_email,
            sharedWithUserId: row.shared_with_user_id,
            accessLevel: row.access_level,
            accessToken: row.access_token,
            isPublic: row.is_public,
            isActive: row.is_active,
            expiresAt: row.expires_at,
            hasAccessed: row.has_accessed,
            firstAccessedAt: row.first_accessed_at,
            lastAccessedAt: row.last_accessed_at,
            accessCount: row.access_count,
            createdAt: row.created_at,
            updatedAt: row.updated_at
          };

          return {
            share,
            shareUrl
          };
        });
      } catch (dbError) {
        console.error('Database error when getting shares for prototype:', dbError);

        // Return an empty array if there's a database error
        return [];
      }
    } catch (error) {
      console.error('Error getting shares:', error);
      throw error;
    }
  },

  /**
   * Update a share's properties
   * @param {string} shareId - ID of the share to update
   * @param {string} ownerId - ID of the owner (for verification)
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} - The updated share with share URL
   */
  async updateShare(shareId, ownerId, updateData) {
    try {
      // In a real implementation, this would update the database
      console.log(`Updating share: ${shareId}, owner: ${ownerId}, data:`, updateData);

      // Create a dummy updated share object
      const share = {
        id: shareId,
        ownerId: ownerId,
        accessToken: 'dummy-token-' + Math.random().toString(36).substring(2, 15),
        ...updateData,
        updatedAt: new Date().toISOString()
      };

      // Generate the share URL - use the frontend URL, not the API URL
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
      const shareUrl = `${baseUrl}/shared/${share.accessToken}`;

      return {
        share,
        shareUrl
      };
    } catch (error) {
      console.error('Error updating share:', error);
      throw error;
    }
  },

  /**
   * Delete a share
   * @param {string} shareId - ID of the share to delete
   * @param {string} ownerId - ID of the owner (for verification)
   * @returns {Promise<boolean>} - Whether the deletion was successful
   */
  async deleteShare(shareId, ownerId) {
    try {
      // In a real implementation, this would delete from the database
      console.log(`Deleting share: ${shareId}, owner: ${ownerId}`);

      // Return success
      return true;
    } catch (error) {
      console.error('Error deleting share:', error);
      throw error;
    }
  },

  /**
   * Get a prototype by share token
   * @param {string} accessToken - The access token
   * @returns {Promise<Object>} - The prototype and share information
   */
  async getPrototypeByShareToken(accessToken) {
    try {
      console.log(`Getting prototype by share token: ${accessToken}`);

      try {
        // Try to find the share in the database
        const shareResult = await pool.query(
          `SELECT s.*, p.*, u.display_name, u.email
           FROM shares s
           JOIN prototypes p ON s.prototype_id = p.id
           JOIN users u ON s.owner_id = u.id
           WHERE s.access_token = $1 AND s.is_active = true`,
          [accessToken]
        );

        if (shareResult.rows.length > 0) {
          const shareData = shareResult.rows[0];

          // Record this access
          await pool.query(
            `UPDATE shares
             SET access_count = access_count + 1,
                 last_accessed_at = NOW(),
                 has_accessed = true,
                 first_accessed_at = COALESCE(first_accessed_at, NOW())
             WHERE access_token = $1`,
            [accessToken]
          );

          console.log(`Found prototype for token: ${accessToken}`);

          // Return the prototype and share info
          return {
            prototype: {
              id: shareData.prototype_id,
              title: shareData.title,
              description: shareData.description,
              html: shareData.html,
              css: shareData.css,
              createdAt: shareData.created_at
            },
            share: {
              accessLevel: shareData.access_level,
              owner: {
                id: shareData.owner_id,
                displayName: shareData.display_name,
                email: shareData.email
              },
              expiresAt: shareData.expires_at,
              isPublic: shareData.is_public
            }
          };
        }
      } catch (dbError) {
        console.error('Database error when getting prototype by share token:', dbError);
      }

      // If we couldn't find the share or there was a database error, create a dummy prototype
      console.log(`Creating dummy prototype for token: ${accessToken}`);

      const dummyPrototype = {
        id: 'prototype_' + Math.random().toString(36).substring(2, 15),
        title: 'Sample Prototype',
        description: 'This is a sample prototype for testing shared links',
        html: `
          <div class="container mx-auto p-8">
            <div class="bg-white rounded-lg shadow-lg p-6">
              <h1 class="text-3xl font-bold text-primary mb-4">Sample Prototype</h1>
              <p class="text-gray-700 mb-4">This is a sample prototype that demonstrates the sharing functionality.</p>
              <div class="bg-gray-100 p-4 rounded-lg mb-4">
                <h2 class="text-xl font-semibold mb-2">Features</h2>
                <ul class="list-disc pl-5 space-y-1">
                  <li>Public and private sharing</li>
                  <li>Access control (view, comment, edit)</li>
                  <li>Expiration dates</li>
                  <li>Access tracking</li>
                </ul>
              </div>
              <button class="bg-primary text-white px-4 py-2 rounded-button hover:bg-blue-700 transition-colors">
                Sample Button
              </button>
            </div>
          </div>
        `,
        css: `
          .text-primary { color: #0047AB; }
          .bg-primary { background-color: #0047AB; }
        `,
        createdAt: new Date().toISOString()
      };

      const dummyOwner = {
        id: 'user_' + Math.random().toString(36).substring(2, 15),
        displayName: 'Demo User',
        email: '<EMAIL>'
      };

      // Return the dummy prototype and share info
      return {
        prototype: dummyPrototype,
        share: {
          accessLevel: 'view',
          owner: dummyOwner,
          expiresAt: null,
          isPublic: true
        }
      };
    } catch (error) {
      console.error('Error getting prototype by share token:', error);
      throw error;
    }
  },

  /**
   * Get all prototypes shared with a user
   * @param {string} email - Email of the user
   * @returns {Promise<Array>} - Array of shared prototypes with share URLs
   */
  async getPrototypesSharedWithUser(email) {
    try {
      // In a real implementation, this would query the database
      console.log(`Getting prototypes shared with user: ${email}`);

      // Return an empty array for now
      const shares = [];

      // Generate share URLs for each share - use the frontend URL, not the API URL
      const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';

      return shares.map(share => {
        const shareUrl = `${baseUrl}/shared/${share.accessToken}`;
        return {
          prototype: share.prototypeId,
          share: {
            id: share.id,
            accessLevel: share.accessLevel,
            owner: share.ownerId,
            expiresAt: share.expiresAt,
            createdAt: share.createdAt,
            isPublic: share.isPublic,
            shareUrl
          }
        };
      });
    } catch (error) {
      console.error('Error getting prototypes shared with user:', error);
      throw error;
    }
  },

  /**
   * Validate if a user has access to a shared prototype
   * @param {string} prototypeId - ID of the prototype
   * @param {string} email - Email of the user trying to access
   * @param {string} accessToken - Access token from the share URL
   * @returns {Promise<Object>} - Access information
   */
  async validateAccess(prototypeId, email, accessToken) {
    try {
      // In a real implementation, this would query the database
      console.log(`Validating access for prototype: ${prototypeId}, email: ${email}, token: ${accessToken}`);

      // For testing purposes, always return access granted
      if (accessToken) {
        return {
          hasAccess: true,
          accessLevel: 'view',
          isPublic: true
        };
      }

      // If email is provided, also grant access
      if (email) {
        return {
          hasAccess: true,
          accessLevel: 'view',
          isPublic: false
        };
      }

      // No access found
      return {
        hasAccess: false
      };
    } catch (error) {
      console.error('Error validating access:', error);
      throw error;
    }
  }
};

module.exports = shareService;
