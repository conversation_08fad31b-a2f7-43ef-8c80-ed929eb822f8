/**
 * Types and interfaces for page creation functionality
 */

export interface PageCreationContext {
  projectId?: string;
  userId?: number;
  deviceType?: 'desktop' | 'mobile' | 'tablet';
}

export interface PageCreationRequest {
  prompt: string;
  pageName?: string;
  context?: PageCreationContext;
}

export interface PageCreationResult {
  success: boolean;
  pageId?: string;
  pageName?: string;
  error?: string;
}

export type PageCreationSource = 
  | 'new-page-flow'      // User clicks "New Page" and enters prompt
  | 'element-implementation' // User implements element as new page
  | 'navigation-link'    // User clicks non-existent navigation link
  | 'confirmation-dialog'; // User confirms page creation from dialog

export interface PageCreationMetrics {
  source: PageCreationSource;
  promptLength: number;
  nameGenerationTime: number;
  nameGenerationSource: 'api' | 'fallback';
  totalCreationTime: number;
}

export interface PendingPageCreation {
  pageName: string;
  pageId: string;
  source: PageCreationSource;
  originalPrompt?: string;
}
