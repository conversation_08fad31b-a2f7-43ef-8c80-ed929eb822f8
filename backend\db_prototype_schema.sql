-- PostgreSQL schema for prototypes

-- The "prototypes" table stores generated prototype metadata, structure, code, and association with users.
-- Each prototype belongs to one user and may be linked to a prompt (optional for traceability).
-- Only essential columns are included; stay consistent with existing schema style.

CREATE TABLE IF NOT EXISTS prototypes (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    prompt_id INTEGER REFERENCES prompts(id) ON DELETE SET NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    html TEXT NOT NULL,          -- Generated HTML code
    css TEXT,                    -- (Optional) Generated CSS code
    preview_image_url TEXT,      -- (Optional) Cached screenshot or preview
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Index to quickly find prototypes by user and sort by creation time
CREATE INDEX IF NOT EXISTS idx_prototypes_user_created_at ON prototypes(user_id, created_at DESC);

-- (Optional, but consistent) Function and trigger to auto-update updated_at timestamp
CREATE OR REPLACE FUNCTION update_prototypes_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS set_prototypes_updated_at ON prototypes;
CREATE TRIGGER set_prototypes_updated_at
BEFORE UPDATE ON prototypes
FOR EACH ROW
EXECUTE FUNCTION update_prototypes_updated_at();

-- End of prototypes schema.