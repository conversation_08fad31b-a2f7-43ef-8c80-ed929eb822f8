/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        gray: {
          50: '#f9fafb',
          100: '#1F2937',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
        indigo: {
          50: '#eef2ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81',
        },
        red: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        green: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
      },
    },
  },
  safelist: [
    'bg-gray-50',
    'bg-gray-100',
    'bg-gray-200',
    'bg-gray-300',
    'bg-gray-400',
    'bg-gray-500',
    'bg-gray-600',
    'bg-gray-700',
    'bg-gray-800',
    'bg-gray-900',
    'text-gray-50',
    'text-gray-100',
    'text-gray-200',
    'text-gray-300',
    'text-gray-400',
    'text-gray-500',
    'text-gray-600',
    'text-gray-700',
    'text-gray-800',
    'text-gray-900',
    'border-gray-50',
    'border-gray-100',
    'border-gray-200',
    'border-gray-300',
    'border-gray-400',
    'border-gray-500',
    'border-gray-600',
    'border-gray-700',
    'border-gray-800',
    'border-gray-900',
    'bg-indigo-50',
    'bg-indigo-100',
    'bg-indigo-200',
    'bg-indigo-300',
    'bg-indigo-400',
    'bg-indigo-500',
    'bg-indigo-600',
    'bg-indigo-700',
    'bg-indigo-800',
    'bg-indigo-900',
    'text-indigo-50',
    'text-indigo-100',
    'text-indigo-200',
    'text-indigo-300',
    'text-indigo-400',
    'text-indigo-500',
    'text-indigo-600',
    'text-indigo-700',
    'text-indigo-800',
    'text-indigo-900',
    'border-indigo-50',
    'border-indigo-100',
    'border-indigo-200',
    'border-indigo-300',
    'border-indigo-400',
    'border-indigo-500',
    'border-indigo-600',
    'border-indigo-700',
    'border-indigo-800',
    'border-indigo-900',
    'bg-red-50',
    'bg-red-100',
    'bg-red-200',
    'bg-red-300',
    'bg-red-400',
    'bg-red-500',
    'bg-red-600',
    'bg-red-700',
    'bg-red-800',
    'bg-red-900',
    'text-red-50',
    'text-red-100',
    'text-red-200',
    'text-red-300',
    'text-red-400',
    'text-red-500',
    'text-red-600',
    'text-red-700',
    'text-red-800',
    'text-red-900',
    'border-red-50',
    'border-red-100',
    'border-red-200',
    'border-red-300',
    'border-red-400',
    'border-red-500',
    'border-red-600',
    'border-red-700',
    'border-red-800',
    'border-red-900',
    'bg-green-50',
    'bg-green-100',
    'bg-green-200',
    'bg-green-300',
    'bg-green-400',
    'bg-green-500',
    'bg-green-600',
    'bg-green-700',
    'bg-green-800',
    'bg-green-900',
    'text-green-50',
    'text-green-100',
    'text-green-200',
    'text-green-300',
    'text-green-400',
    'text-green-500',
    'text-green-600',
    'text-green-700',
    'text-green-800',
    'text-green-900',
    'border-green-50',
    'border-green-100',
    'border-green-200',
    'border-green-300',
    'border-green-400',
    'border-green-500',
    'border-green-600',
    'border-green-700',
    'border-green-800',
    'border-green-900',
    'bg-white',
    'text-white',
    'border-white',
    'bg-black',
    'text-black',
    'border-black',
    'rounded-md',
    'rounded-lg',
    'rounded-full',
    'shadow',
    'shadow-sm',
    'shadow-md',
    'shadow-lg',
    'shadow-xl',
    'min-h-screen',
    'h-screen',
    'h-96',
    'w-full',
    'flex',
    'flex-col',
    'items-center',
    'justify-center',
    'justify-between',
    'space-x-2',
    'space-x-3',
    'space-x-4',
    'space-y-2',
    'space-y-3',
    'space-y-4',
    'p-2',
    'p-4',
    'p-6',
    'px-2',
    'px-4',
    'px-6',
    'py-2',
    'py-4',
    'py-6',
    'mt-2',
    'mt-4',
    'mt-6',
    'mt-8',
    'mb-2',
    'mb-4',
    'mb-6',
    'ml-2',
    'ml-3',
    'mr-2',
    'border',
    'border-2',
    'border-4',
    'border-dashed',
    'border-transparent',
    'hover:bg-gray-50',
    'hover:bg-gray-100',
    'hover:bg-indigo-600',
    'hover:bg-indigo-700',
    'hover:text-indigo-900',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-2',
    'focus:ring-indigo-500',
    'focus:border-indigo-500',
    'font-medium',
    'font-semibold',
    'font-bold',
    'font-extrabold',
    'text-xs',
    'text-sm',
    'text-base',
    'text-lg',
    'text-xl',
    'text-2xl',
    'text-3xl',
    'text-4xl',
    'text-5xl',
    'text-center',
    'whitespace-pre-wrap',
    'overflow-auto',
    'overflow-hidden',
    'animate-spin',
    'animate-pulse',
    'opacity-25',
    'opacity-75',
    'transition',
    'divide-y',
    'divide-gray-200',
    'max-w-xl',
    'max-w-2xl',
    'max-w-7xl',
    'mx-auto',
    'inline-flex',
    'flex-shrink-0',
    'flex-wrap',
    'gap-2',
    'relative',
    'absolute',
    'inset-x-0',
    'transform',
    '-translate-y-1/2',
    'tracking-wide',
    'tracking-wider',
    'tracking-tight',
    'uppercase',
    'leading-6',
    'leading-8',
    'flex-1',
  ],
  plugins: [],
}
