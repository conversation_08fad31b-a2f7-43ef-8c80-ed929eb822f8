# JustPrototype Project Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Current Implementation Status](#current-implementation-status)
4. [Known Issues](#known-issues)
5. [Development Guidelines](#development-guidelines)
6. [Troubleshooting](#troubleshooting)
7. [Next Steps](#next-steps)

## Project Overview

JustPrototype is a web application that allows users to generate interactive UI prototypes using natural language prompts. The application leverages Large Language Models (LLMs) to convert text descriptions into functional HTML, CSS, and JavaScript code.

### Key Features
- Natural language to HTML/CSS/JavaScript generation
- Interactive prototype workspace
- Conversation mode for iterative improvements
- Version history tracking
- Design style customization
- Debug window for monitoring generation progress

## Architecture

### Frontend
- **Framework**: React with Material-UI components
- **State Management**: React hooks and context
- **Storage**: Local storage for prototypes and conversations
- **Key Components**:
  - `PrototypeContainer`: Main container managing workflow stages
  - `PrototypeWorkspace`: Interactive workspace for viewing and editing prototypes
  - `InitialPromptScreen`: Screen for entering the initial prototype description
  - `PlanningScreen`: Shows the design plan before implementation
  - `DebugWindow`: Displays streaming updates during generation

### Backend
- **Framework**: Node.js
- **API Routes**: Located in `backend/routes`
- **Configuration**: Located in `backend/config`
- **Controllers**: Located in `backend/controllers`
- **Server**: Main server file at `backend/server.js`
- **LLM Integration**: Connects to various LLM providers (OpenAI, Anthropic, etc.)

#### Backend Structure
```
backend/
├── config/           # Configuration files
│   ├── default.js    # Default configuration
│   └── llm.js        # LLM provider configuration
├── controllers/      # Request handlers
│   ├── llmController.js  # LLM-related operations
│   └── apiController.js  # API key management
├── routes/           # API route definitions
│   ├── llmRoutes.js  # LLM-related endpoints
│   └── apiRoutes.js  # API management endpoints
├── services/         # Business logic
│   └── llmService.js # LLM provider integration
├── utils/            # Utility functions
│   └── promptUtils.js # Prompt formatting utilities
└── server.js         # Main server entry point
```

#### API Endpoints
- **POST /api/llm/generate**: Generate HTML from a prompt
  - Request body: `{ prompt, provider, model, designSettings }`
  - Response: `{ type, html, designPlan? }`

- **GET /api/llm/providers**: Get available LLM providers
  - Response: `{ providers: [{ id, name, models }] }`

- **POST /api/keys**: Save API keys
  - Request body: `{ provider, key }`
  - Response: `{ success: true }`

### Data Flow
1. User enters a natural language prompt
2. Frontend sends the prompt to the backend
3. Backend forwards the prompt to the selected LLM provider
4. LLM generates a design plan and HTML implementation
5. Backend returns the response to the frontend
6. Frontend renders the HTML in an iframe and displays the design plan
7. User can make follow-up requests to modify the prototype

## Current Implementation Status

### Completed Features

#### Frontend
- **UI Framework**
  - Basic layout with responsive design
  - Material-UI components for consistent styling
  - Light/dark mode support

- **Prototype Generation**
  - Initial prompt input screen
  - Design plan display
  - HTML rendering in iframe
  - Conversation history

- **Workspace Features**
  - Version history navigation
  - Prototype preview
  - Chat interface for follow-up prompts

- **Debug Tools**
  - Debug window for streaming updates
  - Console logging for HTML content
  - Error handling and display

#### Backend
- **API Routes**
  - LLM provider integration
  - HTML generation endpoints
  - Configuration management
  - API key management

- **LLM Integration**
  - Support for multiple providers (OpenAI, Anthropic)
  - Prompt templating with system prompts
  - Response parsing and formatting
  - Error handling and rate limiting

- **Server Configuration**
  - CORS setup for frontend communication
  - Environment-based configuration
  - Error middleware
  - Request logging

- **Security**
  - API key validation
  - Input sanitization
  - No storage of API keys in database (per requirements)

### Partially Implemented Features

- **Streaming Responses**: Currently simulated with progress updates
- **Element Selection**: UI implemented but functionality limited
- **Design Style Selection**: Basic implementation with some issues

## Known Issues

### Critical Issues

1. **HTML Rendering Issue**
   - **Description**: HTML content is not properly rendered in the iframe
   - **Symptoms**: Console shows HTML is received but nothing displays
   - **Attempted Solutions**:
     - Using srcDoc attribute on iframe
     - Directly writing to iframe document
     - Adding proper HTML structure to incomplete HTML
     - Handling truncated HTML by adding closing tags

2. **Truncated API Responses**
   - **Description**: HTML content from API is sometimes truncated
   - **Symptoms**: HTML ends abruptly without closing tags
   - **Current Workaround**: Adding closing tags to truncated HTML

### Minor Issues

1. **Todo App References**
   - Some prompts still reference a Todo app instead of being generic
   - Need to update all prompt templates

2. **UI Space Utilization**
   - Chat boxes could be larger
   - Better use of screen space needed

## Development Guidelines

### Code Structure
- **Components**: Located in `frontend/src/components`
- **Services**: Located in `frontend/src/services`
- **Styles**: Primarily using Material-UI's styling system
- **Assets**: Located in `frontend/src/assets`

### Frontend Coding Standards
- Use functional components with hooks
- Prefer const over let when variables won't be reassigned
- Use async/await for asynchronous operations
- Add comments for complex logic
- Use descriptive variable and function names

### Frontend State Management
- Use React's useState for component-level state
- Use React's useContext for global state when needed
- Store prototypes and settings in localStorage

### API Integration
- Use axios for API requests
- Handle errors gracefully with try/catch
- Implement proper loading states

### Backend Coding Standards
- Use ES6+ syntax
- Organize code using the MVC pattern (routes, controllers, services)
- Use async/await for asynchronous operations
- Implement proper error handling with try/catch
- Use environment variables for configuration
- Follow RESTful API design principles

### Backend Performance Considerations
- Implement request validation to fail fast
- Use proper HTTP status codes
- Consider implementing rate limiting for LLM API calls
- Add caching for frequently used responses
- Use compression for response payloads

## Troubleshooting

### HTML Rendering Issues
1. Check browser console for errors
2. Verify HTML content is being received (check console logs)
3. Ensure HTML has proper structure (DOCTYPE, html, head, body tags)
4. Check for truncated HTML and fix closing tags
5. Verify iframe sandbox attributes allow proper rendering

### API Connection Issues
1. Ensure backend server is running
2. Check API endpoint URLs
3. Verify API keys are properly configured
4. Check network tab in browser dev tools for request/response details
5. Verify CORS is properly configured on the backend

### LLM Provider Issues
1. Verify API keys are valid
2. Check rate limits
3. Ensure prompt format is correct
4. Review provider documentation for any changes
5. Check provider status pages for outages

### Backend Server Issues
1. Check server logs for errors
2. Verify Node.js version compatibility
3. Ensure all dependencies are installed (`npm install`)
4. Check for port conflicts
5. Verify environment variables are properly set
6. Test API endpoints using Postman or curl

### Response Size Issues
1. Check if the LLM provider has response size limits
2. Verify that the backend isn't truncating large responses
3. Check if Express.js is configured to handle large payloads
4. Consider implementing chunked responses for large HTML content
5. Look for any middleware that might be limiting response size

## Next Steps

### Short-term Frontend Improvements
1. **Fix HTML Rendering Issue**
   - Investigate alternative rendering approaches
   - Consider using a sandboxed div instead of iframe
   - Check for content security policy issues

2. **Improve Error Handling**
   - Add more robust error handling for API failures
   - Implement retry mechanisms for failed requests

3. **Enhance User Experience**
   - Add more visual feedback during generation
   - Implement a way to cancel ongoing generations

### Short-term Backend Improvements
1. **Implement True Streaming**
   - Modify backend to support Server-Sent Events (SSE) or WebSockets
   - Implement chunked responses for large HTML content
   - Add progress tracking for LLM requests

2. **Fix Response Truncation**
   - Increase Express.js payload limits
   - Implement proper response handling for large payloads
   - Add compression for large responses

3. **Improve LLM Provider Integration**
   - Add support for more providers (e.g., Cohere, Mistral)
   - Implement fallback mechanisms if primary provider fails
   - Add caching for common requests to reduce API costs

4. **Enhance Logging and Monitoring**
   - Implement structured logging
   - Add request/response logging for debugging
   - Set up monitoring for API rate limits and usage

### Long-term Frontend Roadmap
1. **Advanced Customization**
   - More design style options
   - Component library integration
   - Custom CSS variables

2. **Collaboration Features**
   - Sharing prototypes
   - Comments and feedback
   - Team workspaces

3. **Export Options**
   - Export to code repositories
   - Download as standalone HTML/CSS/JS
   - Integration with design tools

4. **Performance Optimization**
   - Optimizing large HTML rendering
   - Implementing virtualization for large prototypes
   - Progressive loading of complex prototypes

### Long-term Backend Roadmap
1. **Database Integration**
   - Move from localStorage to a proper database
   - User authentication and authorization
   - Prototype versioning and history

2. **Advanced LLM Features**
   - Fine-tuned models for better HTML generation
   - Multi-model pipelines for specialized tasks
   - Implement retrieval-augmented generation (RAG) for design patterns

3. **API Ecosystem**
   - Public API for third-party integrations
   - Webhook support for automation
   - Plugin system for extensibility

4. **Infrastructure Improvements**
   - Containerization with Docker
   - Deployment automation
   - Horizontal scaling for high traffic
   - Comprehensive monitoring and alerting

---

## Appendix: Debugging Tools

### Debug Window
The debug window shows real-time updates during the HTML generation process:
- When HTML generation starts
- Progress updates during generation
- When HTML generation completes
- Any errors that occur during the process

To toggle the debug window, click the code icon in the header.

### Console Logging
Important information is logged to the browser console:
- HTML content length and preview
- API response details
- Error messages
- Iframe loading status

### Testing Recommendations
1. Test with simple HTML first to isolate rendering issues
2. Use the debug window to monitor the streaming process
3. Check browser console for any errors related to iframe content
4. Verify that the HTML content is being properly received from the API

# JustPrototype Project Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Current Implementation Status](#current-implementation-status)
4. [Known Issues](#known-issues)
5. [Development Guidelines](#development-guidelines)
6. [Troubleshooting](#troubleshooting)
7. [Next Steps](#next-steps)

## Project Overview

JustPrototype is a web application (Aimed to be Replica of Readdy.ai)) that allows users to generate interactive UI prototypes using natural language prompts. The application leverages Large Language Models (LLMs) to convert text descriptions into functional HTML, CSS, and JavaScript code.

### Key Features
- Natural language to HTML/CSS/JavaScript generation
- Interactive prototype workspace
- Conversation mode for iterative improvements
- Version history tracking
- Design style customization
- Debug window for monitoring generation progress

High level Requirements:
1.) Should be able to create New Prototype
2.) I should be able to provide Prompt , it should generate plan and then should provide a button to generate prototype.
3.) Output should be streamed .
4,) I should be able to send follow up prompts - output should not change completely
5.) Should be able to select element and request for a change
6.) Should be able to crate versions as well
7.) Auth should be google

Phase 2 - Roll out:
1.) Projects should persist
2.) Stripe Integration
3.) Free tier
4.) Pricing plan
5.) Feedback page
6.) Save Prompts or user activity in Db

Phase 3:
1.) Collobrative editing
2.) Should be able to provide designer window on seleting control
3.) Should ask for layout in the beginning


## Architecture

### Frontend
- **Framework**: React with Material-UI components
- **State Management**: React hooks and context
- **Storage**: Local storage for prototypes and conversations
- **Key Components**:
  - `PrototypeContainer`: Main container managing workflow stages
  - `PrototypeWorkspace`: Interactive workspace for viewing and editing prototypes
  - `InitialPromptScreen`: Screen for entering the initial prototype description
  - `PlanningScreen`: Shows the design plan before implementation
  - `DebugWindow`: Displays streaming updates during generation

### Backend
- **Framework**: Node.js
- **API Routes**: Located in `backend/routes`
- **Configuration**: Located in `backend/config`
- **Controllers**: Located in `backend/controllers`
- **Server**: Main server file at `backend/server.js`
- **LLM Integration**: Connects to various LLM providers (OpenAI, Anthropic, etc.)

#### Backend Structure
```
backend/
├── config/           # Configuration files
│   ├── default.js    # Default configuration
│   └── llm.js        # LLM provider configuration
├── controllers/      # Request handlers
│   ├── llmController.js  # LLM-related operations
│   └── apiController.js  # API key management
├── routes/           # API route definitions
│   ├── llmRoutes.js  # LLM-related endpoints
│   └── apiRoutes.js  # API management endpoints
├── services/         # Business logic
│   └── llmService.js # LLM provider integration
├── utils/            # Utility functions
│   └── promptUtils.js # Prompt formatting utilities
└── server.js         # Main server entry point
```

#### API Endpoints
- **POST /api/llm/generate**: Generate HTML from a prompt
  - Request body: `{ prompt, provider, model, designSettings }`
  - Response: `{ type, html, designPlan? }`

- **GET /api/llm/providers**: Get available LLM providers
  - Response: `{ providers: [{ id, name, models }] }`

- **POST /api/keys**: Save API keys
  - Request body: `{ provider, key }`
  - Response: `{ success: true }`

### Data Flow
1. User enters a natural language prompt
2. Frontend sends the prompt to the backend
3. Backend forwards the prompt to the selected LLM provider
4. LLM generates a design plan and HTML implementation
5. Backend returns the response to the frontend
6. Frontend renders the HTML in an iframe and displays the design plan
7. User can make follow-up requests to modify the prototype

## Current Implementation Status

### Completed Features

#### Frontend
- **UI Framework**
  - Basic layout with responsive design
  - Material-UI components for consistent styling
  - Light/dark mode support

- **Prototype Generation**
  - Initial prompt input screen
  - Design plan display
  - HTML rendering in iframe
  - Conversation history

- **Workspace Features**
  - Version history navigation
  - Prototype preview
  - Chat interface for follow-up prompts

- **Debug Tools**
  - Debug window for streaming updates
  - Console logging for HTML content
  - Error handling and display

#### Backend
- **API Routes**
  - LLM provider integration
  - HTML generation endpoints
  - Configuration management
  - API key management

- **LLM Integration**
  - Support for multiple providers (OpenAI, Anthropic)
  - Prompt templating with system prompts
  - Response parsing and formatting
  - Error handling and rate limiting

- **Server Configuration**
  - CORS setup for frontend communication
  - Environment-based configuration
  - Error middleware
  - Request logging

- **Security**
  - API key validation
  - Input sanitization
  - No storage of API keys in database (per requirements)

### Partially Implemented Features

- **Streaming Responses**: Currently simulated with progress updates
- **Element Selection**: UI implemented but functionality limited
- **Design Style Selection**: Basic implementation with some issues

## Known Issues

### Critical Issues

1. **HTML Rendering Issue**
   - **Description**: HTML content is not properly rendered in the iframe
   - **Symptoms**: Console shows HTML is received but nothing displays
   - **Attempted Solutions**:
     - Using srcDoc attribute on iframe
     - Directly writing to iframe document
     - Adding proper HTML structure to incomplete HTML
     - Handling truncated HTML by adding closing tags

2. **Truncated API Responses**
   - **Description**: HTML content from API is sometimes truncated
   - **Symptoms**: HTML ends abruptly without closing tags
   - **Current Workaround**: Adding closing tags to truncated HTML

### Minor Issues

1. **Todo App References**
   - Some prompts still reference a Todo app instead of being generic
   - Need to update all prompt templates

2. **UI Space Utilization**
   - Chat boxes could be larger
   - Better use of screen space needed

## Development Guidelines

### Code Structure
- **Components**: Located in `frontend/src/components`
- **Services**: Located in `frontend/src/services`
- **Styles**: Primarily using Material-UI's styling system
- **Assets**: Located in `frontend/src/assets`

### Frontend Coding Standards
- Use functional components with hooks
- Prefer const over let when variables won't be reassigned
- Use async/await for asynchronous operations
- Add comments for complex logic
- Use descriptive variable and function names

### Frontend State Management
- Use React's useState for component-level state
- Use React's useContext for global state when needed
- Store prototypes and settings in localStorage

### API Integration
- Use axios for API requests
- Handle errors gracefully with try/catch
- Implement proper loading states

### Backend Coding Standards
- Use ES6+ syntax
- Organize code using the MVC pattern (routes, controllers, services)
- Use async/await for asynchronous operations
- Implement proper error handling with try/catch
- Use environment variables for configuration
- Follow RESTful API design principles

### Backend Performance Considerations
- Implement request validation to fail fast
- Use proper HTTP status codes
- Consider implementing rate limiting for LLM API calls
- Add caching for frequently used responses
- Use compression for response payloads

## Troubleshooting

### HTML Rendering Issues
1. Check browser console for errors
2. Verify HTML content is being received (check console logs)
3. Ensure HTML has proper structure (DOCTYPE, html, head, body tags)
4. Check for truncated HTML and fix closing tags
5. Verify iframe sandbox attributes allow proper rendering

### API Connection Issues
1. Ensure backend server is running
2. Check API endpoint URLs
3. Verify API keys are properly configured
4. Check network tab in browser dev tools for request/response details
5. Verify CORS is properly configured on the backend

### LLM Provider Issues
1. Verify API keys are valid
2. Check rate limits
3. Ensure prompt format is correct
4. Review provider documentation for any changes
5. Check provider status pages for outages

### Backend Server Issues
1. Check server logs for errors
2. Verify Node.js version compatibility
3. Ensure all dependencies are installed (`npm install`)
4. Check for port conflicts
5. Verify environment variables are properly set
6. Test API endpoints using Postman or curl

### Response Size Issues
1. Check if the LLM provider has response size limits
2. Verify that the backend isn't truncating large responses
3. Check if Express.js is configured to handle large payloads
4. Consider implementing chunked responses for large HTML content
5. Look for any middleware that might be limiting response size

## Next Steps

### Short-term Frontend Improvements
1. **Fix HTML Rendering Issue**
   - Investigate alternative rendering approaches
   - Consider using a sandboxed div instead of iframe
   - Check for content security policy issues

2. **Improve Error Handling**
   - Add more robust error handling for API failures
   - Implement retry mechanisms for failed requests

3. **Enhance User Experience**
   - Add more visual feedback during generation
   - Implement a way to cancel ongoing generations

### Short-term Backend Improvements
1. **Implement True Streaming**
   - Modify backend to support Server-Sent Events (SSE) or WebSockets
   - Implement chunked responses for large HTML content
   - Add progress tracking for LLM requests

2. **Fix Response Truncation**
   - Increase Express.js payload limits
   - Implement proper response handling for large payloads
   - Add compression for large responses

3. **Improve LLM Provider Integration**
   - Add support for more providers (e.g., Cohere, Mistral)
   - Implement fallback mechanisms if primary provider fails
   - Add caching for common requests to reduce API costs

4. **Enhance Logging and Monitoring**
   - Implement structured logging
   - Add request/response logging for debugging
   - Set up monitoring for API rate limits and usage

### Long-term Frontend Roadmap
1. **Advanced Customization**
   - More design style options
   - Component library integration
   - Custom CSS variables

2. **Collaboration Features**
   - Sharing prototypes
   - Comments and feedback
   - Team workspaces

3. **Export Options**
   - Export to code repositories
   - Download as standalone HTML/CSS/JS
   - Integration with design tools

4. **Performance Optimization**
   - Optimizing large HTML rendering
   - Implementing virtualization for large prototypes
   - Progressive loading of complex prototypes

### Long-term Backend Roadmap
1. **Database Integration**
   - Move from localStorage to a proper database
   - User authentication and authorization
   - Prototype versioning and history

2. **Advanced LLM Features**
   - Fine-tuned models for better HTML generation
   - Multi-model pipelines for specialized tasks
   - Implement retrieval-augmented generation (RAG) for design patterns

3. **API Ecosystem**
   - Public API for third-party integrations
   - Webhook support for automation
   - Plugin system for extensibility

4. **Infrastructure Improvements**
   - Containerization with Docker
   - Deployment automation
   - Horizontal scaling for high traffic
   - Comprehensive monitoring and alerting

---

## Appendix: Debugging Tools

### Debug Window
The debug window shows real-time updates during the HTML generation process:
- When HTML generation starts
- Progress updates during generation
- When HTML generation completes
- Any errors that occur during the process

To toggle the debug window, click the code icon in the header.

### Console Logging
Important information is logged to the browser console:
- HTML content length and preview
- API response details
- Error messages
- Iframe loading status

### Testing Recommendations
1. Test with simple HTML first to isolate rendering issues
2. Use the debug window to monitor the streaming process
3. Check browser console for any errors related to iframe content
4. Verify that the HTML content is being properly received from the API
