/**
 * <PERSON><PERSON><PERSON> to apply database schema updates
 * 
 * This script applies the schema updates to support multiple authentication providers
 * and sets the default prototype_count to 3 for new users.
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Create a connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.PGSSLMODE === 'require' ? { rejectUnauthorized: false } : false,
});

async function applySchemaUpdates() {
  try {
    console.log('Applying schema updates...');
    
    // Read the schema update SQL file
    const schemaPath = path.join(__dirname, 'db_auth_schema.sql');
    const schemaSql = fs.readFileSync(schemaPath, 'utf8');
    
    // Execute the schema updates
    await pool.query(schemaSql);
    console.log('Schema updates applied successfully!');
    
    // Update existing users to have prototype_count = 3
    const updateResult = await pool.query(`
      UPDATE users 
      SET prototype_count = 3 
      WHERE prototype_count < 3
    `);
    
    console.log(`Updated ${updateResult.rowCount} existing users to have prototype_count = 3`);
    
    // Verify the schema changes
    const tableResult = await pool.query(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'users'
    `);
    
    console.log('Current users table schema:');
    tableResult.rows.forEach(row => {
      console.log(`- ${row.column_name}: ${row.data_type} (${row.is_nullable === 'YES' ? 'nullable' : 'not nullable'})`);
    });
    
    console.log('Schema update complete!');
  } catch (error) {
    console.error('Error applying schema updates:', error);
  } finally {
    // Close the pool
    await pool.end();
  }
}

// Run the function
applySchemaUpdates().catch(console.error);
