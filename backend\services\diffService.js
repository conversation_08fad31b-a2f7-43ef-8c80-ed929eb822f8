/**
 * Diff Service for HTML Content Management
 * Handles diff generation and optimization for LLM responses
 */

const DiffMatchPatch = require('diff-match-patch');

class DiffService {
  constructor() {
    this.dmp = new DiffMatchPatch();

    // Configure diff-match-patch for optimal HTML diffing
    this.dmp.Diff_Timeout = 1.0; // 1 second timeout for diff computation
    this.dmp.Diff_EditCost = 4; // Cost of an edit operation
    this.dmp.Match_Threshold = 0.8; // Threshold for fuzzy matching
    this.dmp.Match_Distance = 1000; // Distance for fuzzy matching
    this.dmp.Patch_DeleteThreshold = 0.5; // Threshold for patch deletion
    this.dmp.Patch_Margin = 4; // Margin around patches
  }

  /**
   * Generate diff between original and modified HTML
   * @param {string} originalHtml - Original HTML content
   * @param {string} modifiedHtml - Modified HTML content
   * @param {Object} options - Diff generation options
   * @returns {Object} Diff data with patches and metadata
   */
  generateDiff(originalHtml, modifiedHtml, options = {}) {
    try {
      const {
        enableSemanticCleanup = true,
        enableEfficiencyCleanup = true,
        minDiffSize = 10,
        maxDiffSize = 50000
      } = options;

      // Normalize HTML content for better diffing
      const normalizedOriginal = this.normalizeHtml(originalHtml);
      const normalizedModified = this.normalizeHtml(modifiedHtml);

      // Generate diff
      const diffs = this.dmp.diff_main(normalizedOriginal, normalizedModified);

      // Apply semantic cleanup to improve diff quality
      if (enableSemanticCleanup) {
        this.dmp.diff_cleanupSemantic(diffs);
      }

      // Apply efficiency cleanup to reduce diff size
      if (enableEfficiencyCleanup) {
        this.dmp.diff_cleanupEfficiency(diffs);
      }

      // Generate patches from diffs
      const patches = this.dmp.patch_make(normalizedOriginal, diffs);
      const patchText = this.dmp.patch_toText(patches);

      // Calculate diff statistics
      const stats = this.calculateDiffStats(diffs, originalHtml, modifiedHtml);

      // Determine if diff is worth using vs full replacement
      const shouldUseDiff = this.shouldUseDiff(stats, minDiffSize, maxDiffSize);

      return {
        success: true,
        shouldUseDiff,
        patches: patchText,
        stats,
        metadata: {
          originalSize: originalHtml.length,
          modifiedSize: modifiedHtml.length,
          patchSize: patchText.length,
          compressionRatio: patchText.length / modifiedHtml.length,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        shouldUseDiff: false,
        patches: null,
        stats: null
      };
    }
  }

  /**
   * Apply diff patches to original HTML
   * @param {string} originalHtml - Original HTML content
   * @param {string} patchText - Patch text to apply
   * @returns {Object} Result with applied HTML or error
   */
  applyDiff(originalHtml, patchText) {
    try {
      const normalizedOriginal = this.normalizeHtml(originalHtml);
      const patches = this.dmp.patch_fromText(patchText);
      const [modifiedHtml, results] = this.dmp.patch_apply(patches, normalizedOriginal);

      // Check if all patches applied successfully
      const allPatchesApplied = results.every(result => result === true);

      return {
        success: allPatchesApplied,
        html: modifiedHtml,
        patchResults: results,
        appliedPatches: results.filter(r => r === true).length,
        totalPatches: results.length
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        html: originalHtml // Fallback to original
      };
    }
  }

  /**
   * Normalize HTML for better diffing
   * @param {string} html - HTML content to normalize
   * @returns {string} Normalized HTML
   */
  normalizeHtml(html) {
    if (!html || typeof html !== 'string') {
      return '';
    }

    return html
      // Normalize whitespace
      .replace(/\s+/g, ' ')
      // Remove extra spaces around tags
      .replace(/>\s+</g, '><')
      // Normalize line endings
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // Trim
      .trim();
  }

  /**
   * Calculate diff statistics
   * @param {Array} diffs - Diff array from diff-match-patch
   * @param {string} originalHtml - Original HTML
   * @param {string} modifiedHtml - Modified HTML
   * @returns {Object} Diff statistics
   */
  calculateDiffStats(diffs, originalHtml, modifiedHtml) {
    let additions = 0;
    let deletions = 0;
    let unchanged = 0;

    diffs.forEach(([operation, text]) => {
      const length = text.length;
      switch (operation) {
        case 1: // DIFF_INSERT
          additions += length;
          break;
        case -1: // DIFF_DELETE
          deletions += length;
          break;
        case 0: // DIFF_EQUAL
          unchanged += length;
          break;
      }
    });

    const totalChanges = additions + deletions;
    const changePercentage = ((totalChanges / originalHtml.length) * 100).toFixed(2);

    return {
      additions,
      deletions,
      unchanged,
      totalChanges,
      changePercentage: parseFloat(changePercentage),
      diffOperations: diffs.length
    };
  }

  /**
   * Determine if diff should be used vs full replacement
   * @param {Object} stats - Diff statistics
   * @param {number} minDiffSize - Minimum diff size to consider
   * @param {number} maxDiffSize - Maximum diff size before using full replacement
   * @returns {boolean} Whether to use diff
   */
  shouldUseDiff(stats, minDiffSize, maxDiffSize) {
    // Use diff if changes are significant but not too large
    return stats.totalChanges >= minDiffSize &&
           stats.totalChanges <= maxDiffSize &&
           stats.changePercentage < 80; // Don't use diff if >80% changed
  }

  /**
   * Create streaming diff response for SSE
   * @param {string} originalHtml - Original HTML content
   * @param {string} modifiedHtml - Modified HTML content
   * @param {Object} options - Options for diff generation
   * @returns {Object} Streaming diff response
   */
  createStreamingDiffResponse(originalHtml, modifiedHtml, options = {}) {
    const diffResult = this.generateDiff(originalHtml, modifiedHtml, options);

    return {
      type: 'diff',
      data: {
        shouldUseDiff: diffResult.shouldUseDiff,
        patches: diffResult.patches,
        stats: diffResult.stats,
        metadata: diffResult.metadata,
        fallbackHtml: diffResult.shouldUseDiff ? null : modifiedHtml
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Validate HTML content for diff processing
   * @param {string} html - HTML content to validate
   * @returns {boolean} Whether HTML is valid for diffing
   */
  validateHtmlForDiff(html) {
    if (!html || typeof html !== 'string') {
      return false;
    }

    // Check for minimum content length
    if (html.trim().length < 10) {
      return false;
    }

    // Check for basic HTML structure
    const hasHtmlTags = /<[^>]+>/g.test(html);
    return hasHtmlTags;
  }

  /**
   * Get diff service statistics
   * @returns {Object} Service statistics
   */
  getStats() {
    return {
      version: '1.0.0',
      diffTimeout: this.dmp.Diff_Timeout,
      editCost: this.dmp.Diff_EditCost,
      matchThreshold: this.dmp.Match_Threshold,
      matchDistance: this.dmp.Match_Distance
    };
  }
}

module.exports = DiffService;
