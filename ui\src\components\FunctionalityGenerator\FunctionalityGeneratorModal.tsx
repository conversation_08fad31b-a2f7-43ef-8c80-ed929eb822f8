import React, { useState, useEffect } from 'react';
import { FiX } from 'react-icons/fi';
import styles from './FunctionalityGenerator.module.css';
import { UnimplementedElement } from './FunctionalityGenerator';

interface FunctionalityGeneratorModalProps {
  element: UnimplementedElement | null;
  defaultPrompt: string;
  onClose: () => void;
  onGenerate: (element: UnimplementedElement, prompt: string) => void;
  isGenerating: boolean;
}

export const FunctionalityGeneratorModal: React.FC<FunctionalityGeneratorModalProps> = ({
  element,
  defaultPrompt,
  onClose,
  onGenerate,
  isGenerating
}) => {
  const [prompt, setPrompt] = useState(defaultPrompt);

  // Update prompt when defaultPrompt changes
  useEffect(() => {
    setPrompt(defaultPrompt);
  }, [defaultPrompt]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (element && prompt.trim()) {
      onGenerate(element, prompt);
    }
  };

  // If no element is selected, don't render the modal
  if (!element) return null;

  return (
    <div className={styles.generatorModal}>
      <div className={styles.generatorModalContent}>
        <div className={styles.generatorModalHeader}>
          <h3 className={styles.generatorModalTitle}>
            Generate Functionality
          </h3>
          <button 
            className={styles.generatorModalClose} 
            onClick={onClose}
            disabled={isGenerating}
          >
            <FiX />
          </button>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.generatorModalBody}>
            <div>
              <label className={styles.generatorModalLabel}>
                Element Type:
              </label>
              <input
                className={styles.generatorModalInput}
                type="text"
                value={element.type}
                readOnly
              />
            </div>
            <div>
              <label className={styles.generatorModalLabel}>
                Element Selector:
              </label>
              <input
                className={styles.generatorModalInput}
                type="text"
                value={element.ref}
                readOnly
              />
            </div>
            <div>
              <label className={styles.generatorModalLabel}>
                Element Description:
              </label>
              <input
                className={styles.generatorModalInput}
                type="text"
                value={element.description || ''}
                readOnly
              />
            </div>
            <div>
              <label className={styles.generatorModalLabel}>
                Element Context:
              </label>
              <input
                className={styles.generatorModalInput}
                type="text"
                value={element.context || ''}
                readOnly
              />
            </div>
            <div>
              <label className={styles.generatorModalLabel}>
                Instructions for Functionality:
              </label>
              <textarea
                className={styles.generatorModalTextarea}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Describe what functionality you want to implement..."
                disabled={isGenerating}
                required
              />
            </div>
          </div>
          <div className={styles.generatorModalFooter}>
            <button
              type="button"
              className={`${styles.generatorModalButton} ${styles.generatorModalCancel}`}
              onClick={onClose}
              disabled={isGenerating}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={`${styles.generatorModalButton} ${styles.generatorModalGenerate}`}
              disabled={isGenerating || !prompt.trim()}
            >
              {isGenerating ? 'Generating...' : 'Generate Functionality'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FunctionalityGeneratorModal;
