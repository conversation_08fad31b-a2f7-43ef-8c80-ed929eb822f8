/**
 * Production-ready API service for EditorV3
 * Handles all backend communication with proper error handling, retries, and streaming
 */

// ============================================================================
// TYPES
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    requestId: string;
    duration: number;
    timestamp: Date;
  };
}

export interface StreamingResponse {
  onStart?: () => void;
  onData?: (chunk: string) => void;
  onEnd?: (finalContent: string) => void;
  onError?: (error: Error) => void;
}

export interface GenerateRequest {
  prompt: string;
  options?: {
    streaming?: boolean;
    provider?: string;
  };
}

export interface EditRequest {
  htmlContent: string;
  prompt: string;
  elementSelector?: string;
  options?: {
    streaming?: boolean;
    provider?: string;
    preserveFormatting?: boolean;
  };
}

export interface LinkPagesRequest {
  pages: Array<{
    id: string;
    name: string;
    content: string;
  }>;
  options?: {
    concurrency?: number;
    delay?: number;
  };
}

// ============================================================================
// CONFIGURATION
// ============================================================================

const API_BASE_URL = '/api/llm/v3';
const DEFAULT_TIMEOUT = 30000; // 30 seconds
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

class ApiError extends Error {
  constructor(
    message: string,
    public code: string,
    public status?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

const delay = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

const generateRequestId = (): string => 
  `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const isRetryableError = (error: any): boolean => {
  if (error instanceof ApiError) {
    return error.status ? error.status >= 500 : true;
  }
  return !error.name?.includes('Abort') && error.name !== 'TypeError';
};

// ============================================================================
// CORE API SERVICE CLASS
// ============================================================================

export class EditorApiService {
  private abortControllers: Map<string, AbortController> = new Map();

  // ============================================================================
  // PUBLIC METHODS
  // ============================================================================

  /**
   * Generate new HTML content from a prompt
   */
  async generateHtml(
    request: GenerateRequest,
    streamingCallbacks?: StreamingResponse
  ): Promise<ApiResponse<string>> {
    const requestId = generateRequestId();
    
    try {
      if (request.options?.streaming && streamingCallbacks) {
        return await this.handleStreamingRequest(
          '/generate-html',
          { prompt: request.prompt },
          streamingCallbacks,
          requestId
        );
      } else {
        return await this.handleStandardRequest(
          '/generate-html',
          { prompt: request.prompt },
          requestId
        );
      }
    } catch (error) {
      return this.handleError(error, requestId);
    }
  }

  /**
   * Edit existing HTML content
   */
  async editHtml(
    request: EditRequest,
    streamingCallbacks?: StreamingResponse
  ): Promise<ApiResponse<string>> {
    const requestId = generateRequestId();
    
    try {
      const payload = {
        htmlContent: request.htmlContent,
        prompt: request.prompt,
        elementSelector: request.elementSelector,
        ...request.options
      };

      if (request.options?.streaming && streamingCallbacks) {
        return await this.handleStreamingRequest(
          '/edit',
          payload,
          streamingCallbacks,
          requestId
        );
      } else {
        return await this.handleStandardRequest(
          '/edit',
          payload,
          requestId
        );
      }
    } catch (error) {
      return this.handleError(error, requestId);
    }
  }

  /**
   * Link multiple pages with navigation
   */
  async linkPages(
    request: LinkPagesRequest,
    progressCallback?: (progress: number, currentPage: string) => void
  ): Promise<ApiResponse<Array<{ pageId: string; success: boolean; content?: string; error?: string }>>> {
    const requestId = generateRequestId();
    const startTime = Date.now();
    
    try {
      const { pages, options = {} } = request;
      const { concurrency = 3, delay: delayMs = 1000 } = options;
      const results: Array<{ pageId: string; success: boolean; content?: string; error?: string }> = [];
      
      // Process pages in batches to avoid overwhelming the API
      for (let i = 0; i < pages.length; i += concurrency) {
        const batch = pages.slice(i, i + concurrency);
        const batchPromises = batch.map(async (page) => {
          try {
            const otherPageNames = pages
              .filter(p => p.id !== page.id)
              .map(p => p.name);

            const prompt = this.generateNavigationPrompt(otherPageNames);
            
            const response = await this.editHtml({
              htmlContent: page.content,
              prompt,
              options: { streaming: false }
            });

            progressCallback?.(
              ((i + batch.indexOf(page) + 1) / pages.length) * 100,
              page.name
            );

            if (response.success) {
              return {
                pageId: page.id,
                success: true,
                content: response.data
              };
            } else {
              return {
                pageId: page.id,
                success: false,
                error: response.error?.message || 'Unknown error'
              };
            }
          } catch (error) {
            return {
              pageId: page.id,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            };
          }
        });

        const batchResults = await Promise.allSettled(batchPromises);
        
        for (const result of batchResults) {
          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            results.push({
              pageId: 'unknown',
              success: false,
              error: result.reason?.message || 'Batch operation failed'
            });
          }
        }

        // Add delay between batches
        if (i + concurrency < pages.length) {
          await delay(delayMs);
        }
      }

      return {
        success: true,
        data: results,
        metadata: {
          requestId,
          duration: Date.now() - startTime,
          timestamp: new Date()
        }
      };
    } catch (error) {
      return this.handleError(error, requestId);
    }
  }

  /**
   * Cancel an ongoing request
   */
  cancelRequest(requestId: string): void {
    const controller = this.abortControllers.get(requestId);
    if (controller) {
      controller.abort();
      this.abortControllers.delete(requestId);
    }
  }

  /**
   * Cancel all ongoing requests
   */
  cancelAllRequests(): void {
    for (const [requestId, controller] of this.abortControllers) {
      controller.abort();
    }
    this.abortControllers.clear();
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  private async handleStreamingRequest(
    endpoint: string,
    payload: any,
    callbacks: StreamingResponse,
    requestId: string
  ): Promise<ApiResponse<string>> {
    const controller = new AbortController();
    this.abortControllers.set(requestId, controller);
    const startTime = Date.now();

    try {
      const response = await this.makeRequest(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        signal: controller.signal,
        body: JSON.stringify(payload)
      });

      const reader = response.body?.getReader();
      if (!reader) {
        throw new ApiError('No response body available for streaming', 'NO_RESPONSE_BODY');
      }

      const decoder = new TextDecoder();
      let accumulatedContent = '';
      let isCollectingHTML = false;

      callbacks.onStart?.();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            const event = line.substring(6);
            if (event === 'start') {
              isCollectingHTML = true;
            } else if (event === 'end') {
              isCollectingHTML = false;
              callbacks.onEnd?.(accumulatedContent);
            }
          } else if (line.startsWith('data:') && isCollectingHTML) {
            const data = line.substring(5);
            accumulatedContent += data;
            callbacks.onData?.(data);
          }
        }
      }

      return {
        success: true,
        data: accumulatedContent,
        metadata: {
          requestId,
          duration: Date.now() - startTime,
          timestamp: new Date()
        }
      };
    } catch (error) {
      callbacks.onError?.(error instanceof Error ? error : new Error('Unknown streaming error'));
      throw error;
    } finally {
      this.abortControllers.delete(requestId);
    }
  }

  private async handleStandardRequest(
    endpoint: string,
    payload: any,
    requestId: string
  ): Promise<ApiResponse<string>> {
    const controller = new AbortController();
    this.abortControllers.set(requestId, controller);
    const startTime = Date.now();

    try {
      const response = await this.makeRequest(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        signal: controller.signal,
        body: JSON.stringify(payload)
      });

      const data = await response.json();

      return {
        success: true,
        data: data.html || data.content || data,
        metadata: {
          requestId,
          duration: Date.now() - startTime,
          timestamp: new Date()
        }
      };
    } catch (error) {
      throw error;
    } finally {
      this.abortControllers.delete(requestId);
    }
  }

  private async makeRequest(
    endpoint: string,
    options: RequestInit
  ): Promise<Response> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        const response = await fetch(url, {
          ...options,
          signal: options.signal
        });

        if (!response.ok) {
          throw new ApiError(
            `HTTP ${response.status}: ${response.statusText}`,
            'HTTP_ERROR',
            response.status
          );
        }

        return response;
      } catch (error) {
        if (attempt === MAX_RETRIES || !isRetryableError(error)) {
          throw error;
        }
        
        console.warn(`Request attempt ${attempt} failed, retrying...`, error);
        await delay(RETRY_DELAY * attempt);
      }
    }

    throw new ApiError('Max retries exceeded', 'MAX_RETRIES_EXCEEDED');
  }

  private handleError(error: any, requestId: string): ApiResponse<never> {
    const apiError = error instanceof ApiError ? error : new ApiError(
      error.message || 'Unknown error occurred',
      error.name || 'UNKNOWN_ERROR'
    );

    return {
      success: false,
      error: {
        code: apiError.code,
        message: apiError.message,
        details: apiError.details
      },
      metadata: {
        requestId,
        duration: 0,
        timestamp: new Date()
      }
    };
  }

  private generateNavigationPrompt(otherPageNames: string[]): string {
    return `Update the navigation bar to include links to all pages: ${otherPageNames.join(', ')}

🎯 **CRITICAL REQUIREMENTS:**
1. **Keep all existing content and design exactly the same** - only modify navigation
2. **Add navigation links** for each page listed above
3. **Use proper <a> tags** with clickable text (e.g., <a href="#">About</a>)
4. **Maintain consistent styling** with existing navigation elements
5. **Do not change any other content** on the page - only the navigation bar
6. **If no navigation exists**, create a simple navigation bar at the top
7. **Make links clearly visible** and properly styled

**Navigation Format:**
- Use <a> tags for each page link
- Include "Home" or "Main" link if this isn't the main page
- Style consistently with the page design
- Position at the top of the page (header/nav area)

Add these navigation links while preserving everything else on the page exactly as it is.`;
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const editorApiService = new EditorApiService();
export default editorApiService;
