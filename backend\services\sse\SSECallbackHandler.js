const { sanitizeHTML } = require('../htmlValidationService');

class SSECallbackHandler {
  constructor(res, options = {}) {
    this.res = res;
    this.task = options.task || 'default';
    this.streamTokens = options.streamTokens || false;
    this.tokenBatchSize = options.tokenBatchSize || 0;
    this.tokenBatchTimeout = options.tokenBatchTimeout || 50;
    this.tokenBuffer = [];
    this.timeoutId = null;
    this.headersWritten = false;
    this.contentBuffer = '';
    this.responseEnded = false;
    this.codeBlockStarted = false;
    this.lastSentCode = '';  // Track last sent code to prevent duplicates
  }

  ensureHeaders() {
    if (!this.headersWritten && !this.responseEnded) {
      this.headersWritten = true;
      return true;
    }
    return false;
  }

  handleStart() {
    if (this.ensureHeaders()) {
      this.sendEvent('start', 'Starting generation...');
    }
  }

  handleError(error) {
    console.error(`SSE ${this.task} error:`, error);
    if (this.ensureHeaders()) {
      this.sendEvent('error', { error: error.message });
    }
    this.handleEnd();
  }

  handleEnd() {
    this.flushTokenBuffer();
    
    // Process any remaining content
    if (this.contentBuffer) {
      const code = this.extractCode(this.contentBuffer);
      if (code && code !== this.lastSentCode) {
        this.sendEvent('formatted_code', { type: 'final', code });
        this.lastSentCode = code;
      }
      this.contentBuffer = '';
    }
    
    if (!this.responseEnded) {
      this.sendEvent('end');
      this.responseEnded = true;
      this.res.end();
    }
  }

  extractCode(content) {
    if (!content) return '';

    // Extract code from markdown code blocks
    const codeBlockRegex = /```(?:html)?\n([\s\S]*?)```/;
    const match = content.match(codeBlockRegex);
    if (match) {
      this.codeBlockStarted = false;  // Reset flag after finding complete block
      return match[1].trim();
    }

    // If we're in a code block but haven't found the end yet
    if (this.codeBlockStarted) {
      const endIndex = content.indexOf('```', 3);
      if (endIndex !== -1) {
        this.codeBlockStarted = false;
        return content.substring(content.indexOf('\n') + 1, endIndex).trim();
      }
    }

    // If we find the start of a code block
    const startIndex = content.indexOf('```html');
    if (startIndex !== -1) {
      this.codeBlockStarted = true;
      const afterStart = content.split('```html\n')[1];
      return afterStart ? afterStart.trim() : '';
    }

    // If no code block found but content looks like HTML
    if (content.trim().toLowerCase().startsWith('<!doctype html') || 
        content.trim().toLowerCase().startsWith('<html')) {
      return content.trim();
    }

    return '';
  }

  handleToken(token) {
    if (!this.responseEnded) {
      // Accumulate content for code extraction
      this.contentBuffer += token;

      // Stream tokens if enabled
      if (this.streamTokens) {
        if (this.tokenBatchSize > 0) {
          this.bufferToken(token);
        } else {
          this.sendEvent('token', { content: token });
        }
      }

      // Check for code blocks and send updates
      const code = this.extractCode(this.contentBuffer);
      if (code && code !== this.lastSentCode) {
        this.sendEvent('formatted_code', {
          type: this.codeBlockStarted ? 'partial' : 'final',
          code
        });
        this.lastSentCode = code;

        // Only clear buffer if we found a complete block
        if (!this.codeBlockStarted) {
          this.contentBuffer = '';
        }
      }
    }
  }

  bufferToken(token) {
    this.tokenBuffer.push(token);

    if (this.tokenBuffer.length >= this.tokenBatchSize) {
      this.flushTokenBuffer();
    } else if (!this.timeoutId) {
      this.timeoutId = setTimeout(() => this.flushTokenBuffer(), this.tokenBatchTimeout);
    }
  }

  flushTokenBuffer() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    if (this.tokenBuffer.length > 0) {
      this.sendEvent('token', { content: this.tokenBuffer.join('') });
      this.tokenBuffer = [];
    }
  }

  handleText(text) {
    if (!this.responseEnded) {
      this.contentBuffer += text;
      
      const code = this.extractCode(this.contentBuffer);
      if (code && code !== this.lastSentCode) {
        this.sendEvent('formatted_code', {
          type: 'final',
          code
        });
        this.lastSentCode = code;
        this.contentBuffer = '';
      }
    }
  }

  sendEvent(event, data = '') {
    if (!this.responseEnded) {
      try {
        let eventData = data;

        // Convert data to string if it's an object
        if (typeof data === 'object') {
          if (event === 'formatted_code' && data.code) {
            // Ensure code is a string and sanitize if it's HTML
            data.code = String(data.code);
            if (data.code.trim().toLowerCase().startsWith('<!doctype html') || 
                data.code.trim().toLowerCase().startsWith('<html')) {
              data.code = sanitizeHTML(data.code);
            }
          }
          eventData = JSON.stringify(data);
        }

        // Send the event
        this.res.write(`event: ${event}\n`);
        this.res.write(`data: ${eventData}\n\n`);
      } catch (error) {
        console.error('Error sending SSE event:', error);
        this.handleError(error);
      }
    }
  }

  getCallbacks() {
    return {
      handleLLMNewToken: (token) => {
        this.handleToken(token);
      },
      handleLLMEnd: () => {
        this.handleEnd();
      },
      handleLLMError: (error) => {
        this.handleError(error);
      },
      handleChainEnd: (outputs) => {
        if (outputs.text) {
          this.handleText(outputs.text);
        }
        this.handleEnd();
      }
    };
  }
}

module.exports = SSECallbackHandler;
