import React from 'react';
import { Box, Paper, Typography, IconButton } from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';

function DebugWindow({ messages, onClose }) {
  return (
    <Paper
      elevation={3}
      sx={{
        position: 'fixed',
        bottom: 20,
        right: 20,
        width: 400,
        maxHeight: 300,
        zIndex: 9999,
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: '8px',
        border: '1px solid rgba(0, 0, 0, 0.1)'
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '8px 16px',
          backgroundColor: '#4F46E5',
          color: 'white'
        }}
      >
        <Typography variant="subtitle2" fontWeight={600}>
          Debug: Streaming Response
        </Typography>
        <IconButton size="small" onClick={onClose} sx={{ color: 'white' }}>
          <CloseIcon fontSize="small" />
        </IconButton>
      </Box>
      
      <Box
        sx={{
          padding: '12px',
          overflowY: 'auto',
          flexGrow: 1,
          backgroundColor: '#1E1E38',
          color: '#E5E7EB',
          fontFamily: 'monospace',
          fontSize: '0.85rem'
        }}
      >
        {messages.map((message, index) => (
          <Box key={index} sx={{ mb: 1 }}>
            <Typography
              variant="body2"
              component="div"
              sx={{
                fontFamily: 'monospace',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                color: message.type === 'error' ? '#F87171' : 
                       message.type === 'info' ? '#60A5FA' : 
                       message.type === 'success' ? '#34D399' : '#E5E7EB'
              }}
            >
              [{new Date(message.timestamp).toLocaleTimeString()}] {message.content}
            </Typography>
          </Box>
        ))}
      </Box>
    </Paper>
  );
}

export default DebugWindow;
