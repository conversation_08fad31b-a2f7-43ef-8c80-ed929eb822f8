# SPAShell API Reference v2.1

**Version:** 2.1  
**Date:** December 2024  
**Component:** SPAShell Integration API  
**Status:** Production Ready  

## 📋 Overview

This document provides a comprehensive API reference for the SPAShell integration in EditorPageV3Refactored, including all functions, components, props, and integration points.

## 🏗️ Core Components

### SPAShell Component

```typescript
interface SPAShellProps {
  className?: string;
  enableEditMode?: boolean;
  dashboardHtml?: string;
}

export const SPAShell: React.FC<SPAShellProps>
```

#### Props
- **className** (optional): CSS classes to apply to the SPAShell container
- **enableEditMode** (optional): Enable/disable live edit mode functionality
- **dashboardHtml** (optional): HTML content to display in the dashboard view

#### Features
- Multi-view routing system (Dashboard, Analytics, Settings)
- Component registry for modular widgets
- Action registry for centralized event handling
- Live edit mode with click-to-edit functionality
- Diff modal for reviewing changes
- Loading overlays and status indicators

### Hybrid Preview Component

```typescript
// Conditional rendering component in EditorPageV3Refactored
{useSPAMode ? (
  <SPAShell
    className="h-full"
    enableEditMode={spaEditMode}
    dashboardHtml={state.htmlContent || state.stableIframeContent || ''}
  />
) : (
  <PreviewPanel
    htmlContent={state.htmlContent || state.stableIframeContent || ''}
    viewMode={generationViewMode}
    onViewModeChange={setGenerationViewMode}
    isGenerating={state.isGenerating || isLoadingPage}
    streamingContent={isLoadingPage ? 'Loading existing page...' : state.streamingContent}
    stableIframeContent={state.stableIframeContent}
    onElementClick={handleElementClick}
  />
)}
```

## 🔧 State Management

### SPAShell Integration State

```typescript
// Mode control state
const [useSPAMode, setUseSPAMode] = useState(false);
const [spaEditMode, setSpaEditMode] = useState(false);
```

#### State Variables
- **useSPAMode**: `boolean` - Controls whether SPAShell or PreviewPanel is active
- **spaEditMode**: `boolean` - Controls whether SPAShell edit mode is enabled

#### State Setters
- **setUseSPAMode**: `(value: boolean) => void` - Switch between preview modes
- **setSpaEditMode**: `(value: boolean) => void` - Toggle SPAShell edit mode

## 🎮 Integration Functions

### Mode Control Functions

#### toggleSPAMode()
```typescript
const toggleSPAMode = () => {
  setUseSPAMode(!useSPAMode);
  console.log(`🔄 Switched to ${!useSPAMode ? 'SPAShell' : 'PreviewPanel'} mode`);
};
```
**Purpose**: Toggle between PreviewPanel and SPAShell modes  
**Parameters**: None  
**Returns**: `void`  
**Side Effects**: Updates `useSPAMode` state, logs mode change  

#### handleSPAEditModeToggle()
```typescript
const handleSPAEditModeToggle = () => {
  setSpaEditMode(!spaEditMode);
  console.log(`🔄 SPAShell edit mode: ${!spaEditMode ? 'enabled' : 'disabled'}`);
};
```
**Purpose**: Toggle SPAShell edit mode on/off  
**Parameters**: None  
**Returns**: `void`  
**Side Effects**: Updates `spaEditMode` state, logs edit mode change  

### Element Handling Functions

#### handleElementClick()
```typescript
const handleElementClick = async (element: any) => {
  console.log('🔥 Enhanced element click handler:', element);
  
  // Handle navigation clicks
  if (element.isNavigation) {
    handleNavigationClick(element);
    return;
  }
  
  // Handle interactive elements that need implementation
  if ((element.implementationType && element.implementationReason) || element.isInteractive) {
    actions.setSelectedElement(element);
    actions.setShowImplementModal(true);
    return;
  }
  
  console.log('🔥 Element does not need implementation, ignoring');
};
```
**Purpose**: Enhanced element click handler that works with both PreviewPanel and SPAShell  
**Parameters**: 
- `element: any` - Element data from click event
**Returns**: `Promise<void>`  
**Side Effects**: May trigger navigation or implementation modal  

## 🌐 SPAShell Core System

### Router System

```typescript
// Router initialization in SPAShell
const router = new Router();

// Default views
router.addView('dashboard', dashboardHtml || getDashboardContent(), 'Dashboard');
router.addView('analytics', getAnalyticsContent(), 'Analytics');
router.addView('settings', getSettingsContent(), 'Settings');
router.updateNavigation();
```

#### Router Methods
- **addView(id, content, title)**: Add a new view to the router
- **navigateToView(viewName)**: Navigate to a specific view
- **updateNavigation()**: Update navigation UI elements

### Component Registry

```typescript
// Component registry for modular widgets
const componentRegistry = new ComponentRegistry();
```

#### Registry Methods
- **register(name, component)**: Register a new component
- **get(name)**: Retrieve a registered component
- **reinitialize(container)**: Reinitialize components in container
- **clear()**: Clear all registered components

### Action Registry

```typescript
// Action registry for centralized event handling
const actionRegistry = new ActionRegistry();
```

#### Registry Methods
- **register(action, handler)**: Register an action handler
- **execute(action, target, params, element)**: Execute an action
- **clear()**: Clear all registered actions

### Patch Manager

```typescript
// Patch manager for diff-apply operations
const patchManager = new PatchManager();
```

#### Patch Manager Methods
- **applyLlmPatchToView(viewName, fragmentHtml, prompt, selector)**: Apply LLM patch to view
- **testPatchApplication()**: Test patch application functionality
- **simulateApiResponse()**: Simulate API response for testing

## 🎨 UI Components

### Mode Toggle Button

```typescript
<button
  onClick={toggleSPAMode}
  className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
    useSPAMode 
      ? 'bg-orange-500 text-white hover:bg-orange-600' 
      : 'bg-blue-500 text-white hover:bg-blue-600'
  }`}
  title={`Switch to ${useSPAMode ? 'Preview' : 'SPA'} mode`}
>
  {useSPAMode ? '🔄 SPA Mode' : '📱 Preview Mode'}
</button>
```

#### Props
- **onClick**: `() => void` - Click handler for mode toggle
- **className**: Dynamic classes based on current mode
- **title**: Tooltip text for accessibility

### Edit Mode Toggle Button

```typescript
{useSPAMode && (
  <button
    onClick={handleSPAEditModeToggle}
    className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
      spaEditMode 
        ? 'bg-red-500 text-white hover:bg-red-600' 
        : 'bg-green-500 text-white hover:bg-green-600'
    }`}
    title={`${spaEditMode ? 'Disable' : 'Enable'} SPA edit mode`}
  >
    {spaEditMode ? '✏️ Exit Edit' : '🔧 Edit Mode'}
  </button>
)}
```

#### Conditional Rendering
- Only visible when `useSPAMode` is `true`
- Visual state changes based on `spaEditMode`

### Integration Status Indicator

```typescript
<div className="absolute top-4 right-4 z-10">
  <div className="bg-green-100 border border-green-300 rounded-lg px-3 py-1.5 text-xs font-medium text-green-800">
    ✅ SPAShell Integrated
  </div>
</div>
```

#### Purpose
- Provides visual confirmation that SPAShell is successfully integrated
- Always visible to indicate system status

## 🔄 Event Handling

### Global Edit Handler (SPAShell)

```typescript
function globalEditHandler(e: MouseEvent) {
  const spaCore = (window as any).spaCore;
  if (!spaCore?.isEditMode) return;

  e.preventDefault();
  e.stopPropagation();

  const target = e.target as HTMLElement;
  const editable = target.closest('[data-editable], button, section, div') as HTMLElement | null;
  if (!editable) return;

  // Generate CSS selector and extract fragment
  const fragmentHtml = editable.outerHTML;
  const selector = generateSelector(editable);
  const prompt = `Edit the selected element: ${editable.textContent?.trim() || editable.tagName}`;

  // Apply LLM patch to view
  spaCore.patchManager.applyLlmPatchToView(viewName, fragmentHtml, prompt, selector);
}
```

#### Event Flow
1. User clicks element in SPAShell edit mode
2. Global edit handler captures click
3. Element selector is generated
4. Fragment HTML is extracted
5. LLM patch is applied to the view

### Message Handling

```typescript
// Listen for navigation messages from iframe
useEffect(() => {
  const handleMessage = (event: MessageEvent) => {
    if (event.data?.type === 'NAVIGATE_TO_PAGE') {
      const { pageId, pageName } = event.data;
      handleNavigationClick({
        textContent: pageName,
        isNavigation: true
      });
    }
  };

  window.addEventListener('message', handleMessage);
  return () => window.removeEventListener('message', handleMessage);
}, [state.pages]);
```

## 🛠️ Integration Points

### useEditorV3 Hook Integration

```typescript
// SPAShell integrates with existing useEditorV3 hook
const { state, actions, refs } = useEditorV3({ projectId });

// All existing functionality preserved:
// - state.htmlContent: Current HTML content
// - state.stableIframeContent: Stable iframe content
// - state.isGenerating: Generation status
// - state.streamingContent: Streaming content
// - actions.editContent(): Edit content function
// - actions.generateFromPrompt(): Generate function
// - actions.setSelectedElement(): Set selected element
// - actions.setShowImplementModal(): Show implementation modal
```

### PatchManager Integration

```typescript
// SPAShell uses the same PatchManager as useEditorV3
// for consistent diff-patch-apply functionality
const patchManager = new PatchManager();

// Integration with existing diff-apply system
if (patchManagerRef.current && diffData.shouldUseDiff) {
  const patchedHtml = await patchManagerRef.current.applyUnifiedDiff(
    currentHtml, 
    diffData.patches
  );
  setHtmlContent(patchedHtml);
}
```

## 🧪 Testing APIs

### Debug Functions

```typescript
// Exposed on window for debugging
(window as any).spaCore = spaCore.current;
(window as any).testPatchManager = () => {
  spaCore.current.patchManager.testPatchApplication();
};
(window as any).simulateApiResponse = () => {
  spaCore.current.patchManager.simulateApiResponse();
};
```

#### Available Debug Functions
- **window.spaCore**: Access to SPAShell core system
- **window.testPatchManager()**: Test patch manager functionality
- **window.simulateApiResponse()**: Simulate API response for testing

### Testing Utilities

```typescript
// Check if SPAShell is properly initialized
const isSPAShellReady = () => {
  return !!(window as any).spaCore && 
         !!(window as any).spaCore.router && 
         !!(window as any).spaCore.patchManager;
};

// Get current SPAShell mode
const getCurrentMode = () => {
  return useSPAMode ? 'SPAShell' : 'PreviewPanel';
};

// Get edit mode status
const getEditModeStatus = () => {
  return spaEditMode;
};
```

## 📊 Performance Considerations

### Lazy Loading
- SPAShell only initializes when activated (`useSPAMode = true`)
- Component registry loads components on demand
- Router views are created only when needed

### Memory Management
- Proper cleanup when switching modes
- Event listeners are removed on component unmount
- Global variables are cleaned up appropriately

### Event Optimization
- Efficient event delegation in both modes
- Debounced event handling where appropriate
- Minimal DOM manipulation for better performance

## 🔒 Security Considerations

### Iframe Sandboxing
```typescript
<iframe
  sandbox="allow-scripts allow-same-origin allow-modals allow-popups allow-forms"
  // ... other props
/>
```

### Event Validation
- All click events are validated before processing
- Element selectors are sanitized
- User input is properly escaped

### Global Variable Management
- SPAShell core is properly namespaced
- Debug functions are only available in development
- Cleanup prevents memory leaks

---

**Document Version:** 2.1  
**Last Updated:** December 2024  
**Next Review:** v2.2 (Next API additions)
