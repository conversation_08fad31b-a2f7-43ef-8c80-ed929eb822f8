import React from 'react';
import styles from '../../pages/EditorPage.module.css';

interface LoadingSpinnerProps {
  message?: string;
  subMessage?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  message = 'Generating preview...',
  subMessage = 'Please wait while we prepare your content'
}) => {
  return (
    <div className={styles.loadingContainer} style={{
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      zIndex: 10,
      backgroundColor: '#f9fafb', // Light background to ensure spinner is visible
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      {/* Pulse animation spinner */}
      <div className={styles.loadingPulse}>
        <div></div>
        <div></div>
      </div>

      <div style={{
        fontWeight: 600,
        fontSize: '18px',
        color: '#4f46e5',
        marginTop: '24px',
        letterSpacing: '0.5px'
      }}>
        {message}
      </div>
      <div style={{
        fontSize: '14px',
        marginTop: '8px',
        color: '#6b7280',
        maxWidth: '300px',
        textAlign: 'center',
        lineHeight: '1.5'
      }}>
        {subMessage}
      </div>
    </div>
  );
};
