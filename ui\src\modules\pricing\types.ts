/**
 * Represents a pricing plan feature
 */
export interface PlanFeature {
  /**
   * Feature name
   */
  name: string;

  /**
   * Optional feature description
   */
  description?: string;

  /**
   * Optional icon component or name
   */
  icon?: React.ReactNode | string;

  /**
   * Optional feature highlight flag
   */
  isHighlighted?: boolean;
}

/**
 * Represents a pricing plan
 */
export interface PricingPlan {
  /**
   * Unique plan identifier
   */
  id: string;

  /**
   * Plan name (e.g., "Free", "Pro", "Enterprise")
   */
  name: string;

  /**
   * Plan description
   */
  description?: string;

  /**
   * Monthly price in dollars
   */
  price: number;

  /**
   * Optional yearly price in dollars (if different from monthly * 12)
   */
  yearlyPrice?: number;

  /**
   * Optional monthly price when billed yearly
   */
  yearlyPricePerMonth?: number;

  /**
   * Optional currency code (default: USD)
   */
  currency?: string;

  /**
   * Array of plan features
   */
  features: PlanFeature[];

  /**
   * Optional badge text (e.g., "Most Popular", "Best Value")
   */
  badge?: string;

  /**
   * Optional flag to indicate if this is a featured/highlighted plan
   */
  isFeatured?: boolean;

  /**
   * Optional action button text (default: "Get Started")
   */
  buttonText?: string;

  /**
   * Optional URL for the checkout page
   */
  checkoutUrl?: string;

  /**
   * Optional custom action when selecting this plan
   */
  onSelect?: () => void;

  /**
   * Optional quota information
   */
  quota?: {
    /**
     * Maximum number of prototypes allowed
     */
    prototypeLimit: number;

    /**
     * Optional additional quota information
     */
    [key: string]: any;
  };

  /**
   * Optional metadata for additional plan information
   */
  metadata?: Record<string, any>;

  /**
   * Optional flag to disable the plan (e.g., for "Coming Soon" plans)
   */
  disabled?: boolean;
}

/**
 * Represents a FAQ item
 */
export interface FAQItem {
  /**
   * Question text
   */
  question: string;

  /**
   * Answer text (can include HTML)
   */
  answer: string;
}

/**
 * Represents a comparison feature
 */
export interface ComparisonFeature {
  /**
   * Feature name
   */
  name: string;

  /**
   * Optional feature description
   */
  description?: string;

  /**
   * Optional category for grouping features
   */
  category?: string;
}

/**
 * Represents the pricing module configuration
 */
export interface PricingConfig {
  /**
   * Array of pricing plans
   */
  plans: PricingPlan[];

  /**
   * Optional FAQ items
   */
  faqItems?: FAQItem[];

  /**
   * Optional comparison table configuration
   */
  comparison?: {
    /**
     * Features to compare
     */
    features: ComparisonFeature[];

    /**
     * Feature availability by plan
     */
    planFeatures: Record<string, Array<boolean | string>>;
  };

  /**
   * Optional yearly discount percentage
   */
  yearlyDiscountPercentage?: number;
}
