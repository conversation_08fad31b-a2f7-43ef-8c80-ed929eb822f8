const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

class VersionService {
  /**
   * Create a new version of a prototype
   * @param {number} prototypeId - The prototype ID
   * @param {string} html - The HTML content
   * @param {string} css - The CSS content (optional)
   * @param {string} changeDescription - Description of changes
   * @param {string} operationType - Type of operation ('generate', 'edit', 'implement', 'session_edit')
   * @param {string} userPrompt - The user's request
   * @param {string} llmResponse - The LLM's response
   * @returns {Promise<number>} The new version ID
   */
  async createVersion(prototypeId, html, css = null, changeDescription = null, operationType = 'edit', userPrompt = null, llmResponse = null) {
    try {
      const result = await pool.query(
        'SELECT create_prototype_version($1, $2, $3, $4, $5, $6, $7) as version_id',
        [prototypeId, html, css, changeDescription, operationType, userPrompt, llmResponse]
      );
      
      return result.rows[0].version_id;
    } catch (error) {
      console.error('Error creating prototype version:', error);
      throw error;
    }
  }

  /**
   * Get all versions for a prototype
   * @param {number} prototypeId - The prototype ID
   * @returns {Promise<Array>} Array of version metadata
   */
  async getVersions(prototypeId) {
    try {
      const result = await pool.query(
        'SELECT * FROM get_prototype_versions($1)',
        [prototypeId]
      );
      
      return result.rows;
    } catch (error) {
      console.error('Error getting prototype versions:', error);
      throw error;
    }
  }

  /**
   * Get a specific version of a prototype
   * @param {number} prototypeId - The prototype ID
   * @param {number} versionNumber - The version number
   * @returns {Promise<Object>} The version data
   */
  async getVersion(prototypeId, versionNumber) {
    try {
      const result = await pool.query(
        'SELECT * FROM get_prototype_version($1, $2)',
        [prototypeId, versionNumber]
      );
      
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting prototype version:', error);
      throw error;
    }
  }

  /**
   * Get the latest version number for a prototype
   * @param {number} prototypeId - The prototype ID
   * @returns {Promise<number>} The latest version number
   */
  async getLatestVersionNumber(prototypeId) {
    try {
      const result = await pool.query(
        'SELECT get_next_version_number($1) - 1 as latest_version',
        [prototypeId]
      );
      
      return result.rows[0].latest_version || 0;
    } catch (error) {
      console.error('Error getting latest version number:', error);
      throw error;
    }
  }

  /**
   * Get version history with pagination
   * @param {number} prototypeId - The prototype ID
   * @param {number} limit - Number of versions to return
   * @param {number} offset - Number of versions to skip
   * @returns {Promise<Array>} Array of version metadata
   */
  async getVersionHistory(prototypeId, limit = 10, offset = 0) {
    try {
      const result = await pool.query(`
        SELECT 
          id,
          version_number,
          change_description,
          operation_type,
          created_at
        FROM prototype_versions 
        WHERE prototype_id = $1 
        ORDER BY version_number DESC 
        LIMIT $2 OFFSET $3
      `, [prototypeId, limit, offset]);
      
      return result.rows;
    } catch (error) {
      console.error('Error getting version history:', error);
      throw error;
    }
  }

  /**
   * Delete old versions (keep only the latest N versions)
   * @param {number} prototypeId - The prototype ID
   * @param {number} keepCount - Number of latest versions to keep
   * @returns {Promise<number>} Number of versions deleted
   */
  async cleanupOldVersions(prototypeId, keepCount = 50) {
    try {
      const result = await pool.query(`
        DELETE FROM prototype_versions 
        WHERE prototype_id = $1 
        AND version_number < (
          SELECT version_number 
          FROM prototype_versions 
          WHERE prototype_id = $1 
          ORDER BY version_number DESC 
          LIMIT 1 OFFSET $2
        )
      `, [prototypeId, keepCount - 1]);
      
      return result.rowCount;
    } catch (error) {
      console.error('Error cleaning up old versions:', error);
      throw error;
    }
  }

  /**
   * Get version statistics for a prototype
   * @param {number} prototypeId - The prototype ID
   * @returns {Promise<Object>} Version statistics
   */
  async getVersionStats(prototypeId) {
    try {
      const result = await pool.query(`
        SELECT 
          COUNT(*) as total_versions,
          MIN(created_at) as first_version_date,
          MAX(created_at) as latest_version_date,
          COUNT(DISTINCT operation_type) as operation_types_used
        FROM prototype_versions 
        WHERE prototype_id = $1
      `, [prototypeId]);
      
      const operationStats = await pool.query(`
        SELECT 
          operation_type,
          COUNT(*) as count
        FROM prototype_versions 
        WHERE prototype_id = $1
        GROUP BY operation_type
        ORDER BY count DESC
      `, [prototypeId]);
      
      return {
        ...result.rows[0],
        operation_breakdown: operationStats.rows
      };
    } catch (error) {
      console.error('Error getting version stats:', error);
      throw error;
    }
  }
}

module.exports = new VersionService();
