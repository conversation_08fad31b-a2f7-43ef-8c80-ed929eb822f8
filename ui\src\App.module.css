/* ...existing styles... */

.trialBadge {
  background: #fff;
  color: #b45309;
  font-weight: 600;
  border-radius: 999px;
  padding: 0.25em 0.9em;
  font-size: 0.95em;
  margin-left: 0.5em;
  letter-spacing: 0.01em;
  border: 1px solid #fde68a;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  margin-right: 0.5em;
  box-shadow: 0 1px 6px 0 rgba(0,0,0,0.03);
}

.upgradeLink {
  color: #2563eb !important;
  background: #fff !important;
  border-radius: 999px;
  padding: 0.15em 0.7em;
  margin-left: 0.5em;
  font-weight: 600;
  text-decoration: underline;
  font-size: 0.95em;
  border: 1px solid #dbeafe;
  transition: background 0.15s, color 0.15s;
  display: inline-block;
}

.upgradeLink:hover {
  background: #dbeafe !important;
  color: #1e40af !important;
}

.authNav {
  display: flex;
  align-items: center;
  gap: 0.5em;
  background: #fffbe8;
  border-radius: 999px;
  padding: 0.25em 1em;
  box-shadow: 0 1px 6px 0 rgba(0,0,0,0.03);
}

.authUser {
  margin-right: 0.5em;
  color: #23272f;
  background: #fff;
  border-radius: 999px;
  padding: 0.15em 0.7em;
  font-weight: 600;
  font-size: 1em;
  letter-spacing: 0.01em;
  display: inline-block;
}

.nav {
  background: #2563eb;
  color: #fff;
  padding: 0.5rem 0;
  box-shadow: 0 2px 8px 0 rgba(37,99,235,0.08);
}

.navContent {
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.navLink {
  color: #fff;
  text-decoration: none;
  font-weight: 600;
  margin-right: 1.5rem;
  font-size: 1.1rem;
  transition: color 0.2s;
}

.navLink:hover {
  color: #dbeafe;
}

.navLogo {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 1.35rem;
  font-weight: 800;
  color: #fff;
  letter-spacing: -0.5px;
  text-shadow: 0 1px 8px rgba(37,99,235,0.10), 0 1px 1px rgba(0,0,0,0.04);
  background: linear-gradient(90deg, #fff 60%, #dbeafe 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.primaryCta {
  background: #2563eb;
  color: #fff;
  font-weight: 700;
  border-radius: 999px;
  padding: 0.5rem 1.5rem;
  font-size: 1rem;
  border: none;
  box-shadow: 0 2px 8px 0 rgba(37,99,235,0.08);
  transition: background 0.2s, color 0.2s;
  margin-left: 12px;
  display: flex;
  align-items: center;
}

.primaryCta:hover {
  background: #1e40af;
  color: #fff;
}

@media (max-width: 700px) {
  .navContent {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    padding: 0 0.5rem;
  }
  .navLink {
    margin-right: 1rem;
    font-size: 1rem;
  }
  .navLogo {
    margin-right: 1rem;
    font-size: 1.1rem;
  }
  .authNav {
    margin-top: 0.5rem;
    padding: 0.25em 0.7em;
  }
}
