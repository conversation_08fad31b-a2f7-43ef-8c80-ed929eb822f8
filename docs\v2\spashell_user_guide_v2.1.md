# SPAShell User Guide v2.1

**Version:** 2.1  
**Date:** December 2024  
**Audience:** End Users, Developers, QA Testers  
**Status:** Production Ready  

## 📋 Overview

This guide explains how to use the new SPAShell integration in the EditorPageV3Refactored component. The integration provides two powerful modes for prototype development while maintaining full backward compatibility.

## 🎯 What's New

### Hybrid Preview System
You now have **two modes** for viewing and editing your prototypes:

1. **📱 Preview Mode** (Default) - The familiar interface you know
2. **🔄 SPA Mode** (New) - Advanced Single Page Application capabilities

### Key Benefits
- ✅ **Zero Learning Curve**: Everything works exactly as before
- ✅ **Progressive Enhancement**: New features when you need them
- ✅ **Seamless Switching**: Switch modes without losing your work
- ✅ **Live Editing**: Click any element to edit it directly (SPA mode)

## 🚀 Getting Started

### Accessing the New Features

1. **Navigate to the Editor**: Go to `http://localhost:5173/editor-v3-refactored`
2. **Look for Mode Controls**: You'll see new buttons in the top-left corner of the preview area
3. **Integration Status**: A green "✅ SPAShell Integrated" indicator confirms the feature is active

### Visual Indicators

```
Preview Area:
┌─────────────────────────────────────────────────────┐
│ [📱 Preview Mode] [✅ SPAShell Integrated]          │
│                                                     │
│ Your prototype content appears here...              │
│                                                     │
└─────────────────────────────────────────────────────┘
```

## 📱 Preview Mode (Default)

### What It Is
This is the **original interface** you're familiar with. All existing functionality works exactly as before.

### Features Available
- ✅ **HTML Preview**: See your prototype in action
- ✅ **Code View**: Switch to see the generated HTML code
- ✅ **Element Selection**: Click elements to edit them
- ✅ **Chat Interface**: Use the chat to make changes
- ✅ **Diff-Patch-Apply**: Fast, efficient editing
- ✅ **Fragment Editing**: 70-80% token reduction
- ✅ **All Existing Features**: Everything you're used to

### How to Use
1. **Default Mode**: Preview Mode is active by default
2. **Generate Content**: Use the chat interface to create prototypes
3. **Edit Elements**: Click on elements to modify them
4. **Switch Views**: Toggle between Preview and Code views
5. **Resize Panels**: Adjust chat and pages panel widths

### When to Use Preview Mode
- ✅ **Familiar Workflow**: When you want the interface you know
- ✅ **Code Editing**: When you need to see/edit raw HTML
- ✅ **Simple Prototypes**: For straightforward single-page designs
- ✅ **Learning**: When getting familiar with the system

## 🔄 SPA Mode (Advanced)

### What It Is
SPA Mode transforms your prototype into a **full Single Page Application** with advanced navigation, live editing, and modular components.

### Features Available
- ✅ **Multi-View Navigation**: Dashboard → Analytics → Settings
- ✅ **Live Edit Mode**: Click any element to edit it directly
- ✅ **Component Registry**: Modular, reusable components
- ✅ **Action Registry**: Advanced interaction handling
- ✅ **Router System**: Real SPA navigation
- ✅ **All Chat Features**: Full integration with existing chat system

### How to Switch to SPA Mode

1. **Click the Mode Button**: Click "📱 Preview Mode" to switch
2. **Button Changes**: It becomes "🔄 SPA Mode" (orange)
3. **New Controls Appear**: Edit mode toggle becomes visible
4. **Content Transforms**: Your prototype becomes a full SPA

```
Before: [📱 Preview Mode]
After:  [🔄 SPA Mode] [🔧 Edit Mode]
```

### SPA Mode Interface

```
SPA Mode Interface:
┌─────────────────────────────────────────────────────┐
│ [🔄 SPA Mode] [🔧 Edit Mode] [✅ SPAShell Integrated]│
│                                                     │
│ ┌─ Navigation ─────────────────────────────────────┐ │
│ │ SPA Dashboard | Dashboard | Analytics | Settings │ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│ ┌─ Main Content ──────────────────────────────────┐ │
│ │ Your prototype content with SPA capabilities... │ │
│ │ • Click elements to edit (when edit mode on)   │ │
│ │ • Navigate between views                        │ │
│ │ • Advanced interactions                         │ │
│ └─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────┘
```

### Multi-View Navigation

In SPA Mode, your prototype has **three built-in views**:

1. **📊 Dashboard**: Main content area (your generated prototype)
2. **📈 Analytics**: Analytics view (placeholder for future features)
3. **⚙️ Settings**: Settings view (placeholder for future features)

**How to Navigate:**
- Click the navigation links in the SPA header
- Views switch instantly without page reload
- Your main content appears in the Dashboard view

### Live Edit Mode

#### Enabling Live Edit Mode
1. **Switch to SPA Mode**: Click "📱 Preview Mode" → "🔄 SPA Mode"
2. **Enable Edit Mode**: Click "🔧 Edit Mode"
3. **Button Changes**: Becomes "✏️ Exit Edit" (red)
4. **Ready to Edit**: Click any element to edit it directly

#### Using Live Edit Mode
1. **Click Any Element**: Buttons, text, sections, etc.
2. **Element Highlights**: Selected element gets a blue outline
3. **Automatic Processing**: System extracts element and generates edit prompt
4. **Chat Integration**: Edit request appears in chat interface
5. **Real-time Updates**: Changes apply immediately

```
Live Edit Flow:
Click Element → Highlight → Extract → Generate Prompt → Apply Changes
```

#### What You Can Edit
- ✅ **Buttons**: Add functionality, change text, modify styling
- ✅ **Text Content**: Headings, paragraphs, labels
- ✅ **Sections**: Entire content blocks
- ✅ **Navigation**: Menu items, links
- ✅ **Forms**: Input fields, form layouts
- ✅ **Any HTML Element**: Comprehensive editing support

### When to Use SPA Mode
- ✅ **Multi-Page Apps**: When building complex applications
- ✅ **Live Editing**: When you want click-to-edit functionality
- ✅ **Advanced Prototypes**: For sophisticated user interfaces
- ✅ **Real SPAs**: When you need actual SPA behavior
- ✅ **Component-Based Design**: For modular, reusable components

## 🔄 Switching Between Modes

### Seamless Mode Switching
You can switch between modes **at any time** without losing your work:

1. **Data Preservation**: All content, chat history, and settings are preserved
2. **Instant Switching**: Mode changes happen immediately
3. **No Reload Required**: Smooth transitions without page refresh
4. **Context Maintained**: Your place in the conversation is kept

### Mode Switch Examples

#### From Preview to SPA Mode
```
1. Working in Preview Mode with generated content
2. Click "📱 Preview Mode" button
3. Interface switches to "🔄 SPA Mode"
4. Content transforms into full SPA
5. All chat history and content preserved
```

#### From SPA to Preview Mode
```
1. Working in SPA Mode with live editing
2. Click "🔄 SPA Mode" button
3. Interface switches to "📱 Preview Mode"
4. Returns to familiar preview interface
5. All changes and content preserved
```

## 💬 Chat Integration

### Chat Works in Both Modes
The chat interface works **identically** in both modes:

- ✅ **Same Commands**: All chat commands work in both modes
- ✅ **Same Responses**: LLM responses are consistent
- ✅ **Same History**: Conversation history is shared
- ✅ **Same Features**: Element selection, implementation modals, etc.

### Mode-Specific Chat Behavior

#### Preview Mode Chat
- Edits update the preview iframe
- Code view shows raw HTML changes
- Traditional diff-patch-apply workflow

#### SPA Mode Chat
- Edits update the SPA views
- Changes reflect in the router system
- Live edit mode integrates with chat

### Chat + Live Edit Combination
In SPA Mode with Edit Mode enabled:

1. **Click Element**: Select element via live edit
2. **Chat Processes**: System generates edit prompt
3. **Make Changes**: Use chat to refine the edit
4. **See Results**: Changes apply to the SPA immediately

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### Mode Button Not Visible
**Problem**: Can't see the mode toggle button  
**Solution**: 
- Ensure you're on the correct page (`/editor-v3-refactored`)
- Check that content is loaded in the preview area
- Look in the top-left corner of the preview panel

#### SPA Mode Not Working
**Problem**: SPA Mode doesn't seem to activate  
**Solution**:
- Check for the green "✅ SPAShell Integrated" indicator
- Try refreshing the page
- Ensure JavaScript is enabled
- Check browser console for errors

#### Live Edit Mode Not Responding
**Problem**: Clicking elements doesn't trigger editing  
**Solution**:
- Ensure you're in SPA Mode (orange "🔄 SPA Mode" button)
- Enable Edit Mode (click "🔧 Edit Mode" → "✏️ Exit Edit")
- Try clicking different elements (buttons, text, sections)
- Check that the element highlights when clicked

#### Content Not Preserving During Mode Switch
**Problem**: Losing content when switching modes  
**Solution**:
- This should not happen - if it does, it's a bug
- Try switching back to the previous mode
- Check the chat history for your content
- Report the issue with steps to reproduce

### Debug Information

#### Checking Integration Status
Open browser console and run:
```javascript
// Check if SPAShell is properly integrated
console.log('SPA Core:', window.spaCore);
console.log('Test Patch Manager:', window.testPatchManager);
```

#### Testing Mode Switching
1. Generate some content in Preview Mode
2. Switch to SPA Mode
3. Verify content appears in Dashboard view
4. Switch back to Preview Mode
5. Verify content is preserved

## 📊 Feature Comparison Quick Reference

| Feature | Preview Mode | SPA Mode |
|---------|-------------|----------|
| **HTML Preview** | ✅ Yes | ✅ Yes |
| **Code View** | ✅ Yes | ❌ No |
| **Chat Interface** | ✅ Yes | ✅ Yes |
| **Element Selection** | ✅ Yes | ✅ Enhanced |
| **Live Editing** | ❌ No | ✅ Yes |
| **Multi-View Navigation** | ❌ No | ✅ Yes |
| **Component Registry** | ❌ No | ✅ Yes |
| **Router System** | ❌ No | ✅ Yes |
| **Diff-Patch-Apply** | ✅ Yes | ✅ Yes |
| **Fragment Editing** | ✅ Yes | ✅ Yes |

## 🎯 Best Practices

### When to Use Each Mode

#### Use Preview Mode For:
- 📝 **Content Creation**: Initial prototype generation
- 🔍 **Code Review**: When you need to see raw HTML
- 🎓 **Learning**: Getting familiar with the system
- 🚀 **Simple Prototypes**: Single-page designs
- 🔧 **Debugging**: When you need code-level access

#### Use SPA Mode For:
- 🏗️ **Complex Applications**: Multi-page prototypes
- ⚡ **Live Editing**: Quick element modifications
- 🎨 **Interactive Design**: Advanced user interfaces
- 🔄 **SPA Development**: Real single-page applications
- 🧩 **Component-Based Design**: Modular architectures

### Workflow Recommendations

#### Hybrid Workflow (Recommended)
1. **Start in Preview Mode**: Generate initial content
2. **Switch to SPA Mode**: For advanced editing and navigation
3. **Use Live Edit**: Make quick element changes
4. **Return to Preview**: For code review or major changes
5. **Iterate**: Switch modes as needed for different tasks

#### Power User Workflow
1. **Generate in Preview Mode**: Create base content
2. **Switch to SPA Mode**: Enable advanced features
3. **Enable Live Edit**: For rapid iteration
4. **Use Multi-Views**: Organize complex applications
5. **Leverage Chat**: Combine chat and live edit for efficiency

## 🔮 Future Features (Coming Soon)

### Planned Enhancements
- 🔄 **Mode Persistence**: Remember your preferred mode
- 🎨 **Custom Views**: Create your own SPA views
- 🧩 **Advanced Components**: Enhanced component library
- 📊 **Analytics Integration**: Real analytics in Analytics view
- ⚙️ **Settings Panel**: Functional settings management

### Feedback and Suggestions
We're continuously improving the SPAShell integration. Your feedback helps us prioritize new features and improvements.

---

**Document Version:** 2.1  
**Last Updated:** December 2024  
**Next Update:** v2.2 (Feature additions and improvements)
