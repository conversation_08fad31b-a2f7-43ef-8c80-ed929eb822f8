import React, { useState } from 'react';
import { Button } from './Button';
import { QuotaInfo } from '../services/planService';
import styles from './QuotaExceededModal.module.css';

interface QuotaExceededModalProps {
  open: boolean;
  onClose: () => void;
  onUpgrade: () => void;
  quota: QuotaInfo;
}

/**
 * Modal shown when a user has exceeded their prototype quota
 */
const QuotaExceededModal: React.FC<QuotaExceededModalProps> = ({
  open,
  onClose,
  onUpgrade,
  quota = {
    plan: 'free',
    totalCount: 3,
    usedCount: 3,
    remainingCount: 0
  }
}) => {
  const { plan, usedCount, totalCount, remainingCount } = quota;
  const percentUsed = Math.min(100, Math.round((usedCount / totalCount) * 100));
  const isPro = plan !== 'free';
  const [loading, setLoading] = useState(false);

  const handleUpgrade = () => {
    setLoading(true);
    // Optionally: window.analytics?.track("Upgrade Clicked");

    // Open payment page in a new tab
    window.open("https://justprototype.lemonsqueezy.com/buy/561b5e04-cb9f-4bee-9ed5-a99d8b6cc6fa", '_blank', 'noopener,noreferrer');

    // Also call the onUpgrade prop to close the modal
    if (onUpgrade) {
      onUpgrade();
    }
  };

  if (!open) return null;

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalContent}>
        <div className={styles.modalHeader}>
          <h2 className={styles.modalTitle}>
            Prototype Limit Reached
          </h2>
        </div>

        <div className={styles.modalBody}>
          <p className={styles.modalText}>
            {`You've reached your ${isPro ? 'pro' : 'free'} prototype limit. ${!isPro ? 'Upgrade to create more prototypes and unlock additional features.' : 'Contact support to increase your limit.'}`}
          </p>

          {/* Quota progress bar */}
          <div className={styles.progressContainer}>
            <div className={styles.progressLabels}>
              <span className={styles.progressLabel}>
                {usedCount} used
              </span>
              <span className={styles.progressLabel}>
                {totalCount} total
              </span>
            </div>
            <div className={styles.progressBar}>
              <div
                className={`${styles.progressFill} ${remainingCount <= 0 ? styles.progressFillExceeded : ''}`}
                style={{ width: `${percentUsed}%` }}
              />
            </div>
          </div>

          {!isPro && (
            <div className={styles.benefitsBox}>
              <h3 className={styles.benefitsTitle}>
                Pro Plan Benefits
              </h3>
              <p className={styles.benefitItem}>
                • Up to 50 prototypes (vs. 3 on free plan)
              </p>
              <p className={styles.benefitItem}>
                • Advanced design options
              </p>
              <p className={styles.benefitItem}>
                • Export to code
              </p>
              <p className={styles.benefitItem}>
                • Priority support
              </p>
            </div>
          )}
        </div>

        <div className={styles.modalFooter}>
          <Button
            onClick={onClose}
            variant="secondary"
            size="medium"
          >
            Maybe Later
          </Button>
          {!isPro && (
            <Button
              onClick={handleUpgrade}
              variant="primary"
              size="medium"
              disabled={loading}
            >
              {loading ? "Redirecting..." : "Upgrade to Pro"}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuotaExceededModal;
