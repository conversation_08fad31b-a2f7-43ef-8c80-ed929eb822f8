import { useState, useRef, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  TextField,
  Button,
  Paper,
  IconButton,
  AppBar,
  Toolbar,
  Menu,
  MenuItem,
  CircularProgress
} from '@mui/material';
import {
  Send as SendIcon,
  GitHub as GitHubIcon,
  AccountCircle as AccountCircleIcon,
  Publish as PublishIcon,
  TouchApp as TouchAppIcon,
  ContentCopy as CopyIcon,
  Code as CodeIcon
} from '@mui/icons-material';
import { llmService } from '../services/llmService';
import DebugWindow from './DebugWindow';

function PrototypeWorkspace({ prototypeName, initialHtml, initialPrompt, initialMessages }) {
  // Use provided messages if available, otherwise create default messages
  const [messages, setMessages] = useState(initialMessages || [
    {
      type: 'system',
      content: 'Exciting things ahead...',
      timestamp: new Date()
    },
    {
      type: 'user',
      content: initialPrompt,
      timestamp: new Date(Date.now() - 60000)
    },
    {
      type: 'assistant',
      content: 'Here\'s what I\'ve created based on your request:',
      html: initialHtml,
      timestamp: new Date(Date.now() - 30000)
    }
  ]);

  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedElement, setSelectedElement] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [currentHtml, setCurrentHtml] = useState(initialHtml);
  const [debugMessages, setDebugMessages] = useState([]);
  const [showDebugWindow, setShowDebugWindow] = useState(false);

  const iframeRef = useRef(null);
  const messagesEndRef = useRef(null);

  // Scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // State for version history
  const [versionHistory, setVersionHistory] = useState([]);
  const [currentVersion, setCurrentVersion] = useState(0);

  // Update current HTML when messages change
  useEffect(() => {
    const htmlMessages = messages.filter(m => m.html);
    if (htmlMessages.length > 0) {
      // Update version history
      setVersionHistory(htmlMessages.map(m => m.html));

      // Set to latest version by default
      const latestVersion = htmlMessages.length - 1;
      setCurrentVersion(latestVersion);

      // Get the HTML content
      const htmlContent = htmlMessages[latestVersion].html;

      // Log the HTML content for debugging
      console.log('Setting HTML content, length:', htmlContent.length);
      console.log('HTML content preview:', htmlContent.substring(0, 100) + '...');

      // Make sure the HTML has proper structure
      let processedHtml = htmlContent;
      if (!htmlContent.includes('<html')) {
        processedHtml = `<!DOCTYPE html><html><head><meta charset="UTF-8"></head><body>${htmlContent}</body></html>`;
      }

      // Set current HTML to the selected version
      setCurrentHtml(processedHtml);
    }
  }, [messages]);

  // Directly write HTML to iframe when currentHtml changes
  useEffect(() => {
    if (iframeRef.current && currentHtml) {
      try {
        console.log('Writing HTML directly to iframe');
        console.log('HTML content length:', currentHtml.length);
        console.log('HTML content preview:', currentHtml.substring(0, 100) + '...');

        // Make sure we have a valid HTML document
        let processedHtml = currentHtml;

        // Check if HTML is truncated and fix it
        if (currentHtml && !currentHtml.includes('</html>') && currentHtml.includes('<html')) {
          console.warn('HTML appears to be truncated, adding closing tags');
          // Add closing tags to make it valid HTML
          processedHtml += '\n</style>\n</head>\n<body>\n<div>Content was truncated</div>\n</body>\n</html>';
        }

        // If it's not a complete HTML document, wrap it
        if (!currentHtml.includes('<!DOCTYPE') && !currentHtml.includes('<html')) {
          processedHtml = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Prototype Preview</title>
</head>
<body>
  ${currentHtml}
</body>
</html>`;
        }

        // Get the iframe document
        const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow.document;

        // Clear the document first
        iframeDoc.open();
        iframeDoc.write(processedHtml);
        iframeDoc.close();

        // Add event listener to handle when iframe is fully loaded
        const handleLoad = () => {
          console.log('Iframe content fully loaded');

          // Add any additional scripts or functionality here if needed

          // Remove the event listener to avoid memory leaks
          iframeRef.current.removeEventListener('load', handleLoad);
        };

        // Add the load event listener
        iframeRef.current.addEventListener('load', handleLoad);

        console.log('HTML written to iframe successfully');
      } catch (error) {
        console.error('Error writing HTML to iframe:', error);
      }
    }
  }, [currentHtml]);

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!prompt.trim()) return;

    // Add user message
    setMessages(prev => [...prev, {
      type: 'user',
      content: prompt,
      timestamp: new Date()
    }]);

    // Start generating response
    setIsGenerating(true);

    // Show debug window
    setShowDebugWindow(true);

    // Clear previous debug messages
    setDebugMessages([{
      type: 'info',
      content: 'Starting HTML generation...',
      timestamp: new Date()
    }]);

    try {
      // Call the LLM service to generate updated HTML
      // We'll create a specialized prompt for incremental updates
      const currentHtml = messages.filter(m => m.html).length > 0
        ? messages[messages.filter(m => m.html).length - 1].html
        : initialHtml;

      const enhancedPrompt = `
CURRENT HTML:
\`\`\`html
${currentHtml}
\`\`\`

ORIGINAL REQUEST: ${initialPrompt}

FOLLOW-UP REQUEST: ${prompt}

IMPORTANT INSTRUCTIONS:
1. Make ONLY the specific changes requested in the follow-up request
2. Preserve the overall structure, styling, and content of the existing HTML EXACTLY as it is
3. Do NOT change any colors, backgrounds, or styles unless explicitly requested
4. Do NOT add or remove elements unless explicitly requested
5. Do NOT regenerate the entire page - only modify the specific parts mentioned
6. Return the COMPLETE HTML with your minimal changes
7. If you need to add new elements, match the existing style patterns
8. Preserve all CSS classes and styling unless specifically asked to change them

Return ONLY the modified HTML with no additional explanation.`;

      // Add debug message
      setDebugMessages(prev => [...prev, {
        type: 'info',
        content: 'Sending request to LLM service...',
        timestamp: new Date()
      }]);

      // Call the LLM service with streaming updates
      const result = await llmService.streamGenerateHTML(
        enhancedPrompt,
        null,
        (progress) => {
          // Add debug message for each progress update
          setDebugMessages(prev => [...prev, {
            type: progress.status === 'error' ? 'error' :
                  progress.status === 'complete' ? 'success' : 'info',
            content: progress.message || 'Processing...',
            timestamp: new Date()
          }]);
        }
      );

      // Add debug message
      setDebugMessages(prev => [...prev, {
        type: 'success',
        content: 'HTML generation complete!',
        timestamp: new Date()
      }]);

      // Check if the response contains a design plan
      if (result.type === 'plan_and_implementation') {
        // Add the design plan message
        setMessages(prev => [...prev, {
          type: 'assistant',
          content: '## Design Plan\n\n' + result.designPlan,
          isPlan: true,
          timestamp: new Date()
        }]);

        // Add the HTML implementation message
        setMessages(prev => [...prev, {
          type: 'assistant',
          content: 'I\'ve updated the design based on your feedback:',
          html: result.html,
          timestamp: new Date()
        }]);
      } else {
        // Add assistant message with just the generated HTML
        setMessages(prev => [...prev, {
          type: 'assistant',
          content: 'I\'ve updated the design based on your feedback:',
          html: result.html || result,
          timestamp: new Date()
        }]);
      }
    } catch (error) {
      console.error('Error generating updated HTML:', error);

      // Add debug message
      setDebugMessages(prev => [...prev, {
        type: 'error',
        content: `Error: ${error.message}`,
        timestamp: new Date()
      }]);

      // Add error message
      setMessages(prev => [...prev, {
        type: 'assistant',
        content: `Sorry, I encountered an error: ${error.message}`,
        timestamp: new Date()
      }]);
    } finally {
      setIsGenerating(false);
      setPrompt('');
    }
  };

  const handleElementSelect = () => {
    // Toggle element selection mode
    const newSelectionMode = !selectedElement;
    setSelectedElement(newSelectionMode);

    // Access the iframe's contentWindow to enable element selection
    const iframe = iframeRef.current;
    if (!iframe) return;

    const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;

    if (newSelectionMode) {
      // Add selection styles to the iframe
      const styleElement = iframeDocument.createElement('style');
      styleElement.id = 'selection-styles';
      styleElement.textContent = `
        .element-highlight {
          outline: 2px dashed rgba(139, 92, 246, 0.5) !important;
          cursor: pointer !important;
        }
        .element-selected {
          outline: 3px solid rgba(139, 92, 246, 0.8) !important;
          box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.2) !important;
        }
      `;
      iframeDocument.head.appendChild(styleElement);

      // Add hover effect to all elements
      const allElements = iframeDocument.querySelectorAll('body *');
      allElements.forEach(element => {
        element.addEventListener('mouseover', () => {
          element.classList.add('element-highlight');
        });

        element.addEventListener('mouseout', () => {
          element.classList.remove('element-highlight');
        });

        element.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();

          // Remove previous selection
          const previousSelected = iframeDocument.querySelector('.element-selected');
          if (previousSelected) {
            previousSelected.classList.remove('element-selected');
          }

          // Add new selection
          element.classList.add('element-selected');

          // Get element info for prompt suggestion
          const tagName = element.tagName.toLowerCase();
          const elementClasses = Array.from(element.classList)
            .filter(cls => cls !== 'element-highlight' && cls !== 'element-selected')
            .join('.');
          const elementId = element.id ? `#${element.id}` : '';

          // Set prompt with element selection
          setPrompt(`Update the ${tagName}${elementId ? ` with ID "${elementId.substring(1)}"` : ''}${elementClasses ? ` with classes "${elementClasses}"` : ''} to have the following changes: `);
        });
      });
    } else {
      // Remove selection styles
      const styleElement = iframeDocument.getElementById('selection-styles');
      if (styleElement) {
        styleElement.remove();
      }

      // Remove event listeners (simplified - in a real implementation you would properly remove them)
      const allElements = iframeDocument.querySelectorAll('body *');
      allElements.forEach(element => {
        element.classList.remove('element-highlight');
        element.classList.remove('element-selected');
      });
    }
  };

  // Handle version change
  const handleVersionChange = (version) => {
    if (version >= 0 && version < versionHistory.length) {
      setCurrentVersion(version);

      // Get the HTML content
      const htmlContent = versionHistory[version];

      // Log the HTML content for debugging
      console.log('Changing to version', version);
      console.log('HTML content length:', htmlContent.length);
      console.log('HTML content preview:', htmlContent.substring(0, 100) + '...');

      // Make sure the HTML has proper structure
      let processedHtml = htmlContent;
      if (!htmlContent.includes('<html')) {
        processedHtml = `<!DOCTYPE html><html><head><meta charset="UTF-8"></head><body>${htmlContent}</body></html>`;
      }

      // Set current HTML to the selected version
      setCurrentHtml(processedHtml);

      // Directly write to iframe for immediate update
      if (iframeRef.current) {
        try {
          console.log('Writing version HTML directly to iframe');

          // Check if HTML is truncated and fix it
          if (processedHtml && !processedHtml.includes('</html>') && processedHtml.includes('<html')) {
            console.warn('Version HTML appears to be truncated, adding closing tags');
            // Add closing tags to make it valid HTML
            processedHtml += '\n</style>\n</head>\n<body>\n<div>Content was truncated</div>\n</body>\n</html>';
          }

          // If it's not a complete HTML document, wrap it
          if (!processedHtml.includes('<!DOCTYPE') && !processedHtml.includes('<html')) {
            processedHtml = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Prototype Preview</title>
</head>
<body>
  ${processedHtml}
</body>
</html>`;
          }

          const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow.document;

          // Clear the document first
          iframeDoc.open();
          iframeDoc.write(processedHtml);
          iframeDoc.close();

          console.log('Version HTML written to iframe successfully');
        } catch (error) {
          console.error('Error writing version HTML to iframe:', error);
        }
      }
    }
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100vh' }}>
      {/* Debug Window */}
      {showDebugWindow && (
        <DebugWindow
          messages={debugMessages}
          onClose={() => setShowDebugWindow(false)}
        />
      )}

      {/* Header */}
      <AppBar position="static" color="default" elevation={1} sx={{ backgroundColor: 'white' }}>
        <Toolbar sx={{ minHeight: '64px !important' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box
              sx={{
                width: 32,
                height: 32,
                borderRadius: '8px',
                backgroundColor: '#8B5CF6',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 1
              }}
            >
              <Typography variant="subtitle1" fontWeight={700} sx={{ color: 'white' }}>
                J
              </Typography>
            </Box>
            <Typography variant="subtitle1" fontWeight={600} sx={{ color: '#1F2937' }}>
              {prototypeName || 'New Prototype'}
            </Typography>
          </Box>

          <Box sx={{ flexGrow: 1 }} />

          <Button
            variant="contained"
            startIcon={<PublishIcon />}
            sx={{
              backgroundColor: '#8B5CF6',
              '&:hover': {
                backgroundColor: '#7C3AED',
              },
              borderRadius: 2,
              px: 2,
              py: 0.75,
              fontWeight: 600,
              mr: 1
            }}
          >
            Publish
          </Button>

          <IconButton
            size="medium"
            sx={{ color: '#4B5563' }}
            onClick={() => setShowDebugWindow(!showDebugWindow)}
            title="Toggle Debug Window"
          >
            <CodeIcon />
          </IconButton>

          <IconButton size="medium" sx={{ color: '#4B5563', ml: 1 }}>
            <GitHubIcon />
          </IconButton>

          <IconButton
            size="medium"
            sx={{ color: '#4B5563', ml: 1 }}
            onClick={handleMenuOpen}
          >
            <AccountCircleIcon />
          </IconButton>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            transformOrigin={{ horizontal: 'right', vertical: 'top' }}
            anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          >
            <MenuItem onClick={handleMenuClose}>Profile</MenuItem>
            <MenuItem onClick={handleMenuClose}>Settings</MenuItem>
            <MenuItem onClick={handleMenuClose}>Logout</MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Box sx={{ display: 'flex', flexGrow: 1, overflow: 'hidden' }}>
        {/* Preview Panel */}
        <Box
          sx={{
            flexGrow: 1,
            p: 2,
            backgroundColor: '#F3F4F6',
            display: 'flex',
            flexDirection: 'column'
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
            <Paper
              elevation={2}
              sx={{
                flexGrow: 1,
                borderRadius: 2,
                overflow: 'hidden',
                display: 'flex',
                flexDirection: 'column',
                mb: 1
              }}
            >
              {isGenerating && (
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    zIndex: 10
                  }}
                >
                  <CircularProgress size={40} sx={{ color: '#8B5CF6', mb: 2 }} />
                  <Typography variant="body2" sx={{ color: '#4B5563', fontWeight: 500 }}>
                    Generating your prototype...
                  </Typography>
                </Box>
              )}

              <iframe
                ref={iframeRef}
                title="Prototype Preview"
                style={{
                  border: 'none',
                  width: '100%',
                  height: '100%',
                  display: 'block',
                  backgroundColor: 'white'
                }}
              />
            </Paper>

            {/* Version History Navigation */}
            {versionHistory.length > 1 && (
              <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                gap: 1,
                py: 1.5,
                px: 2,
                backgroundColor: 'white',
                borderRadius: '8px',
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                mt: 1
              }}>
                <Typography variant="caption" sx={{
                  color: '#4B5563',
                  fontWeight: 500,
                  mr: 1
                }}>
                  Version History:
                </Typography>

                <IconButton
                  size="small"
                  disabled={currentVersion === 0}
                  onClick={() => handleVersionChange(currentVersion - 1)}
                  sx={{
                    color: currentVersion === 0 ? '#D1D5DB' : '#6B7280',
                    backgroundColor: '#F9FAFB',
                    border: '1px solid #E5E7EB',
                    borderRadius: '4px',
                    p: 0.5,
                    '&:hover': {
                      backgroundColor: '#F3F4F6',
                      borderColor: '#D1D5DB',
                    }
                  }}
                >
                  <Typography variant="caption" sx={{ fontWeight: 600 }}>←</Typography>
                </IconButton>

                <Box sx={{
                  display: 'flex',
                  gap: 0.5,
                  flexWrap: 'wrap',
                  justifyContent: 'center'
                }}>
                  {versionHistory.map((_, index) => (
                    <Button
                      key={index}
                      variant={currentVersion === index ? "contained" : "outlined"}
                      size="small"
                      onClick={() => handleVersionChange(index)}
                      sx={{
                        minWidth: '36px',
                        height: '28px',
                        px: 1,
                        borderRadius: '4px',
                        backgroundColor: currentVersion === index ? '#8B5CF6' : 'transparent',
                        borderColor: currentVersion === index ? '#8B5CF6' : '#E5E7EB',
                        color: currentVersion === index ? 'white' : '#6B7280',
                        '&:hover': {
                          backgroundColor: currentVersion === index ? '#7C3AED' : '#F9FAFB',
                          borderColor: currentVersion === index ? '#7C3AED' : '#D1D5DB',
                        },
                        fontWeight: 600,
                        fontSize: '0.75rem',
                        lineHeight: 1
                      }}
                    >
                      V{index + 1}
                    </Button>
                  ))}
                </Box>

                <IconButton
                  size="small"
                  disabled={currentVersion === versionHistory.length - 1}
                  onClick={() => handleVersionChange(currentVersion + 1)}
                  sx={{
                    color: currentVersion === versionHistory.length - 1 ? '#D1D5DB' : '#6B7280',
                    backgroundColor: '#F9FAFB',
                    border: '1px solid #E5E7EB',
                    borderRadius: '4px',
                    p: 0.5,
                    '&:hover': {
                      backgroundColor: '#F3F4F6',
                      borderColor: '#D1D5DB',
                    }
                  }}
                >
                  <Typography variant="caption" sx={{ fontWeight: 600 }}>→</Typography>
                </IconButton>
              </Box>
            )}
          </Box>
        </Box>

        {/* Conversation Panel */}
        <Box
          sx={{
            width: 420, // Increased width for better readability
            borderLeft: '1px solid #E5E7EB',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: 'white'
          }}
        >
          <Box
            sx={{
              flexGrow: 1,
              overflowY: 'auto',
              p: 2,
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            {messages.map((message, index) => (
              <Box
                key={index}
                sx={{
                  mb: 3,
                  alignSelf: message.type === 'user' ? 'flex-end' : 'flex-start',
                  maxWidth: '85%'
                }}
              >
                {message.type === 'system' && (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100%',
                      mb: 2
                    }}
                  >
                    <Box
                      sx={{
                        backgroundColor: '#F3F4F6',
                        borderRadius: 2,
                        py: 1,
                        px: 2,
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      <Box
                        sx={{
                          width: 24,
                          height: 24,
                          borderRadius: '8px',
                          backgroundColor: '#8B5CF6',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mr: 1
                        }}
                      >
                        <Typography variant="caption" fontWeight={700} sx={{ color: 'white' }}>
                          J
                        </Typography>
                      </Box>
                      <Typography variant="body2" sx={{ color: '#4B5563' }}>
                        {message.content}
                      </Typography>
                    </Box>
                  </Box>
                )}

                {message.type === 'user' && (
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: '#8B5CF6',
                    }}
                  >
                    <Typography variant="body2" sx={{ color: 'white' }}>
                      {message.content}
                    </Typography>
                  </Paper>
                )}

                {message.type === 'assistant' && (
                  <Box>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 2,
                        borderRadius: 2,
                        backgroundColor: message.isPlan ? '#F5F7FF' : '#F3F4F6',
                        mb: message.html ? 1 : 0,
                        border: message.isPlan ? '1px solid #E0E7FF' : 'none',
                        boxShadow: message.isPlan ? '0 2px 4px rgba(79, 70, 229, 0.05)' : 'none'
                      }}
                    >
                      {message.isPlan ? (
                        <Box>
                          <Typography variant="subtitle2" sx={{ color: '#4F46E5', fontWeight: 600, mb: 1.5, display: 'flex', alignItems: 'center' }}>
                            <span style={{
                              display: 'inline-block',
                              width: '12px',
                              height: '12px',
                              borderRadius: '3px',
                              backgroundColor: '#4F46E5',
                              marginRight: '8px'
                            }}></span>
                            Design Plan
                          </Typography>

                          <Box sx={{
                            color: '#1F2937',
                            pl: 1.5,
                            pr: 1,
                            borderLeft: '2px solid #E0E7FF',
                            fontSize: '0.875rem',
                            maxHeight: '300px',
                            overflowY: 'auto',
                            '&::-webkit-scrollbar': {
                              width: '6px',
                            },
                            '&::-webkit-scrollbar-thumb': {
                              backgroundColor: '#E0E7FF',
                              borderRadius: '3px',
                            }
                          }}>
                            <Typography
                              variant="body2"
                              component="div"
                              sx={{
                                whiteSpace: 'pre-wrap',
                                fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                                fontSize: '0.875rem',
                                '& h1, & h2, & h3': {
                                  fontSize: '1rem',
                                  fontWeight: 600,
                                  color: '#4F46E5',
                                  marginTop: '12px',
                                  marginBottom: '8px'
                                },
                                '& ul, & ol': {
                                  paddingLeft: '20px',
                                  marginTop: '8px',
                                  marginBottom: '8px'
                                },
                                '& li': {
                                  marginBottom: '4px'
                                }
                              }}
                              dangerouslySetInnerHTML={{
                                __html: message.content
                                  .replace(/^## (.*$)/gm, '<h2>$1</h2>')
                                  .replace(/^# (.*$)/gm, '<h1>$1</h1>')
                                  .replace(/^\- (.*$)/gm, '<li>$1</li>')
                                  .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
                                  .replace(/<\/ul>\s*<ul>/g, '')
                              }}
                            />
                          </Box>
                        </Box>
                      ) : (
                        <Typography variant="body2" sx={{ color: '#1F2937' }}>
                          {message.content}
                        </Typography>
                      )}
                    </Paper>

                    {message.html && (
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          mt: 1
                        }}
                      >
                        <Button
                          size="small"
                          startIcon={<CopyIcon fontSize="small" />}
                          onClick={() => {
                            navigator.clipboard.writeText(message.html);
                            // Could add a toast notification here
                          }}
                          sx={{
                            color: '#6B7280',
                            textTransform: 'none',
                            '&:hover': {
                              backgroundColor: 'rgba(107, 114, 128, 0.1)'
                            }
                          }}
                        >
                          Copy HTML
                        </Button>

                        <Button
                          size="small"
                          startIcon={<CodeIcon fontSize="small" />}
                          onClick={() => {
                            // Open a new window with the HTML code
                            const newWindow = window.open('', '_blank');
                            // Create HTML content
                            const htmlContent = `
                              <html>
                                <head>
                                  <title>HTML Code</title>
                                  <style>
                                    body {
                                      background-color: #1E1E38;
                                      color: #E5E7EB;
                                      font-family: monospace;
                                      padding: 20px;
                                      margin: 0;
                                    }
                                    pre {
                                      white-space: pre-wrap;
                                      word-wrap: break-word;
                                      background-color: #2A2A4A;
                                      padding: 20px;
                                      border-radius: 8px;
                                      overflow: auto;
                                    }
                                  </style>
                                </head>
                                <body>
                                  <pre>${message.html.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
                                </body>
                              </html>
                            `;

                            // Use modern approach to write to document
                            newWindow.document.open();
                            // Using innerHTML instead of document.write to avoid deprecation warning
                            newWindow.document.documentElement.innerHTML = htmlContent;
                            newWindow.document.close();
                          }}
                          sx={{
                            color: '#6B7280',
                            textTransform: 'none',
                            '&:hover': {
                              backgroundColor: 'rgba(107, 114, 128, 0.1)'
                            }
                          }}
                        >
                          View Code
                        </Button>
                      </Box>
                    )}
                  </Box>
                )}
              </Box>
            ))}
            <div ref={messagesEndRef} />
          </Box>

          {/* Divider removed */}

          <Box sx={{ p: 2, backgroundColor: '#F9FAFB' }}>
            <form onSubmit={handleSubmit}>
              <Box sx={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                zIndex: 10
              }}>
                <Button
                  size="small"
                  startIcon={<TouchAppIcon fontSize="small" />}
                  onClick={handleElementSelect}
                  variant={selectedElement ? "contained" : "outlined"}
                  sx={{
                    color: selectedElement ? 'white' : '#8B5CF6',
                    backgroundColor: selectedElement ? '#8B5CF6' : 'rgba(255, 255, 255, 0.9)',
                    textTransform: 'none',
                    borderColor: selectedElement ? '#8B5CF6' : '#E5E7EB',
                    borderRadius: '6px',
                    px: 1.5,
                    py: 0.5,
                    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                    '&:hover': {
                      backgroundColor: selectedElement ? '#7C3AED' : 'rgba(139, 92, 246, 0.1)',
                      borderColor: '#8B5CF6'
                    }
                  }}
                >
                  {selectedElement ? 'Exit Selection Mode' : 'Select Element'}
                </Button>
              </Box>

              <Box sx={{
                display: 'flex',
                p: 1,
                backgroundColor: 'white',
                borderRadius: '12px',
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
              }}>
                <TextField
                  fullWidth
                  placeholder="Type your message..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  disabled={isGenerating}
                  variant="standard" // Changed to standard to remove the outline
                  size="small"
                  sx={{
                    mx: 1,
                    '& .MuiInputBase-root': {
                      fontSize: '0.95rem',
                    },
                    '& .MuiInputBase-input': {
                      color: '#1F2937',
                      padding: '8px 4px',
                      '&::placeholder': {
                        color: '#9CA3AF',
                        opacity: 1,
                      }
                    },
                    '& .MuiInput-underline:before': {
                      borderBottom: 'none', // Remove the underline
                    },
                    '& .MuiInput-underline:hover:before': {
                      borderBottom: 'none', // Remove the underline on hover
                    },
                    '& .MuiInput-underline:after': {
                      borderBottom: 'none', // Remove the underline when focused
                    }
                  }}
                />

                <IconButton
                  type="submit"
                  disabled={isGenerating || !prompt.trim()}
                  sx={{
                    backgroundColor: '#8B5CF6',
                    color: 'white',
                    '&:hover': {
                      backgroundColor: '#7C3AED',
                    },
                    '&.Mui-disabled': {
                      backgroundColor: '#E5E7EB',
                      color: '#9CA3AF'
                    },
                    width: '40px',
                    height: '40px'
                  }}
                >
                  {isGenerating ? (
                    <CircularProgress size={20} sx={{ color: 'white' }} />
                  ) : (
                    <SendIcon fontSize="small" />
                  )}
                </IconButton>
              </Box>
            </form>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}

export default PrototypeWorkspace;
