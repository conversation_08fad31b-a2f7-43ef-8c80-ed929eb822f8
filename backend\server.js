require('dotenv').config();
const express = require('express');
const cors = require('cors');
const session = require('express-session');
const passport = require('passport');
const bodyParser = require('body-parser');
const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const Pool = require('pg').Pool;
const connectPgSimple = require('connect-pg-simple');

const app = express();
const port = process.env.PORT || 5000;

// Database setup
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// CORS configuration
const allowedOrigins = [
  process.env.CLIENT_URL || 'http://localhost:3000',
  'http://localhost:5173', // Vite dev server
  'http://localhost:5174', // Common Vite alternative port
  'http://localhost:8080', // Common dev server port
  'http://127.0.0.1:5173', // Alternative localhost
  'http://127.0.0.1:5174',
  'http://127.0.0.1:8080',
  'http://localhost:5000',
  'https://app.justprototype.dev'
];

// Log allowed origins for debugging
console.log('Allowed CORS origins:', allowedOrigins);

const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) {
      console.log('No origin header present, allowing request');
      return callback(null, true);
    }

    // Normalize origin by removing trailing slashes
    const normalizedOrigin = origin.endsWith('/') ? origin.slice(0, -1) : origin;

    // Check if origin is in allowedOrigins (case-insensitive)
    const isAllowed = allowedOrigins.some(allowedOrigin =>
      normalizedOrigin.toLowerCase() === allowedOrigin.toLowerCase()
    );

    if (!isAllowed) {
      const msg = `The CORS policy for this site does not allow access from the specified Origin: ${origin}. Allowed origins: ${allowedOrigins.join(', ')}`;
      console.warn(msg);
      return callback(new Error(msg), false);
    }

    console.log(`Allowed CORS request from origin: ${origin}`);
    return callback(null, true);
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
  optionsSuccessStatus: 200
};

// Apply CORS middleware
app.use(cors(corsOptions));

// Handle preflight requests
app.options('*', cors(corsOptions));

// Session configuration
const PgSession = connectPgSimple(session);
app.use(session({
  store: new PgSession({
    pool,
    tableName: 'session'
  }),
  secret: process.env.SESSION_SECRET || 'your-session-secret',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days
  }
}));

// Passport initialization
app.use(passport.initialize());
app.use(passport.session());

// Body parser middleware
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'JustPrototype API',
      version: '2.0.0',
      description: 'API documentation for JustPrototype'
    },
    components: {
      schemas: {
        UINode: require('./schemas/uiNode.schema.json')
      }
    }
  },
  apis: [
    './routes/*.js',
    './controllers/*.js',
    './schemas/*.json'
  ]
};

const swaggerDocs = swaggerJsdoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));

// Routes
app.use('/api/llm/v2', require('./routes/llmV2'));
app.use('/api/llm/v3', require('./routes/llmV3'));
app.use('/api/schema', require('./routes/schema'));
app.use('/api/prototype', require('./routes/prototype'));
app.use('/api/page_gen', require('./routes/pageGen'));
app.use('/api/auth', require('./routes/auth'));
app.use('/api/share', require('./routes/shareRoutes'));
app.use('/api/ast', require('./routes/ast'));
app.use('/api/sessions', require('./routes/sessions'));
app.use('/api/intent', require('./routes/intent'));
app.use('/api/diff-test', require('./routes/diffTest'));

// Authentication check endpoint
app.get('/api/auth/check', (req, res) => {
  res.json({
    authenticated: req.isAuthenticated(),
    user: req.user
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: process.env.NODE_ENV === 'production' ? 'Internal Server Error' : err.message
  });
});

// Start server
app.listen(port, () => {
  console.log(`Server running on port ${port}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Client URL: ${process.env.CLIENT_URL || 'http://localhost:3000'}`);
});

module.exports = app;
