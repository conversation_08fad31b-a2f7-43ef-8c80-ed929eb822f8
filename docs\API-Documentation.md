# API Documentation - LiteLLM Integration

## Overview

This document describes the API endpoints and their behavior with the new LiteLLM integration, following the Readdy.ai two-step approach.

## Base Configuration

### Environment Variables
```bash
# LiteLLM Configuration
LITELLM_BASE_URL=http://localhost:4000
LITELLM_API_KEY=sk-1234

# Provider API Keys
DEEPSEEK_API_KEY=your_deepseek_key
OPENROUTER_API_KEY=your_openrouter_key
OPENAI_API_KEY=your_openai_key (optional)
ANTHROPIC_API_KEY=your_anthropic_key (optional)
```

### Provider Selection Logic
The system automatically selects the best available provider:
1. **LiteLLM** (if configured) - Primary choice
2. **OpenAI** (if API key available) - Secondary
3. **Anthropic** (if API key available) - Tertiary

## API Endpoints

### 1. Generate Intent (Step 1)

**Endpoint:** `POST /api/llm/v3/generate-intent`

**Purpose:** Analyze user element clicks and generate contextual understanding (like Readdy.ai)

**Request:**
```json
{
  "elementCode": "<button class=\"btn-primary\">Contact Us</button>",
  "htmlContent": "<!DOCTYPE html>...",
  "conversationHistory": [
    {
      "role": "user",
      "content": "Create a landing page",
      "timestamp": "2024-01-01T00:00:00Z"
    }
  ]
}
```

**Response:**
```json
{
  "code": "OK",
  "data": {
    "canGenerate": true,
    "userIntent": "The user clicked the 'Contact Us' button in the header section. This suggests they want to implement a contact form or modal with fields for name, email, and message.",
    "suggestion": "When the user clicks the 'Contact Us' button, a contact modal should appear with a form containing name, email, message fields, and a submit button. Include form validation and success/error states."
  },
  "meta": {
    "time": 1704067200000,
    "request_id": "intent_1704067200000",
    "message": "",
    "detail": null
  }
}
```

**Model Used:** `deepseek/deepseek-reasoner` (via LiteLLM)

### 2. Edit HTML (Step 2)

**Endpoint:** `POST /api/llm/v3/edit`

**Purpose:** Implement the functionality based on intent analysis

**Request:**
```json
{
  "htmlContent": "<!DOCTYPE html>...",
  "prompt": "Implement 'Contact Us' as a modal",
  "conversationHistory": [
    {
      "role": "user",
      "content": "Create a landing page",
      "timestamp": "2024-01-01T00:00:00Z"
    },
    {
      "role": "assistant",
      "content": "The user clicked the 'Contact Us' button...",
      "timestamp": "2024-01-01T00:01:00Z"
    }
  ],
  "elementSelector": null
}
```

**Response:** Server-Sent Events (SSE)
```
event: context
data: User clicked on Contact Us. They likely want to implement a contact form...

event: data
data: <!DOCTYPE html>
data: <html>
data: <head>
data: ...

event: done
data: [DONE]
```

**Model Used:** `deepseek/deepseek-coder-v2` (via LiteLLM)

### 3. Generate HTML

**Endpoint:** `POST /api/llm/v3/generate`

**Purpose:** Generate complete HTML from scratch

**Request:**
```json
{
  "prompt": "Create a modern landing page for a SaaS product with hero section, features, and pricing"
}
```

**Response:** Server-Sent Events (SSE)
```
event: data
data: <!DOCTYPE html>
data: <html lang="en">
data: <head>
data: ...

event: done
data: [DONE]
```

**Model Used:** `deepseek/deepseek-coder-v2` (via LiteLLM)

### 4. Generate Plan

**Endpoint:** `POST /api/llm/v3/plan`

**Purpose:** Create structured implementation plan

**Request:**
```json
{
  "prompt": "Create a dashboard for project management",
  "deviceType": "desktop"
}
```

**Response:**
```json
{
  "plan": {
    "overview": "A comprehensive project management dashboard...",
    "features": [
      "Project overview cards",
      "Task management interface",
      "Team collaboration tools"
    ],
    "implementation": {
      "layout": "Grid-based responsive layout...",
      "components": ["Header", "Sidebar", "Main Content"],
      "styling": "Modern design with clean typography..."
    },
    "accessibility": [
      "ARIA labels for all interactive elements",
      "Keyboard navigation support"
    ]
  }
}
```

**Model Used:** `qwen/qwen-2.5-72b-instruct` (via LiteLLM)

## Model Selection by Task

### Intent Analysis
- **Primary:** `deepseek/deepseek-reasoner`
- **Fallback:** `gpt-4o`
- **Purpose:** Deep reasoning for understanding user intent

### Planning
- **Primary:** `qwen/qwen-2.5-72b-instruct`
- **Fallback:** `gpt-4o`
- **Purpose:** Large context window for complex planning

### Code Generation
- **Primary:** `deepseek/deepseek-coder-v2`
- **Fallback:** `gpt-4o`
- **Purpose:** Specialized coding capabilities

### Context Analysis
- **Primary:** `qwen/qwen-2.5-32b-instruct`
- **Fallback:** `gpt-4o`
- **Purpose:** Balanced speed and understanding

## Error Handling

### Provider Unavailable
```json
{
  "error": "No LLM provider configured. Please set up LiteLLM, OpenAI, or Anthropic.",
  "code": "PROVIDER_ERROR",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### Model Not Found
```json
{
  "error": "No model mapping found for provider litellm and task intent-analysis",
  "code": "MODEL_ERROR",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### LiteLLM Proxy Error
```json
{
  "error": "LiteLLM proxy connection failed",
  "code": "PROXY_ERROR",
  "details": "Connection refused to http://localhost:4000",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Authentication

All endpoints require session authentication:
```javascript
if (!req.session) {
  return res.status(401).json({ error: 'Authentication required' });
}
```

## Rate Limiting

Rate limiting is handled at the LiteLLM proxy level:
- Configurable per model
- Budget controls available
- Automatic throttling

## Monitoring

### Request Logging
```javascript
console.log(`🤖 Using ${providerKey} provider with model ${modelName} for task: ${taskType}`);
```

### Performance Metrics
- Response time by provider
- Model usage statistics
- Error rates and fallback triggers

## Testing

### Health Check
```bash
curl http://localhost:4000/health
```

### Model Test
```bash
curl -X POST http://localhost:4000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-1234" \
  -d '{
    "model": "deepseek/deepseek-chat",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

### Intent Generation Test
```bash
curl -X POST http://localhost:3000/api/llm/v3/generate-intent \
  -H "Content-Type: application/json" \
  -H "Cookie: session_cookie_here" \
  -d '{
    "elementCode": "<button>Test</button>",
    "htmlContent": "<html><body><button>Test</button></body></html>",
    "conversationHistory": []
  }'
```

## Migration Notes

### From Previous Implementation
- Provider parameter removed from all endpoints
- Automatic provider selection implemented
- No frontend changes required
- Backward compatible with existing calls

### Configuration Changes
- Add LiteLLM environment variables
- Start LiteLLM proxy service
- Existing OpenAI/Anthropic keys still work as fallbacks

## Performance Considerations

### Response Times
- **DeepSeek models:** ~2-5 seconds
- **Qwen models:** ~3-7 seconds
- **Streaming:** Immediate start, progressive delivery

### Cost Optimization
- Automatic model selection based on task complexity
- Fallback to more expensive models only when necessary
- Usage tracking and budget controls via LiteLLM

## Security

### API Key Protection
- Environment variable storage only
- No API keys exposed in responses
- Proxy-based architecture isolates credentials

### Request Validation
- Input sanitization
- Content-type validation
- Session-based authentication

## Troubleshooting

### Common Issues
1. **LiteLLM proxy not responding:** Check Docker container status
2. **Model errors:** Verify API keys and model names
3. **Slow responses:** Check network connectivity and model load

### Debug Mode
Enable verbose logging:
```bash
LITELLM_LOG=DEBUG docker-compose -f docker-compose.litellm.yml up
```
