import { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Tabs,
  Tab,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Close as CloseIcon,
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Info as InfoIcon,
} from '@mui/icons-material';

// Design templates
const DESIGN_TEMPLATES = [
  {
    id: 'landing-page',
    name: 'Landing Page',
    description: 'A modern landing page with hero section, features, and CTA.',
    image: 'https://placehold.co/600x400/1E1E38/FFFFFF?text=Landing+Page',
    category: 'website',
    prompt: 'Create a modern landing page with a hero section, features list with icons, testimonials, pricing section, and a footer with contact information. Use a clean, professional design with a color scheme that conveys trust and innovation.',
  },
  {
    id: 'ecommerce',
    name: 'E-Commerce',
    description: 'Product listing page with shopping cart functionality.',
    image: 'https://placehold.co/600x400/1E1E38/FFFFFF?text=E-Commerce',
    category: 'website',
    prompt: 'Create an e-commerce product listing page with a navigation bar, search functionality, product grid with images, prices and ratings, filtering options, and an add-to-cart button for each product. Include a mini cart preview in the header.',
  },
  {
    id: 'dashboard',
    name: 'Dashboard',
    description: 'Admin dashboard with charts, tables and stats.',
    image: 'https://placehold.co/600x400/1E1E38/FFFFFF?text=Dashboard',
    category: 'webapp',
    prompt: 'Create an admin dashboard with a sidebar navigation, header with user profile, main content area with statistics cards, line and bar charts for data visualization, and a recent activity table. Use a dark theme with accent colors for important metrics.',
  },
  {
    id: 'blog',
    name: 'Blog',
    description: 'Blog layout with featured posts and categories.',
    image: 'https://placehold.co/600x400/1E1E38/FFFFFF?text=Blog',
    category: 'website',
    prompt: 'Create a blog homepage with a header, featured post section with large image, grid of recent posts with thumbnails, categories sidebar, newsletter subscription form, and footer. Use a clean, readable typography and subtle color accents.',
  },
  {
    id: 'portfolio',
    name: 'Portfolio',
    description: 'Showcase your work with this portfolio template.',
    image: 'https://placehold.co/600x400/1E1E38/FFFFFF?text=Portfolio',
    category: 'website',
    prompt: 'Create a portfolio website with a hero section featuring a professional photo and brief introduction, a projects section with filterable gallery, about me section with skills and experience, testimonials from clients, and a contact form. Use a minimal design that puts focus on the work samples.',
  },
  {
    id: 'mobile-app',
    name: 'Mobile App',
    description: 'Mobile app interface with navigation and content.',
    image: 'https://placehold.co/600x400/1E1E38/FFFFFF?text=Mobile+App',
    category: 'mobile',
    prompt: 'Create a mobile app interface with a bottom navigation bar, header with search functionality, main feed with card-based content, user profile section, and notification center. Design it for a social media application with a modern, vibrant color scheme.',
  },
  {
    id: 'saas',
    name: 'SaaS Platform',
    description: 'Software as a Service platform interface.',
    image: 'https://placehold.co/600x400/1E1E38/FFFFFF?text=SaaS',
    category: 'webapp',
    prompt: 'Create a SaaS platform dashboard with a top navigation bar, sidebar with menu items, main content area with welcome message, key metrics in cards, recent activity feed, and quick action buttons. Use a professional color scheme with brand accent colors.',
  },
  {
    id: 'restaurant',
    name: 'Restaurant',
    description: 'Restaurant website with menu and reservation.',
    image: 'https://placehold.co/600x400/1E1E38/FFFFFF?text=Restaurant',
    category: 'website',
    prompt: 'Create a restaurant website with a full-width hero image, about section with the restaurant\'s story, menu section with food categories and prices, chef profiles, reservation form, location with map, and footer with opening hours. Use warm colors and appetizing food imagery.',
  },
];

// Categories
const CATEGORIES = [
  { id: 'all', label: 'All Designs' },
  { id: 'website', label: 'Websites' },
  { id: 'webapp', label: 'Web Apps' },
  { id: 'mobile', label: 'Mobile' },
];

function DesignSelector({ open, onClose, onSelectDesign }) {
  const [category, setCategory] = useState('all');
  const [favorites, setFavorites] = useState([]);
  const [selectedDesign, setSelectedDesign] = useState(null);
  const [previewOpen, setPreviewOpen] = useState(false);

  const handleCategoryChange = (event, newValue) => {
    setCategory(newValue);
  };

  const handleFavoriteToggle = (designId) => {
    if (favorites.includes(designId)) {
      setFavorites(favorites.filter(id => id !== designId));
    } else {
      setFavorites([...favorites, designId]);
    }
  };

  const handleDesignSelect = (design) => {
    onSelectDesign(design.prompt);
    onClose();
  };

  const handlePreview = (design) => {
    setSelectedDesign(design);
    setPreviewOpen(true);
  };

  const filteredDesigns = DESIGN_TEMPLATES.filter(
    design => category === 'all' || design.category === category
  );

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="lg"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
            backgroundColor: '#1A1A2E',
          }
        }}
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          borderBottom: '1px solid rgba(163, 163, 182, 0.2)',
          pb: 1
        }}>
          <Typography variant="h6">Select a Design Template</Typography>
          <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs 
              value={category} 
              onChange={handleCategoryChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': {
                  minWidth: 100,
                  py: 1.5,
                }
              }}
            >
              {CATEGORIES.map((cat) => (
                <Tab 
                  key={cat.id} 
                  label={cat.label} 
                  value={cat.id} 
                />
              ))}
            </Tabs>
          </Box>
          
          <Grid container spacing={3}>
            {filteredDesigns.map((design) => (
              <Grid item xs={12} sm={6} md={4} key={design.id}>
                <Card 
                  sx={{ 
                    height: '100%', 
                    display: 'flex', 
                    flexDirection: 'column',
                    backgroundColor: '#252542',
                    borderRadius: 2,
                    transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 12px 20px rgba(0, 0, 0, 0.2)',
                    }
                  }}
                >
                  <Box sx={{ position: 'relative' }}>
                    <CardMedia
                      component="img"
                      height="160"
                      image={design.image}
                      alt={design.name}
                      sx={{ borderTopLeftRadius: 8, borderTopRightRadius: 8 }}
                    />
                    <Box sx={{ 
                      position: 'absolute', 
                      top: 8, 
                      right: 8, 
                      display: 'flex', 
                      gap: 1 
                    }}>
                      <Tooltip title="Preview">
                        <IconButton 
                          size="small" 
                          sx={{ 
                            backgroundColor: 'rgba(0, 0, 0, 0.5)', 
                            color: 'white',
                            '&:hover': {
                              backgroundColor: 'rgba(0, 0, 0, 0.7)',
                            }
                          }}
                          onClick={() => handlePreview(design)}
                        >
                          <InfoIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title={favorites.includes(design.id) ? "Remove from favorites" : "Add to favorites"}>
                        <IconButton 
                          size="small" 
                          sx={{ 
                            backgroundColor: 'rgba(0, 0, 0, 0.5)', 
                            color: favorites.includes(design.id) ? '#F472B6' : 'white',
                            '&:hover': {
                              backgroundColor: 'rgba(0, 0, 0, 0.7)',
                            }
                          }}
                          onClick={() => handleFavoriteToggle(design.id)}
                        >
                          {favorites.includes(design.id) ? 
                            <FavoriteIcon fontSize="small" /> : 
                            <FavoriteBorderIcon fontSize="small" />
                          }
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>
                  <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                    <Box sx={{ mb: 1, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="subtitle1" component="div" fontWeight={600}>
                        {design.name}
                      </Typography>
                      <Chip 
                        label={design.category} 
                        size="small" 
                        sx={{ 
                          height: 20, 
                          fontSize: '0.7rem',
                          backgroundColor: 'rgba(168, 85, 247, 0.2)',
                          color: '#C084FC',
                        }} 
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flexGrow: 1 }}>
                      {design.description}
                    </Typography>
                    <Button 
                      variant="contained" 
                      fullWidth
                      onClick={() => handleDesignSelect(design)}
                      sx={{ mt: 'auto' }}
                    >
                      Use Template
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(163, 163, 182, 0.2)' }}>
          <Button onClick={onClose} color="inherit">
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Design Preview Dialog */}
      {selectedDesign && (
        <Dialog
          open={previewOpen}
          onClose={() => setPreviewOpen(false)}
          maxWidth="md"
          fullWidth
          sx={{
            '& .MuiDialog-paper': {
              borderRadius: 2,
              backgroundColor: '#1A1A2E',
            }
          }}
        >
          <DialogTitle sx={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            borderBottom: '1px solid rgba(163, 163, 182, 0.2)',
            pb: 1
          }}>
            <Typography variant="h6">{selectedDesign.name}</Typography>
            <IconButton edge="end" color="inherit" onClick={() => setPreviewOpen(false)}>
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          
          <DialogContent sx={{ pt: 3 }}>
            <Box sx={{ mb: 3 }}>
              <img 
                src={selectedDesign.image} 
                alt={selectedDesign.name} 
                style={{ 
                  width: '100%', 
                  borderRadius: 8,
                  marginBottom: 16
                }} 
              />
              
              <Typography variant="subtitle1" gutterBottom>
                Description
              </Typography>
              <Typography variant="body2" paragraph>
                {selectedDesign.description}
              </Typography>
              
              <Typography variant="subtitle1" gutterBottom>
                Prompt
              </Typography>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  backgroundColor: '#252542',
                  borderRadius: 2,
                  border: '1px solid rgba(163, 163, 182, 0.2)',
                }}
              >
                <Typography variant="body2">
                  {selectedDesign.prompt}
                </Typography>
              </Paper>
            </Box>
          </DialogContent>
          
          <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid rgba(163, 163, 182, 0.2)' }}>
            <Button 
              onClick={() => setPreviewOpen(false)} 
              color="inherit"
            >
              Cancel
            </Button>
            <Button 
              variant="contained" 
              onClick={() => {
                handleDesignSelect(selectedDesign);
                setPreviewOpen(false);
              }}
            >
              Use Template
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </>
  );
}

export default DesignSelector;
