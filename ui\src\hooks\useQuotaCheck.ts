import { useState, useEffect } from 'react';
import { getPrototypeQuota, QuotaInfo } from '../services/planService';

interface UseQuotaCheckResult {
  quota: QuotaInfo;
  isLoading: boolean;
  isQuotaExceeded: boolean;
  error: Error | null;
  refetchQuota: () => Promise<void>;
}

/**
 * Hook to check if a user has exceeded their quota
 * @returns {UseQuotaCheckResult} - The quota check result
 */
export function useQuotaCheck(): UseQuotaCheckResult {
  const [quota, setQuota] = useState<QuotaInfo>({
    plan: 'free',
    totalCount: 3,
    usedCount: 0,
    remainingCount: 3
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchQuota = async () => {
    try {
      setIsLoading(true);
      const quotaData = await getPrototypeQuota();
      setQuota(quotaData);
      setError(null);
    } catch (err: any) {
      console.error('Error fetching quota:', err);
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchQuota();
  }, []);

  // Check if quota is exceeded
  const isQuotaExceeded = quota.remainingCount <= 0 || quota.usedCount >= quota.totalCount;

  return {
    quota,
    isLoading,
    isQuotaExceeded,
    error,
    refetchQuota: fetchQuota
  };
}

export default useQuotaCheck;
