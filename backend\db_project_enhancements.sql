-- Database enhancements for project management functionality
-- This file contains additional indexes and optimizations for the project list feature

-- Add index for better performance on project listing with pagination
CREATE INDEX IF NOT EXISTS idx_prototypes_user_created_desc ON prototypes(user_id, created_at DESC);

-- Add index for project search by title (if needed in future)
CREATE INDEX IF NOT EXISTS idx_prototypes_title ON prototypes(title);

-- Add index for project status filtering (if status column is added in future)
-- CREATE INDEX IF NOT EXISTS idx_prototypes_status ON prototypes(status);

-- Function to get user project statistics (for dashboard/analytics)
CREATE OR REPLACE FUNCTION get_user_project_stats(user_id_param INTEGER)
RETURNS TABLE(
  total_projects INTEGER,
  projects_this_month INTEGER,
  last_project_date TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_projects,
    COUNT(CASE WHEN created_at >= date_trunc('month', CURRENT_DATE) THEN 1 END)::INTEGER as projects_this_month,
    MAX(created_at) as last_project_date
  FROM prototypes 
  WHERE prototypes.user_id = user_id_param;
END;
$$ LANGUAGE plpgsql;

-- Sample data for testing (uncomment if needed for development)
/*
-- Insert sample projects for testing
INSERT INTO prototypes (user_id, title, description, html, css) VALUES
(1, 'Sample Landing Page', 'A modern landing page for a SaaS product', '<div>Sample HTML</div>', 'body { margin: 0; }'),
(1, 'E-commerce Store', 'Online store with product catalog', '<div>Store HTML</div>', 'body { font-family: Arial; }'),
(1, 'Portfolio Website', 'Personal portfolio with project gallery', '<div>Portfolio HTML</div>', 'body { background: #f5f5f5; }');
*/

-- View for project list with additional metadata
CREATE OR REPLACE VIEW project_list_view AS
SELECT 
  p.id,
  p.user_id,
  p.title,
  p.description,
  p.created_at,
  p.updated_at,
  p.preview_image_url,
  'active' as status,
  'prototype' as type,
  LENGTH(p.html) as html_size,
  CASE 
    WHEN p.updated_at > p.created_at THEN true 
    ELSE false 
  END as has_been_modified
FROM prototypes p;

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT ON project_list_view TO your_app_user;
-- GRANT EXECUTE ON FUNCTION get_user_project_stats(INTEGER) TO your_app_user;
