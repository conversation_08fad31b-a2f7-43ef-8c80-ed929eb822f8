# LiteLLM Environment Variables Template
# Copy this to .env and fill in your actual API keys

# Primary Provider - DeepSeek (Cost Effective)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Secondary Provider - OpenRouter (for Qwen models)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Fallback Providers
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# LiteLLM Configuration
LITELLM_MASTER_KEY=sk-your-secure-master-key-here
LITELLM_LOG=INFO
PORT=4000

# Optional: Database URL for persistent storage
# DATABASE_URL=postgresql://user:password@host:port/database
