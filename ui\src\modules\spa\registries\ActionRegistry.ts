// ActionRegistry.ts
// SPA Action Registry - Handles all data-action triggers in the SPA

export class ActionRegistry {
  private actions: Record<string, Function>;

  constructor() {
    this.actions = {};
    this.registerDefaultActions();
  }

  registerDefaultActions() {
    // Example: openModal
    this.actions['openModal'] = (targetId: string, params: any, el: HTMLElement) => {
      // Prevent modal opening in edit mode
      if (window && (window as any).spaCore?.isEditMode) {
        // Optionally, show a tooltip or toast: "Cannot open modal in edit mode"
        return;
      }
      const modal = document.getElementById(targetId);
      if (modal) {
        modal.classList.remove('hidden');
      }
    };

    // ...register other default actions here...
  }

  execute(action: string, target: string | null, params: any, el: HTMLElement) {
    if (this.actions[action]) {
      this.actions[action](target, params, el);
    }
  }

  // Allow registering custom actions
  register(action: string, handler: Function) {
    this.actions[action] = handler;
  }

  clear() {
    this.actions = {};
  }
}

export default ActionRegistry;
