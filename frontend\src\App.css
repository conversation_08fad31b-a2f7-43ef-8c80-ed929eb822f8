/* Custom styles for JustProtoType - GenAI Hub style */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@400;500;600&display=swap');

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  letter-spacing: -0.01em;
  background-color: #13131F !important;
  color: #FFFFFF;
  margin: 0;
  padding: 0;
  font-size: 16px;
}

#root {
  width: 100%;
  min-height: 100vh;
}

/* Custom scrollbar - these will override MUI theme settings */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-track {
  background: #1F2937;
}

::-webkit-scrollbar-thumb {
  background: #4B5563;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6B7280;
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(90deg, #C084FC 0%, #A855F7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  text-shadow: 0 0 20px rgba(192, 132, 252, 0.3);
}

/* Smooth transitions */
.MuiButton-root,
.MuiPaper-root,
.MuiTab-root,
.MuiChip-root,
.MuiIconButton-root {
  transition: all 0.15s ease-in-out !important;
}

/* Code highlighting */
pre {
  font-family: 'Fira Code', 'Roboto Mono', 'Courier New', monospace;
  line-height: 1.5;
  background-color: #1F2937;
  border-radius: 4px;
  padding: 16px;
  margin: 0;
  font-size: 14px;
  overflow-x: auto;
}

/* Header styles */
.app-header {
  background-color: #1E1E38;
  border-bottom: 1px solid rgba(163, 163, 182, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Button styles - these will override MUI theme settings */
.MuiButton-containedPrimary {
  background-color: #A855F7 !important;
  color: white !important;
  font-weight: 600 !important;
  border-radius: 6px !important;
  text-transform: none !important;
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3) !important;
  letter-spacing: -0.01em !important;
  font-size: 1rem !important;
  padding: 10px 20px !important;
}

.MuiButton-containedPrimary:hover {
  background-color: #9333EA !important;
  box-shadow: 0 6px 16px rgba(168, 85, 247, 0.5) !important;
  transform: translateY(-2px);
}

.MuiButton-outlinedPrimary {
  border-color: rgba(168, 85, 247, 0.5) !important;
  color: #C084FC !important;
  font-weight: 600 !important;
  border-radius: 6px !important;
  text-transform: none !important;
  letter-spacing: -0.01em !important;
  font-size: 1rem !important;
  padding: 10px 20px !important;
}

.MuiButton-outlinedPrimary:hover {
  background-color: rgba(168, 85, 247, 0.08) !important;
  border-color: #A855F7 !important;
  box-shadow: 0 2px 8px rgba(168, 85, 247, 0.2) !important;
}

/* Paper styles - these will override MUI theme settings */
.MuiPaper-root {
  background-color: #1E1E38 !important;
  border-color: rgba(163, 163, 182, 0.2) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2) !important;
}

/* Input styles - these will override MUI theme settings */
.MuiOutlinedInput-root {
  background-color: #2A2A4A !important;
  border-radius: 6px !important;
  font-size: 1rem !important;
}

.MuiOutlinedInput-input {
  padding: 14px 16px !important;
  font-size: 1rem !important;
}

.MuiOutlinedInput-notchedOutline {
  border-color: rgba(163, 163, 182, 0.4) !important;
}

.MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
  border-color: rgba(168, 85, 247, 0.5) !important;
}

.MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline {
  border-color: #A855F7 !important;
  border-width: 2px !important;
  box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.2) !important;
}

/* Typography styles - these will override MUI theme settings */
.MuiTypography-root {
  color: #FFFFFF !important;
}

.MuiTypography-h5 {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  letter-spacing: -0.025em !important;
}

.MuiTypography-h6 {
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  letter-spacing: -0.025em !important;
}

.MuiTypography-subtitle1 {
  font-size: 1.125rem !important;
  font-weight: 600 !important;
  letter-spacing: -0.015em !important;
}

.MuiTypography-body1 {
  font-size: 1rem !important;
  letter-spacing: -0.01em !important;
}

.MuiTypography-body2 {
  font-size: 0.95rem !important;
  letter-spacing: -0.01em !important;
}

.MuiTypography-colorTextSecondary {
  color: #D4D4D8 !important;
}

/* Chip styles - these will override MUI theme settings */
.MuiChip-root {
  background-color: #2A2A4A !important;
  color: #FFFFFF !important;
  font-weight: 500 !important;
  border-radius: 6px !important;
  height: 32px !important;
  font-size: 0.9rem !important;
  padding: 0 12px !important;
}

.MuiChip-label {
  padding: 0 8px !important;
}

.MuiChip-outlinedPrimary {
  border-color: rgba(168, 85, 247, 0.5) !important;
  color: #C084FC !important;
}

/* Tab styles - these will override MUI theme settings */
.MuiTab-root {
  color: #D4D4D8 !important;
  text-transform: none !important;
  font-weight: 600 !important;
  min-height: 48px !important;
  padding: 12px 20px !important;
  font-size: 1rem !important;
}

.MuiTab-root.Mui-selected {
  color: #C084FC !important;
  font-weight: 700 !important;
}

.MuiTabs-indicator {
  background-color: #A855F7 !important;
  height: 3px !important;
  border-radius: 3px 3px 0 0 !important;
}

/* Divider styles - these will override MUI theme settings */
.MuiDivider-root {
  background-color: rgba(163, 163, 182, 0.2) !important;
}

/* List styles */
.MuiListItem-root {
  padding: 16px 20px !important;
  font-size: 1rem !important;
}

/* Icon button styles */
.MuiIconButton-root {
  color: #D4D4D8 !important;
  padding: 10px !important;
}

.MuiIconButton-root:hover {
  background-color: rgba(168, 85, 247, 0.1) !important;
  transform: scale(1.05);
}

/* Tooltip styles */
.MuiTooltip-tooltip {
  background-color: #2A2A4A !important;
  border: 1px solid rgba(163, 163, 182, 0.2) !important;
  font-size: 0.875rem !important;
  padding: 8px 12px !important;
  font-weight: 500 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Focus visible styles */
.MuiFocusVisible {
  outline: 2px solid #A855F7 !important;
  outline-offset: 2px !important;
}

/* Card hover effect */
.card-hover:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px -5px rgba(0, 0, 0, 0.2), 0 12px 12px -5px rgba(0, 0, 0, 0.1);
}

/* Specific component overrides */
.prompt-input-container .MuiOutlinedInput-root {
  background-color: #2A2A4A !important;
}

.code-preview-container {
  background-color: #2A2A4A !important;
  border-radius: 8px !important;
  padding: 20px !important;
  font-size: 1rem !important;
}

/* Code block styling */
pre {
  font-family: 'Fira Code', 'Roboto Mono', 'Courier New', monospace;
  line-height: 1.6;
  background-color: #2A2A4A;
  border-radius: 8px;
  padding: 20px;
  margin: 0;
  font-size: 1rem;
  overflow-x: auto;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Ensure dark mode is enforced */
.MuiCssBaseline-root {
  background-color: #13131F !important;
}
